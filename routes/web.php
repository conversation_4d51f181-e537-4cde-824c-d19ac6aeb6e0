<?php

use App\Http\Controllers\CalendarController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::get('calendar', [CalendarController::class, 'index'])->middleware(['auth', 'verified'])->name('calendar');

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
