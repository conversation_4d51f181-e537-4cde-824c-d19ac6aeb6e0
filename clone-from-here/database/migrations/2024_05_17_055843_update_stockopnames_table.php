<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStockopnamesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('stockopname_products', 'product_code')) {
            Schema::table('stockopname_products', function (Blueprint $table) {
                $table->string('product_code')->nullable(false)->change();
                $table->integer('start_balance')->nullable()->change();
                $table->integer('quantity_in')->nullable()->change();
                $table->integer('quantity_out')->nullable()->change();
                $table->integer('last_balance')->nullable()->change();
            });
        }

        if (!Schema::hasColumn('stockopname_products', 'is_checked')) {
            Schema::table('stockopname_products', function (Blueprint $table) {
                $table->boolean('is_checked')->default(0);
            });
        }

        if (!Schema::hasColumn('stockopnames', 'total_qty_product_out')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                // $table->renameColumn('product_code');
                $table->integer('total_qty_product_out')->nullable();
                $table->integer('total_qty_product_in')->nullable();
                $table->integer('total_variant_product')->nullable();
                $table->integer('total_cash_in')->nullable();
                $table->integer('total_cash_to_deposit')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stockopname_products', 'is_checked')) {
            Schema::table('stockopname_products', function (Blueprint $table) {
                $table->dropColumn('is_checked');
            });
        }

        if (Schema::hasColumn('stockopnames', 'total_qty_product_out')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->dropColumn('total_qty_product_out');
                $table->dropColumn('total_qty_product_in');
                $table->dropColumn('total_variant_product');
                $table->dropColumn('total_cash_in');
                $table->dropColumn('total_cash_to_deposit');
            });
        }
    }
}
