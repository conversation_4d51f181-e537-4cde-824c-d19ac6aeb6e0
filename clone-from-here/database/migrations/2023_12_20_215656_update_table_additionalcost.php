<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTableAdditionalcost extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('additional_costs')) {
            if (!Schema::hasColumn('additional_costs', 'min_unit')) {
                Schema::table('additional_costs', function (Blueprint $table) {
                    $table->id();
                    $table->unsignedInteger('min_unit')->default(2);
                    $table->integer('total_cost')->default(3000);
                    $table->string('round')->default('up');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('additional_costs', 'min_unit')) {
            Schema::table('additional_costs', function (Blueprint $table) {
                $table->dropColumn('total_cost');
                $table->dropColumn('round');
                $table->dropColumn('min_unit');
            });
        }
    }
}