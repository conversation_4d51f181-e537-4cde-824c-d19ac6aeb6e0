<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateOperationcostSoftdelete extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('employees')) {
            if (!Schema::hasColumn('employees', 'deleted_by')) {
                Schema::table('employees', function (Blueprint $table) {
                    $table->unsignedBigInteger('deleted_by')->nullable();
                    $table->foreign('deleted_by')->references('id')->on('users');
                });
            }
        }
        if (Schema::hasTable('armadas')) {
            if (!Schema::hasColumn('armadas', 'deleted_by')) {
                Schema::table('armadas', function (Blueprint $table) {
                    $table->unsignedBigInteger('deleted_by')->nullable();
                    $table->foreign('deleted_by')->references('id')->on('users');
                });
            }
        }
        if (Schema::hasTable('cost_categories')) {
            if (!Schema::hasColumn('cost_categories', 'deleted_by')) {
                Schema::table('cost_categories', function (Blueprint $table) {
                    $table->unsignedBigInteger('deleted_by')->nullable();
                    $table->foreign('deleted_by')->references('id')->on('users');
                });
            }
        }
        if (Schema::hasTable('operating_costs')) {
            if (!Schema::hasColumn('operating_costs', 'deleted_by')) {
                Schema::table('operating_costs', function (Blueprint $table) {
                    $table->unsignedBigInteger('deleted_by')->nullable();
                    $table->foreign('deleted_by')->references('id')->on('users');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('employees', 'deleted_by')) {
            Schema::table('employees', function (Blueprint $table) {
                $table->dropForeign(['deleted_by']);
                $table->dropColumn('deleted_by');
            });
        }
        if (Schema::hasColumn('armadas', 'deleted_by')) {
            Schema::table('armadas', function (Blueprint $table) {
                $table->dropForeign(['deleted_by']);
                $table->dropColumn('deleted_by');
            });
        }
        if (Schema::hasColumn('cost_categories', 'deleted_by')) {
            Schema::table('cost_categories', function (Blueprint $table) {
                $table->dropForeign(['deleted_by']);
                $table->dropColumn('deleted_by');
            });
        }
        if (Schema::hasColumn('operating_costs', 'deleted_by')) {
            Schema::table('operating_costs', function (Blueprint $table) {
                $table->dropForeign(['deleted_by']);
                $table->dropColumn('deleted_by');
            });
        }
    }
}