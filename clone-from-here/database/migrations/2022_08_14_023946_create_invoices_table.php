<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('code')->index();
            $table->unsignedSmallInteger('sequence_per_day');
            // $table->unsignedBigInteger('status_id');
            // $table->foreign('status_id')->references('id')->on('statuses');
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores');
            $table->unsignedBigInteger('customer_id');
            $table->foreign('customer_id')->references('id')->on('customers');
            $table->string('receiver_phone')->nullable();
            $table->integer('total_bill')->nullable();
            $table->integer('discount')->nullable();
            $table->integer('total_after_discount')->nullable();
            // $table->boolean('is_paid')->default(0);
            $table->integer('deposit_balance_before')->nullable();
            $table->integer('amount_deposit_used')->nullable();
            $table->integer('total_after_deposit')->nullable();
            $table->integer('amount_pay')->nullable();
            $table->integer('deposit_balance_after')->nullable();
            $table->enum('payment_method_confirmed', ['cash', 'transfer', 'qris'])->nullable();
            $table->unsignedBigInteger('bank_id')->nullable();
            $table->foreign('bank_id')->references('id')->on('banks');
            $table->unsignedBigInteger('confirmed_by')->nullable();
            $table->foreign('confirmed_by')->references('id')->on('users');
            $table->text('note')->nullable();
            $table->text('note_confirm')->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->userstamps();
            $table->softUserstamps();
        });

        Schema::create('invoice_order', function ($table) {
            $table->id();
            $table->unsignedBigInteger('invoice_id');
            $table->foreign('invoice_id')->references('id')->on('invoices')->onDelete('cascade');
            $table->unsignedBigInteger('order_id');
            $table->foreign('order_id')->references('id')->on('orders');
            $table->timestamps();
        });

        if (Schema::hasTable('orders')) {
            if (!Schema::hasColumn('orders', 'is_invoiced')) {
                Schema::table('orders', function (Blueprint $table) {
                    $table->boolean('is_invoiced')->default(0)->after('is_offline');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('orders', 'is_invoiced')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropColumn('is_invoiced');
            });
        }

        Schema::dropIfExists('invoice_order');
        Schema::dropIfExists('invoices');
    }
}