<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStockopnamesSubmitedatTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('stockopnames', 'submitted_at')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->timestamp('submitted_at')->nullable();
                $table->timestamp('confirmed_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stockopnames', 'submitted_at')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->dropColumn('submitted_at');
                $table->dropColumn('confirmed_at');
            });
        }
    }
}
