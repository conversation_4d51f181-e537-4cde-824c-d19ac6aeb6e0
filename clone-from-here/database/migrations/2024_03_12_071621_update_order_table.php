<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('orders')) {
            if (!Schema::hasColumn('orders', 'options')) {
                Schema::table('orders', function (Blueprint $table) {
                    $table->json('options')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('orders', 'options')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropColumn('options');
            });
        }
    }
}
