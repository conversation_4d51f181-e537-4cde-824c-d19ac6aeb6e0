<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTablePpobOrderProduct extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('order_product')) {
            if (!Schema::hasColumn('order_product', 'ppob_label')) {
                Schema::table('order_product', function (Blueprint $table) {
                    $table->string('ppob_label')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('order_product', 'ppob_label')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->dropColumn('ppob_label');
            });
        }
    }
}
