<?php

use Illuminate\Database\Migrations\Migration;
// use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGasplusTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cities', function ($table) {
            $table->id();
            $table->string('slug')->index();
            $table->string('name');
            $table->integer('sort_order');
            $table->timestamps();
            $table->userstamps();
            // $table->softDeletes();
            // $table->softUserstamps();
        });

        Schema::create('stores', function ($table) {
            $table->id();
            $table->string('slug')->index();
            $table->unsignedBigInteger('city_id');
            $table->foreign('city_id')->references('id')->on('cities');
            $table->string('area');
            $table->string('name');
            $table->text('address')->nullable();
            $table->string('latlng')->nullable();
            $table->text('description')->nullable();
            $table->string('whatsapp_1');
            $table->string('whatsapp_2')->nullable();
            $table->boolean('is_comingsoon')->default(0);
            $table->boolean('is_notif_wa')->default(0);
            $table->text('msg_order_new')->nullable();
            $table->text('msg_order_delivered')->nullable();
            $table->string('email')->nullable();
            $table->string('chat_server_url')->nullable();
            $table->integer('sort_order');
            $table->date('marketing_start')->nullable();
            $table->unsignedInteger('marketing_each_day')->nullable();
            $table->time('open_hour')->default('07:00:00');
            $table->time('close_hour')->default('17:00:00');
            $table->timestamps();
            $table->userstamps();
        });

        Schema::create('banks', function ($table) {
            $table->id();
            $table->string('bank_name');
            $table->string('account_number');
            $table->string('holder_name');
            $table->integer('sort_order');
            $table->timestamps();
            $table->userstamps();
        });

        Schema::create('bank_store', function ($table) {
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores')->onDelete('cascade');
            $table->unsignedBigInteger('bank_id');
            $table->foreign('bank_id')->references('id')->on('banks')->onDelete('cascade');
            $table->primary(['store_id', 'bank_id']);
            $table->timestamps();
        });

        Schema::create('categories', function ($table) {
            $table->id();
            $table->string('slug')->index();
            $table->string('name');
            $table->integer('sort_order');
            $table->timestamps();
            $table->userstamps();
        });

        Schema::create('products', function ($table) {
            $table->id();
            $table->string('slug')->index();
            $table->string('name');
            $table->string('code')->unique();
            $table->integer('price');
            $table->integer('minimum_order')->default(1);
            $table->string('unit')->default('pcs');
            $table->text('link')->nullable();
            $table->integer('sort_order');
            $table->timestamps();
            $table->softDeletes();
            $table->userstamps();
            $table->softUserstamps();
        });

        Schema::create('category_product', function ($table) {
            $table->unsignedBigInteger('category_id');
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
            $table->unsignedBigInteger('product_id');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->primary(['category_id', 'product_id']);
            $table->timestamps();
        });

        Schema::create('product_store', function ($table) {
            $table->id();
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores')->onDelete('cascade');
            $table->unsignedBigInteger('product_id');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->integer('stock')->default(999);
            $table->integer('local_price')->nullable();
            $table->boolean('is_available')->default(true);
            $table->timestamps();
            // $table->primary(['store_id', 'product_id']);
        });

        Schema::create('customers', function ($table) {
            $table->id();
            $table->string('phone');
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('payment')->nullable();
            $table->integer('amount')->nullable();
            $table->text('note')->nullable();
            $table->text('note_special')->nullable();
            $table->integer('deposit_amount')->default(0);
            $table->timestamps();
            $table->softDeletes();
            $table->userstamps();
            $table->softUserstamps();
        });

        Schema::create('addresses', function ($table) {
            $table->id();
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores');
            $table->unsignedBigInteger('customer_id');
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->string('label')->default('Alamat Utama');
            $table->text('address');
            $table->string('latlng')->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->userstamps();
            $table->softUserstamps();
        });

        Schema::create('statuses', function ($table) {
            $table->id();
            $table->string('code')->index();
            $table->string('name');
            $table->string('color')->default('#444');
            $table->string('icon')->default('fas fa-circle');
            $table->timestamps();
            $table->userstamps();
        });

        Schema::create('orders', function ($table) {
            $table->id();
            $table->timestamps();
            $table->unsignedBigInteger('status_id');
            $table->foreign('status_id')->references('id')->on('statuses');
            $table->unsignedBigInteger('customer_id');
            $table->foreign('customer_id')->references('id')->on('customers');
            $table->string('code')->index();
            $table->integer('deposit_balance_before')->nullable();
            $table->unsignedSmallInteger('sequence_per_day');
            $table->integer('total')->nullable();
            $table->integer('amount_deposit_used')->nullable();
            $table->integer('total_after_deposit')->nullable();
            $table->integer('amount_pay')->nullable();
            $table->integer('amount_split_to_cash')->nullable();
            $table->integer('deposit_balance_after')->nullable();
            $table->string('payment_note')->nullable();
            $table->string('driver_note')->nullable();
            $table->string('confirm_note')->nullable();
            $table->text('note')->nullable();
            $table->string('note_for_driver')->nullable();
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores');
            $table->unsignedBigInteger('address_id');
            $table->foreign('address_id')->references('id')->on('addresses');
            $table->unsignedBigInteger('driver_id')->nullable();
            $table->foreign('driver_id')->references('id')->on('users');
            $table->unsignedBigInteger('cancel_by')->nullable();
            $table->foreign('cancel_by')->references('id')->on('users');
            $table->unsignedBigInteger('confirmed_by')->nullable();
            $table->foreign('confirmed_by')->references('id')->on('users');
            // $table->enum('status', ['open', 'assigned', 'progress', 'completed', 'canceled']);
            $table->integer('duration')->nullable(); // in second
            $table->integer('distance_store_customer')->nullable(); // in meter
            $table->integer('distance_customer_received')->nullable(); // in meter
            $table->enum('payment', ['cash', 'transfer', 'qris'])->nullable();
            $table->enum('payment_method_ask', ['cash', 'non-cash'])->nullable();
            $table->enum('payment_method_confirmed', ['cash', 'transfer', 'qris', 'invoice'])->nullable();
            $table->unsignedBigInteger('bank_id')->nullable();
            $table->foreign('bank_id')->references('id')->on('banks');
            $table->integer('amount_will_pay')->nullable();
            $table->integer('amount_return')->nullable();
            $table->integer('total_deposit')->nullable();
            $table->string('receiver_phone')->nullable();
            $table->timestamp('received_at')->nullable();
            $table->string('received_by')->nullable();
            $table->string('received_latlng')->nullable();
            $table->string('received_latlng_accuracy')->nullable();
            $table->boolean('is_offline')->default(0);
            // $table->unsignedBigInteger('created_by')->nullable(); // by API (null) or by Admin (user_id)
            $table->softDeletes();
            $table->userstamps();
            $table->softUserstamps();
        });

        Schema::create('order_product', function ($table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->unsignedBigInteger('product_id');
            $table->foreign('product_id')->references('id')->on('products');
            $table->integer('qty');
            $table->integer('price')->nullable();
            // $table->primary(['order_id', 'product_id']);
            $table->timestamps();
        });

        Schema::create('feedbacks', function ($table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->tinyInteger('service_rating');
            $table->tinyInteger('delivery_rating');
            $table->text('note')->nullable();
            $table->timestamps();
            $table->userstamps();
        });

        Schema::create('store_user', function ($table) {
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users');
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores');
            $table->primary(['user_id', 'store_id']);
        });

        Schema::create('deposit_flows', function ($table) {
            $table->id();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->foreign('order_id')->references('id')->on('orders');
            $table->unsignedBigInteger('customer_id');
            $table->foreign('customer_id')->references('id')->on('customers');
            $table->integer('amount');
            $table->integer('balance');
            $table->string('note');
            $table->timestamps();
            $table->userstamps();
        });

        Schema::create('totals', function ($table) {
            $table->id();
            $table->morphs('totalable');
            $table->unsignedBigInteger('store_id')->nullable();
            $table->foreign('store_id')->references('id')->on('stores');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users');
            $table->date('date');
            $table->string('type')->nullable();
            $table->integer('total');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('totals');
        Schema::dropIfExists('deposit_flows');
        Schema::dropIfExists('store_user');
        Schema::dropIfExists('feedbacks');
        Schema::dropIfExists('order_product');
        Schema::dropIfExists('orders');
        Schema::dropIfExists('statuses');
        Schema::dropIfExists('addresses');
        Schema::dropIfExists('customers');
        Schema::dropIfExists('product_store');
        Schema::dropIfExists('category_product');
        Schema::dropIfExists('products');
        Schema::dropIfExists('categories');
        Schema::dropIfExists('bank_store');
        Schema::dropIfExists('banks');
        Schema::dropIfExists('stores');
        Schema::dropIfExists('cities');
        // Schema::dropIfExists('images');
    }
}