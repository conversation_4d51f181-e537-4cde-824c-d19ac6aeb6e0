<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTableOperation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vh_brands', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
          });

        Schema::create('vh_models', function (Blueprint $table) {
        $table->id();
        $table->string('name');
        $table->unsignedBigInteger('vh_brand_id');
        $table
            ->foreign('vh_brand_id')
            ->references('id')
            ->on('vh_brands');
        });

        if (Schema::hasTable('armadas')) {
            if (!Schema::hasColumn('armadas', 'vh_brand_id')) {
                Schema::table('armadas', function (Blueprint $table) {
                    $table->smallInteger('year')->nullable()->after('id');
                    $table->unsignedBigInteger('vh_brand_id')->nullable()->after('id');
                    $table->foreign('vh_brand_id')->references('id')->on('vh_brands');
                    $table->unsignedBigInteger('vh_model_id')->nullable()->after('id');
                    $table->foreign('vh_model_id')->references('id')->on('vh_models');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('armadas', 'vh_brand_id')) {
            Schema::table('armadas', function (Blueprint $table) {
                $table->dropForeign(['vh_brand_id']);
                $table->dropColumn('vh_brand_id');
                $table->dropForeign(['vh_model_id']);
                $table->dropColumn('vh_model_id');
                $table->dropColumn('year');
            });
        }

        Schema::dropIfExists('vh_models');
        Schema::dropIfExists('vh_brands');
    }
}