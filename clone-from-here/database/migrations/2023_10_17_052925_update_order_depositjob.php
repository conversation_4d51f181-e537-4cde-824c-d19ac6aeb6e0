<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateOrderDepositjob extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('orders')) {
            if (!Schema::hasColumn('orders', 'deposit_job')) {
                Schema::table('orders', function (Blueprint $table) {
                    $table->integer('deposit_job')->nullable()->after('code');
                });
            }
        }
        if (Schema::hasTable('deposit_flows')) {
            if (!Schema::hasColumn('deposit_flows', 'job_order_id')) {
                Schema::table('deposit_flows', function (Blueprint $table) {
                    $table->unsignedBigInteger('job_order_id')->nullable()->after('id');
                    $table->foreign('job_order_id')->references('id')->on('orders');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('deposit_flows', 'job_order_id')) {
            Schema::table('deposit_flows', function (Blueprint $table) {
                $table->dropForeign(['job_order_id']);
                $table->dropColumn('job_order_id');
            });
        }
        if (Schema::hasColumn('orders', 'deposit_job')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropColumn('deposit_job');
            });
        }
    }
}