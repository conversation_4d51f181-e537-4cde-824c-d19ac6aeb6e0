<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSecondfloorTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('stores')) {
            if (!Schema::hasColumn('stores', 'additionalcost_secondfloor')) {
                Schema::table('stores', function (Blueprint $table) {
                    $table->unsignedInteger('additionalcost_secondfloor')->default(3000);
                });
            }
        }
        if (Schema::hasTable('addresses')) {
            if (!Schema::hasColumn('addresses', 'additionalcost_secondfloor')) {
                Schema::table('addresses', function (Blueprint $table) {
                    $table->boolean('is_secondfloor')->default(0);
                    $table->unsignedInteger('additionalcost_secondfloor')->nullable();
                });
            }
        }
        if (!Schema::hasTable('additional_costs')) {
            Schema::create('additional_costs', function ($table) {
                $table->unsignedBigInteger('order_id');
                $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
                $table->string('name');
                $table->integer('cost');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('additional_costs');

        if (Schema::hasColumn('addresses', 'additionalcost_secondfloor')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->dropColumn('additionalcost_secondfloor');
            });
        }
        if (Schema::hasColumn('stores', 'additionalcost_secondfloor')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->dropColumn('additionalcost_secondfloor');
            });
        }
    }
}