<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStockopnameCostTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stockopname_costs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('stockopname_id');
            $table->foreign('stockopname_id')->references('id')->on('stockopnames')->onDelete('cascade');
            $table->string('note');
            $table->integer('cost');
            $table->userstamps();
            $table->timestamps();
        });

        if (!Schema::hasColumn('stockopnames', 'total_cost')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->integer('total_cost')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stockopnames', 'total_cost')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->dropColumn('total_cost');
            });
        }

        Schema::dropIfExists('stockopname_costs');
    }
}
