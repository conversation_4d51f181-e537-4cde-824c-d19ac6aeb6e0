<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddcolumnStockopnamesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('stockopnames', 'total_spd_deposit')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->integer('total_spd_deposit')->nullable();
                $table->integer('qty_spd_deposit')->nullable();
                $table->integer('total_std_cpr')->nullable();
                $table->integer('avg_std_cpr')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stockopnames', 'total_spd_deposit')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->dropColumn('total_spd_deposit');
                $table->dropColumn('qty_spd_deposit');
                $table->dropColumn('total_std_cpr');
                $table->dropColumn('avg_std_cpr');
            });
        }
    }
}
