<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTablesOperation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users');
            // $table->unsignedBigInteger('armada_id');
            // $table->foreign('armada_id')->references('id')->on('armadas');
            $table->text('full_name');
            $table->date('dob')->nullable();
            $table->text('address')->nullable();
            $table->string('kk_number')->nullable();
            $table->string('ktp_number')->nullable();
            $table->string('sim_number')->nullable();
            $table->date('sim_valid_until')->nullable();
            $table->date('start_work_at')->nullable();
            $table->date('quit_work_at')->nullable();
            $table->text('quit_note')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();
            $table->userstamps();
            $table->softDeletes();
        });

        // Schema::create('vh_brands', function (Blueprint $table) {
        //     $table->id();
        //     $table->string('name')->unique();
        //   });

        //   Schema::create('vh_models', function (Blueprint $table) {
        //     $table->id();
        //     $table->string('name');
        //     $table->unsignedBigInteger('vh_brand_id');
        //     $table
        //       ->foreign('vh_brand_id')
        //       ->references('id')
        //       ->on('vh_brands');
        //   });

        Schema::create('armadas', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id')->nullable();
            $table->foreign('employee_id')->references('id')->on('employees');
            // $table->unsignedBigInteger('vh_brand_id')->nullable();
            // $table->foreign('vh_brand_id')->references('id')->on('vh_brands');
            // $table->unsignedBigInteger('vh_model_id')->nullable();
            // $table->foreign('vh_model_id')->references('id')->on('vh_models');
            // $table->smallInteger('year')->nullable();
            $table->string('licence_number')->unique();
            $table->string('chassis_number')->nullable();
            $table->string('machine_number')->nullable();
            $table->date('stnk_valid_until')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();
            $table->userstamps();
            $table->softDeletes();
        });

        // if (Schema::hasTable('users')) {
        //     if (!Schema::hasColumn('users', 'armada_id')) {
        //         Schema::table('users', function (Blueprint $table) {
        //             $table->unsignedBigInteger('armada_id')->nullable()->after('id');
        //             $table->foreign('armada_id')->references('id')->on('armadas');
        //         });
        //     }
        // }

        Schema::create('cost_categories', function (Blueprint $table) {
            $table->id();
            $table->string('title')->unique();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id')->references('id')->on('cost_categories');
            $table->boolean('is_root')->default(1);
            $table->timestamps();
            $table->userstamps();
            $table->softDeletes();
        });

        Schema::create('operating_costs', function (Blueprint $table) {
            $table->id();
            $table->timestamp('submit_at')->useCurrent();
            $table->unsignedBigInteger('user_id'); // siapa yang submit
            $table->foreign('user_id')->references('id')->on('users');
            $table->unsignedBigInteger('store_id')->nullable(); // khusus untuk biaya armada & biaya bulanan toko
            $table->foreign('store_id')->references('id')->on('stores');

            $table->unsignedBigInteger('cost_category_id');
            $table->foreign('cost_category_id')->references('id')->on('cost_categories');
            $table->unsignedBigInteger('armada_id')->nullable();
            $table->foreign('armada_id')->references('id')->on('armadas');
            $table->unsignedBigInteger('employee_id')->nullable();
            $table->foreign('employee_id')->references('id')->on('employees');

            $table->text('note')->nullable();
            $table->timestamps();
            $table->userstamps();
            $table->softDeletes();
        });

        Schema::create('operating_cost_items', function ($table) {
            $table->id();
            $table->unsignedBigInteger('operating_cost_id');
            $table->foreign('operating_cost_id')->references('id')->on('operating_costs')->onDelete('cascade');
            $table->unsignedBigInteger('cost_category_id');
            $table->foreign('cost_category_id')->references('id')->on('cost_categories');
            $table->integer('price');
            $table->text('note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('operating_cost_items');
        Schema::dropIfExists('operating_costs');
        Schema::dropIfExists('cost_categories');
        Schema::dropIfExists('cost_categories');
        // if (Schema::hasColumn('users', 'armada_id')) {
        //     Schema::table('users', function (Blueprint $table) {
        //         $table->dropForeign(['armada_id']);
        //         $table->dropColumn('armada_id');
        //     });
        // }
        Schema::dropIfExists('armadas');
        // Schema::dropIfExists('vh_models');
        // Schema::dropIfExists('vh_brands');
        Schema::dropIfExists('employees');
    }
}