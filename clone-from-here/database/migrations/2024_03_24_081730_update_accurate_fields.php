<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateAccurateFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // $table->dropUnique('users_email_unique');

        if (Schema::hasTable('stores')) {
            if (!Schema::hasColumn('stores', 'accurate_branch_id')) {
                Schema::table('stores', function (Blueprint $table) {
                    $table->string('accurate_branch_id')->unique()->nullable();
                });
            }
        }

        if (Schema::hasTable('addresses')) {
            if (Schema::hasColumn('addresses', 'accurate_code')) {
                Schema::table('addresses', function (Blueprint $table) {
                    $table->renameColumn('accurate_id', 'accurate_customer_id');
                    $table->renameColumn('accurate_code', 'accurate_customer_code');
                });
            }
        }

        if (Schema::hasTable('settings')) {
            if (!Schema::hasColumn('settings', 'created_at')) {
                Schema::table('settings', function (Blueprint $table) {
                    $table->timestamps();
                    $table->userstamps();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('settings', 'created_at')) {
            Schema::table('settings', function (Blueprint $table) {
                $table->dropColumn('created_at');
                $table->dropColumn('updated_at');
                $table->dropColumn('created_by');
                $table->dropColumn('updated_by');
            });
        }

        if (!Schema::hasColumn('addresses', 'accurate_customer_id')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->renameColumn('accurate_customer_id', 'accurate_id');
                $table->renameColumn('accurate_customer_code', 'accurate_code');
            });
        }

        if (Schema::hasColumn('stores', 'accurate_branch_id')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->dropColumn('accurate_id');
            });
        }
    }
}
