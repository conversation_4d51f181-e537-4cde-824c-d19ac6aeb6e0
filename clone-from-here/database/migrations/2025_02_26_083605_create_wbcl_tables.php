<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWbclTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('notification_schedules')) {
            Schema::create('notification_schedules', function (Blueprint $table) {
                $table->id();
                // $table->integer('lt_month')->nullable();
                // $table->integer('gt_month')->nullable();
                $table->enum('criteria', ['test', 'gt1-lt2-month', 'gt2-lt3-month', 'gt3-lt6-month', 'gt6-month']);
                $table->text('message_template');
                $table->boolean('is_active')->default(0);
                $table->timestamps();
                $table->userstamps();
            });
        }

        if (!Schema::hasTable('notification_logs')) {
            Schema::create('notification_logs', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('notification_schedule_id');
                $table->foreign('notification_schedule_id')->references('id')->on('notification_schedules');
                $table->unsignedBigInteger('customer_id');
                $table->foreign('customer_id')->references('id')->on('customers');
                $table->string('message');
                $table->json('response');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notification_logs');
        Schema::dropIfExists('notification_schedules');
    }
}
