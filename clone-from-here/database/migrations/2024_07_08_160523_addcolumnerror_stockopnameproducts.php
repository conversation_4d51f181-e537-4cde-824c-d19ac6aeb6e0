<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddcolumnerrorStockopnameproducts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('stockopname_products', 'error')) {
            Schema::table('stockopname_products', function (Blueprint $table) {
                $table->string('error')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stockopname_products', 'error')) {
            Schema::table('stockopname_products', function (Blueprint $table) {
                $table->dropColumn('error');
            });
        }
    }
}
