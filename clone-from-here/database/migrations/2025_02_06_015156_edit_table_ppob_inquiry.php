<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EditTablePpobInquiry extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('ppob_inquiry_datas')) {
            Schema::create('ppob_inquiry_datas', function (Blueprint $table) {
                $table->id();
                $table->string('ppob_ref_id')->index();
                $table->string('ppob_product_code')->nullable()->index();
                $table->string('ppob_key')->index();
                $table->string('ppob_tr_id')->nullable();
                $table->json('data')->nullable();
                $table->unsignedBigInteger('product_id');
                $table->foreign('product_id')->references('id')->on('products');
                $table->unsignedBigInteger('customer_id')->nullable();
                $table->foreign('customer_id')->references('id')->on('customers');
                // $table->timestamps();
            });
        }

        if (Schema::hasTable('order_product')) {
            if (!Schema::hasColumn('order_product', 'ppob_tr_id')) {
                Schema::table('order_product', function (Blueprint $table) {
                    $table->string('ppob_tr_id')->nullable();
                    $table->integer('ppob_fee')->nullable();
                    $table->integer('ppob_komisi')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('order_product', 'ppob_tr_id')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->dropColumn('ppob_tr_id');
                $table->dropColumn('ppob_fee');
                $table->dropColumn('ppob_komisi');
            });
        }
        Schema::dropIfExists('ppob_inquiry_datas');
    }
}
