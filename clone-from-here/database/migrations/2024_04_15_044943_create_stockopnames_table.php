<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStockopnamesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stockopnames', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores');
            $table->enum('status', ['draft', 'submitted', 'confirmed'])->default('draft');
            $table->integer('count_products')->nullable();
            $table->integer('count_products_checked')->nullable();
            $table->integer('total_products_less')->nullable();
            $table->integer('total_products_equal')->nullable();
            $table->integer('total_products_over')->nullable();
            // $table->integer('total_cost')->nullable();
            $table->date('date_assigned');
            $table->timestamp('date_submitted')->nullable();
            $table->timestamp('date_confirmed')->nullable();
            $table->unsignedBigInteger('assigned_to');
            $table->foreign('assigned_to')->references('id')->on('users');
            $table->unsignedBigInteger('submitted_by')->nullable();
            $table->foreign('submitted_by')->references('id')->on('users');
            $table->unsignedBigInteger('confirmed_by')->nullable();
            $table->foreign('confirmed_by')->references('id')->on('users');
            $table->timestamps();
            $table->userstamps();
        });

        Schema::create('stockopname_products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('stockopname_id');
            $table->foreign('stockopname_id')->references('id')->on('stockopnames')->onDelete('cascade');
            $table->unsignedBigInteger('product_id');
            $table->foreign('product_id')->references('id')->on('products');
            $table->string('product_code')->nullable();
            $table->integer('start_balance');
            $table->integer('quantity_in');
            $table->integer('quantity_out');
            $table->integer('last_balance');
            $table->integer('quantity_check')->nullable();
            $table->integer('balance_diff')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();
            $table->userstamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stockopnames');
    }
}
