{"taxReference": null, "taxDateView": "21 May 2025", "printUserName": "Belum cetak/email", "reverseInvoice": false, "journalDpId": null, "numericField10": 0, "expiredDate": "-", "reverseInvoiceStatus": null, "masterProjectId": null, "receiveItem": false, "lastReturnDate": null, "currencyId": 50, "efakturProceed": false, "transDateView": "21 May 2025", "tax1Id": null, "branchId": 101900, "lastPaymentId": null, "billOfMaterial": false, "employeeLoanSettlement": false, "taxNumber": null, "retailWpName": "", "tax1Amount": 0, "salesOrder": false, "commentCount": 0, "hasNPWP": false, "lastUpdate": "21/05/2025 08:53:59", "orderDownPayment": null, "taxableAmount2": 0, "taxableAmount1": 0, "lastCashDiscPercent": "", "taxableAmount4": 0, "poNumber": null, "charField10": "", "processStages": false, "posCreateDate": null, "preliminarySurvey": false, "availableDownPayment": 0, "journalId": 997389, "taxReturn": 0, "exchangeInvoiceId": null, "customerClaim": false, "journal": false, "referenceDateTax": null, "projectAmount": null, "deliveryPacking": false, "paymentWithEPayment": false, "customerId": 231939, "percentTaxablePrecision": 100, "deliveryPackingId": null, "currency": {"symbol": "Rp", "defaultArAccountId": 57, "code": "IDR", "defaultSalesDiscAccountId": 60, "optLock": 7, "currencyConverterType": "TO_LOCAL", "defaultAdvSalesAccountId": 59, "exchangeRateSymbolCode": "IDR", "defaultAdvPurchaseAccountId": 58, "defaultRealizeGlAccountId": 61, "name": "Indonesian Rupiah", "codeSymbol": "IDR Rp", "id": 50, "defaultApAccountId": 56, "defaultUnrealizeGlAccountId": 62, "converterTypeName": "1 IDR=XXX IDR"}, "vendorPrice": false, "paymentTermId": 50, "outstanding": true, "fobId": null, "deliveryOrder": false, "efakturPdfFilePath": "", "salesDownPayment": false, "taxReceipt": 0, "taxReported": false, "deliveryPackingNumber": "", "taxable1Total": 0, "detailDownPayment": [], "onlineOrder": false, "dueDate": "21/05/2025", "inclusiveTax": false, "taxableDiscount4": 0, "taxableDiscount2": 0, "hasVaCompanyCode": false, "toAddress": "<PERSON><PERSON><PERSON>an\n<PERSON>", "taxableDiscount1": 0, "rollOver": false, "lastUpdateView": "21 May 2025 08:53:59", "masterDepartmentId": null, "retailIdCard": "", "epaymentStatus": null, "taxOwing": 0, "projectProgress": null, "itemAdjustment": false, "taxReceiptHistory": [], "masterPosSalesType": null, "periodEnd": false, "workOrder": false, "masterSalesmanId": null, "projectPaymentId": null, "purchaseDownPayment": false, "shipment": null, "invoiceTimeView": "01 Jan 1970", "cashierEmployeeId": null, "salesCheckIn": false, "epaymentCode": null, "sptMasaId": null, "primeReturn": 0, "salesQuotation": false, "fixedAssetEdited": false, "fob": null, "taxDownPayment": 0, "projectItemBillId": null, "tax1AmountBase": 0, "canSelfPaidPph": false, "pendingAmortized": false, "notesIdTax": null, "description": "test", "invoiceDp": false, "invoicePrintedTime": "", "budgetPlan": false, "rate": 1, "efakturEmailTime": null, "lastPaymentDate": null, "transDate": "21/05/2025", "inputDownPayment": 0, "cashDiscount": 0, "primeReceipt": 0, "primeDownPayment": 0, "salesReceipt": false, "approvalStatus": "APPROVED", "tax2Id": null, "materialSlip": false, "sellingPriceAdjustment": false, "externalId": null, "cashierEmployeeName": "", "downPaymentAccountId": null, "taxDownPayment2": 0, "suggestedDiscount": 0, "taxDownPayment1": 0, "numericField9": 0, "numericField8": 0, "numericField7": 0, "createdBy": 638486, "shipmentId": null, "numericField6": 0, "numericField5": 0, "numericField4": 0, "purchaseOrder": false, "numericField3": 0, "salesAmountBase": 47000, "numericField2": 0, "numericField1": 0, "userPrinted": null, "age": 0, "charField8": "", "charField9": "", "invoiceTime": "08:53:59", "tax4Amount": 0, "charField6": "", "tax1Rate": 0, "charField7": "", "charField4": "", "charField5": "", "optLock": 7, "epaymentSettingId": null, "epaymentAmount": null, "percentTaxable": 100, "reversePercentShipped": 0, "vaNumber": "", "arAccountId": 57, "returnHistory": [], "number": "SI.2025.05.03930", "printedTime": "", "taxable2Total": 0, "charField2": "", "charField3": "", "dueDateView": "21 May 2025", "charField1": "", "id": 577384, "taxType": null, "hasStatusHistory": true, "dateField2": null, "dateField1": null, "taxable": false, "customerHasDownPayment": false, "invoiceDiscountAccountId": null, "salesInvoice": true, "purchaseInvoice": false, "shipDate": "21/05/2025", "hasExchangeInvoice": false, "uniqueAmount": 0, "documentTransaction": null, "transferOrder": false, "detailExpense": [], "projectBill": false, "detailItem": [{"deliveryOrderDetailId": null, "hasPosSalesType": false, "lastItemDiscPercent": "", "charField8": null, "detailDynamicGroupTotalPrice": 0, "returnQuantity": 0, "charField9": null, "processQuantityDesc": "", "charField6": null, "tax1Rate": 0, "numericField10": null, "charField7": null, "charField4": null, "departmentId": null, "charField5": null, "optLock": 2, "itemUnit": {"codeUnitTax": null, "optLock": 0, "name": "Galon", "id": 51}, "detailSerialNumber": [], "salesOrderPoNumber": "", "waitressEmployeeId": null, "charField2": null, "charField3": null, "reverseInvoiceUnitCost": 0, "salesOrderDetailId": null, "charField1": null, "id": 648443, "salesman3Id": null, "dataClassification6Id": null, "salesQuotationDetailId": null, "unitPrice": 22000, "useTax1": false, "dateField2": null, "dataClassification1": null, "salesmanName": "", "branchId": 101900, "useTax3": false, "dataClassification3": null, "item": {"unit2Id": null, "charField8": null, "charField9": null, "charField6": null, "numericField10": 0, "charField7": null, "unit4Price": 0, "charField4": null, "charField5": null, "optLock": 57, "percentTaxable": 100, "itemBrandId": null, "ratioVendorUnit": 1, "unit3Price": 0, "salesDiscountGlAccountId": 101, "itemProduced": true, "charField2": null, "charField3": null, "charField1": null, "id": 52, "maxOptionModifier": null, "unitPrice": 22000, "dateField2": null, "tax1Id": null, "branchId": null, "dateField1": null, "useWholesalePrice": false, "onSales": 0, "unit5Price": 0, "unit2Price": 0, "suspended": false, "goodTransitGlAccountId": 73, "cogsGlAccountId": 102, "referenceSubstitutionId": null, "shortName": "<PERSON><PERSON>", "itemSubstitutionId": null, "charField10": null, "unBilledGlAccountId": 94, "canChangeDetailGroup": false, "variantLabel2": null, "unit3Id": null, "dimWidth": 0, "variantLabel1": null, "salesRetGlAccountId": 100, "dimDepth": 0, "minimumQuantityReorder": 150, "lockTime": null, "vendorPrice": 16750, "minimumQuantity": 0, "tax4Id": null, "dimHeight": 0, "additionalCost": false, "unit4Id": null, "notes": null, "purchaseRetGlAccountId": 72, "unit1": {"codeUnitTax": null, "optLock": 0, "name": "Galon", "id": 51}, "variantParentId": null, "unit2": null, "unit3": null, "vendorUnit": {"codeUnitTax": null, "optLock": 0, "name": "Galon", "id": 51}, "preferedVendorId": 108450, "ratio2": 0, "unit4": null, "unit5": null, "ratio4": 0, "ratio3": 0, "ratio5": 0, "variantDetail2": null, "tax3Id": null, "inventoryGlAccountId": 72, "variantDetail1": null, "controlQuantity": false, "weight": null, "upcNo": "*************", "substituted": false, "minOptionModifier": 1, "salesGlAccountId": 98, "name": "<PERSON><PERSON>", "deliveryLeadTime": 0, "manageExpired": false, "no": "IA", "defaultDiscount": null, "itemType": "INVENTORY", "unit1Id": 51, "unit5Id": null, "itemCategoryId": 101950, "hasImage": false, "tax1": null, "tax2": null, "manageSN": false, "materialProduced": true, "variantSeq": null, "codeItemTax": null, "tax3": null, "tax4": null, "serialNumberType": null, "tax2Id": null, "cost": 0, "printDetailGroup": false, "calculateGroupPrice": false, "numericField9": 0, "defStandardCost": 0, "numericField8": 0, "numericField7": 0, "numericField6": 0, "numericField5": 0, "numericField4": 0, "numericField3": null, "numericField2": null, "numericField1": null, "vendorUnitId": 51, "unit1Price": 0}, "purchaseInvoiceDetailId": null, "useTax2": false, "dataClassification1Id": null, "dateField1": null, "dataClassification2": null, "dataClassification5": null, "dataClassification4": null, "dataClassification7": null, "dataClassification6": null, "dataClassification9": null, "dataClassification8": null, "posSalesType": null, "tax1Amount": 0, "detailNotes": null, "warehouse": {"scrapWarehouse": false, "defaultWarehouse": false, "locationId": 109150, "optLock": 2, "name": "<PERSON><PERSON><PERSON>", "description": null, "pic": "<PERSON><PERSON>", "id": 101900, "suspended": false}, "startAmortizeMonth": 5, "itemId": 52, "warehouseId": 101900, "charField15": null, "charField13": null, "charField14": null, "charField11": null, "charField12": null, "useTax4": false, "charField10": null, "hasAmortize": false, "canChangeDetailGroup": false, "dataClassification10": null, "detailDynamicGroupPrice": 0, "detailName": "<PERSON><PERSON>", "lastItemCashDiscount": 0, "totalPrice": 44000, "salesInvoiceId": 577384, "groupSeq": null, "salesAmount": 44000, "purchaseInvoiceId": null, "dataClassification7Id": null, "seq": 1, "dataClassification2Id": null, "salesman2Id": null, "onlineOrderDetailId": null, "onlineOrderId": null, "salesOrderId": null, "amortizeAccount": null, "project": null, "deliveredQuantity": 0, "startAmortizeYear": 2025, "quantityDefault": 2, "itemDiscPercent": "", "dataClassification8Id": null, "startAmortizeType": "NONE", "salesQuotationId": null, "salesman5Id": null, "deliveryOrderId": null, "dataClassification3Id": null, "startAmortize": null, "salesman1Id": null, "tax3Id": null, "controlQuantity": 0, "grossAmount": 44000, "projectId": null, "dppAmount": 0, "tax2Amount": 0, "dynamicGroup": false, "amortizeMonth": null, "posSalesTypeId": null, "dataClassification10Id": null, "itemCashDiscount": 0, "dataClassification9Id": null, "salesmanList": [], "itemDiscountAccountId": null, "department": null, "salesman4Id": null, "tax3": null, "detailTaxName": "", "dataClassification5Id": null, "dataClassification4Id": null, "quantity": 2, "amortizeAccountId": null, "detailDynamicGroup": false, "totalCaptionSerialNumber": "0 No Seri/Produksi. Isikan 2 lagi", "numericField9": null, "numericField8": null, "numericField7": null, "numericField6": null, "numericField5": null, "deliveryOrderPoNumber": "", "numericField4": null, "itemUnitId": 51, "numericField3": null, "salesAmountBase": 44000, "numericField2": null, "itemHistoryId": 1193242, "numericField1": null, "unitRatio": 1}, {"deliveryOrderDetailId": null, "hasPosSalesType": false, "lastItemDiscPercent": "", "charField8": null, "detailDynamicGroupTotalPrice": 0, "returnQuantity": 0, "charField9": null, "processQuantityDesc": "", "charField6": null, "tax1Rate": 0, "numericField10": null, "charField7": null, "charField4": null, "departmentId": null, "charField5": null, "optLock": 2, "itemUnit": null, "detailSerialNumber": [], "salesOrderPoNumber": "", "waitressEmployeeId": null, "charField2": null, "charField3": null, "reverseInvoiceUnitCost": 0, "salesOrderDetailId": null, "charField1": null, "id": 648444, "salesman3Id": null, "dataClassification6Id": null, "salesQuotationDetailId": null, "unitPrice": 3000, "useTax1": false, "dateField2": null, "dataClassification1": null, "salesmanName": "", "branchId": 101900, "useTax3": false, "dataClassification3": null, "item": {"unit2Id": null, "charField8": null, "charField9": null, "charField6": null, "numericField10": 0, "charField7": null, "unit4Price": 0, "charField4": null, "charField5": null, "optLock": 2, "percentTaxable": 100, "itemBrandId": null, "ratioVendorUnit": 1, "unit3Price": 0, "salesDiscountGlAccountId": 101, "itemProduced": false, "charField2": null, "charField3": null, "charField1": null, "id": 108800, "maxOptionModifier": 1, "unitPrice": 3000, "dateField2": null, "tax1Id": null, "branchId": null, "dateField1": null, "useWholesalePrice": false, "onSales": 0, "unit5Price": 0, "unit2Price": 0, "suspended": false, "goodTransitGlAccountId": null, "cogsGlAccountId": null, "referenceSubstitutionId": null, "shortName": "Jasa Lantai Atas", "itemSubstitutionId": null, "charField10": null, "unBilledGlAccountId": 94, "canChangeDetailGroup": false, "variantLabel2": null, "unit3Id": null, "dimWidth": 0, "variantLabel1": null, "salesRetGlAccountId": null, "dimDepth": 0, "minimumQuantityReorder": 0, "lockTime": null, "vendorPrice": 0, "minimumQuantity": 0, "tax4Id": null, "dimHeight": 0, "additionalCost": false, "unit4Id": null, "notes": null, "purchaseRetGlAccountId": null, "unit1": null, "variantParentId": null, "unit2": null, "unit3": null, "vendorUnit": null, "preferedVendorId": null, "ratio2": 0, "unit4": null, "unit5": null, "ratio4": 0, "ratio3": 0, "ratio5": 0, "variantDetail2": null, "tax3Id": null, "inventoryGlAccountId": 123, "variantDetail1": null, "controlQuantity": false, "weight": 0, "upcNo": "", "substituted": false, "minOptionModifier": 0, "salesGlAccountId": 98, "name": "Jasa Lantai Atas", "deliveryLeadTime": 0, "manageExpired": false, "no": "JLA", "defaultDiscount": null, "itemType": "SERVICE", "unit1Id": null, "unit5Id": null, "itemCategoryId": 101850, "hasImage": false, "tax1": null, "tax2": null, "manageSN": false, "materialProduced": true, "variantSeq": null, "codeItemTax": null, "tax3": null, "tax4": null, "serialNumberType": null, "tax2Id": null, "cost": 0, "printDetailGroup": false, "calculateGroupPrice": false, "numericField9": 0, "defStandardCost": 0, "numericField8": 0, "numericField7": 0, "numericField6": 0, "numericField5": 0, "numericField4": 0, "numericField3": 0, "numericField2": 0, "numericField1": 0, "vendorUnitId": null, "unit1Price": 0}, "purchaseInvoiceDetailId": null, "useTax2": false, "dataClassification1Id": null, "dateField1": null, "dataClassification2": null, "dataClassification5": null, "dataClassification4": null, "dataClassification7": null, "dataClassification6": null, "dataClassification9": null, "dataClassification8": null, "posSalesType": null, "tax1Amount": 0, "detailNotes": null, "warehouse": null, "startAmortizeMonth": 5, "itemId": 108800, "warehouseId": null, "charField15": null, "charField13": null, "charField14": null, "charField11": null, "charField12": null, "useTax4": false, "charField10": null, "hasAmortize": false, "canChangeDetailGroup": false, "dataClassification10": null, "detailDynamicGroupPrice": 0, "detailName": "Jasa Lantai Atas", "lastItemCashDiscount": 0, "totalPrice": 3000, "salesInvoiceId": 577384, "groupSeq": null, "salesAmount": 3000, "purchaseInvoiceId": null, "dataClassification7Id": null, "seq": 2, "dataClassification2Id": null, "salesman2Id": null, "onlineOrderDetailId": null, "onlineOrderId": null, "salesOrderId": null, "amortizeAccount": null, "project": null, "deliveredQuantity": 0, "startAmortizeYear": 2025, "quantityDefault": 1, "itemDiscPercent": "", "dataClassification8Id": null, "startAmortizeType": "NONE", "salesQuotationId": null, "salesman5Id": null, "deliveryOrderId": null, "dataClassification3Id": null, "startAmortize": null, "salesman1Id": null, "tax3Id": null, "controlQuantity": 0, "grossAmount": 3000, "projectId": null, "dppAmount": 0, "tax2Amount": 0, "dynamicGroup": false, "amortizeMonth": null, "posSalesTypeId": null, "dataClassification10Id": null, "itemCashDiscount": 0, "dataClassification9Id": null, "salesmanList": [], "itemDiscountAccountId": null, "department": null, "salesman4Id": null, "tax3": null, "detailTaxName": "", "dataClassification5Id": null, "dataClassification4Id": null, "quantity": 1, "amortizeAccountId": null, "detailDynamicGroup": false, "totalCaptionSerialNumber": "0 No Seri/Produksi. Isikan 1 lagi", "numericField9": null, "numericField8": null, "numericField7": null, "numericField6": null, "numericField5": null, "deliveryOrderPoNumber": "", "numericField4": null, "itemUnitId": null, "numericField3": null, "salesAmountBase": 3000, "numericField2": null, "itemHistoryId": null, "numericField1": null, "unitRatio": 1}], "employeePayment": false, "expenseAccrual": false, "salesReturn": false, "referenceNumberTax": null, "status": "OUTSTANDING", "lastTaxPaymentDate": null, "employeeLoanInstallment": false, "bankTransfer": false, "deliveryOrderHistory": [], "jobOrder": false, "statusOutstanding": "<PERSON><PERSON>", "salesAmount": 47000, "downPaymentUsed": 0, "masterPosSalesTypeId": null, "statusName": "<PERSON><PERSON>", "dpTax3Id": null, "primeOwing": 47000, "tax2AmountBase": 0, "tax4Rate": 0, "tax4Id": null, "efakturFileName": null, "taxDate": "21/05/2025", "onlineOrderId": null, "approvalTypeNumberId": null, "canDelivery": false, "purchaseReturn": false, "totalExpense": 0, "documentCode": "DIGUNGGUNG", "stockOpnameResult": false, "exchangeInvoice": null, "subTotal": 47000, "lastCashDiscount": 0, "employeeLoanDisbursement": false, "paymentPointOnlineBank": false, "manufactureOrder": false, "returnDownPayment": 0, "masterSalesmanName": "", "exchangeInvoiceDueDate": null, "tax2Rate": 0, "epaymentSetting": null, "projectPaymentAmount": null, "finishedProject": false, "forceCalculatePercentTaxable": false, "openingBalance": false, "epaymentType": null, "fiscalRate": 1, "orderDownPaymentId": null, "paymentWithUniqueAmount": false, "assetTransfer": false, "recurringDetailId": null, "tax4AmountBase": 0, "totalAmountWithUniqueAmount": 0, "stockOpnameOrder": false, "detailSchedulePayment": [], "projectContractorId": null, "projectAddendum": null, "exchangeInvoiceNumber": null, "totalAmount": 47000, "hourTime": 8, "shipDateView": "21 May 2025", "paymentTermIsInstallment": false, "manualApprovalNumber": null, "purchaseRequisition": false, "paymentTerm": {"cashOnDelivery": true, "optLock": 1, "discDays": 0, "installmentTerm": false, "name": "Tunai", "memo": "C.O.D", "defaultTerm": true, "discPC": 0, "id": 50, "netDays": 0, "suspended": false, "manualTerm": false}, "noneInvoiceReturn": false, "dppAmount": 0, "finishedGoodSlip": false, "tax2Amount": 0, "purchasePayment": false, "snRequired": false, "otherDeposit": false, "otherPayment": false, "receiptHistory": [], "tax1": null, "costDistribution": false, "tax2": null, "itemTransfer": false, "vendorClaim": false, "forceCalculateTaxRate": false, "projectPaymentPortion": null, "lastRetailIdCard": "", "reversePercentShippedPoint": "0%", "tax3Amount": 0, "materialEquipment": false, "tax4": null, "cashDiscPercent": "", "efakturEmailUserId": null, "hasDeliveryPacking": false, "multiTaxRate": false, "branchName": "Gasplus Palagan", "materialAdjustment": false, "totalDownPayment": 0, "checkInId": null, "subComp": null, "fixedAsset": false, "epaymentStatusName": "<PERSON><PERSON> terbaya<PERSON>", "attachmentCount": 0, "standardProductCost": false, "arAccount": {"no": "110301", "sub": true, "parent": {"no": "1103", "sub": false, "parent": {"no": null, "sub": false, "parent": null, "lvl": 0, "accountType": null, "optLock": 0, "memo": null, "accountTypeName": "", "parentNode": true, "nameWithIndent": "Root", "suspended": false, "autoNumberTransactionId": null, "nameWithIndentStrip": "Root", "fiscal": false, "noWithIndent": "null", "name": "Root", "id": 50, "currencyId": null}, "lvl": 1, "accountType": "ACCOUNT_RECEIVABLE", "optLock": 0, "memo": null, "accountTypeName": "<PERSON><PERSON><PERSON>", "parentNode": true, "nameWithIndent": "<PERSON><PERSON><PERSON>", "suspended": false, "autoNumberTransactionId": null, "nameWithIndentStrip": "<PERSON><PERSON><PERSON>", "fiscal": false, "noWithIndent": "1103", "name": "<PERSON><PERSON><PERSON>", "id": 52, "currencyId": 50}, "lvl": 2, "accountType": "ACCOUNT_RECEIVABLE", "optLock": 0, "memo": null, "accountTypeName": "<PERSON><PERSON><PERSON>", "parentNode": false, "nameWithIndent": "&nbsp;&nbsp;&nbsp;&nbsp;Piutang Usaha IDR", "suspended": false, "autoNumberTransactionId": null, "nameWithIndentStrip": "- Piutang Usaha IDR", "fiscal": false, "noWithIndent": "&nbsp;&nbsp;&nbsp;&nbsp;110301", "name": "Piutang Usaha IDR", "id": 57, "currencyId": 50}, "customer": {"documentCode": "DIGUNGGUNG", "referenceCustomerLimitId": null, "consignmentStore": false, "charField8": null, "customerLimitAmountValue": 0, "charField9": null, "charField6": null, "contactInfo": {"mobilePhone": "*************", "homePhone": null, "workPhone": null, "email": "<EMAIL>"}, "numericField10": null, "charField7": null, "idCard": null, "charField4": null, "charField5": null, "optLock": 5, "customerLimitAge": false, "defaultIncTax": false, "groupCustomerLimit": false, "charField2": null, "charField3": null, "charField1": null, "id": 231939, "salesman3Id": null, "currencyId": 50, "arAccountCount": 0, "salesman5Id": null, "dateField2": null, "branchId": 101900, "shipSameAsBill": false, "dateField1": null, "defaultTermId": 50, "pkpNo": null, "npwpNo": null, "documentTransaction": null, "suspended": false, "warehouseId": null, "name": "Ds testing", "shipAddressList": [{"country": "Indonesia", "branchId": null, "zipCode": "", "notes": "Ds testing", "address": "<PERSON><PERSON><PERSON>an\n<PERSON>", "city": "Yogyakarta", "latitude": null, "optLock": 1, "vendorId": null, "employeeId": null, "suspended": false, "taxLocation": false, "deleted": false, "province": "", "warehouseId": null, "street": "<PERSON><PERSON><PERSON>an", "customerId": 231939, "name": "Ds testing", "concatFullAddress": "<PERSON><PERSON><PERSON>an <PERSON>", "id": 248573, "projectId": null, "headQuarter": false, "longitude": null}, {"country": "Indonesia", "branchId": null, "zipCode": "", "notes": "Ds testing", "address": "<PERSON><PERSON><PERSON>an\n<PERSON>", "city": "Yogyakarta", "latitude": null, "optLock": 1, "vendorId": null, "employeeId": null, "suspended": false, "taxLocation": true, "deleted": false, "province": "", "warehouseId": null, "street": "<PERSON><PERSON><PERSON>an", "customerId": 231939, "name": "Ds testing", "concatFullAddress": "<PERSON><PERSON><PERSON>an <PERSON>", "id": 248575, "projectId": null, "headQuarter": false, "longitude": null}, {"country": "Indonesia", "branchId": null, "zipCode": "", "notes": "Ds testing", "address": "<PERSON><PERSON><PERSON>an\n<PERSON>", "city": "Yogyakarta", "latitude": null, "optLock": 1, "vendorId": null, "employeeId": null, "suspended": false, "taxLocation": false, "deleted": false, "province": "", "warehouseId": null, "street": "<PERSON><PERSON><PERSON>an", "customerId": 231939, "name": "Ds testing", "concatFullAddress": "<PERSON><PERSON><PERSON>an <PERSON>", "id": 248574, "projectId": null, "headQuarter": false, "longitude": null}], "salesman": null, "defaultInvoiceDesc": null, "charField10": null, "notesIdTax": null, "countryCode": null, "shipAddressId": 248574, "wpName": "Ds testing", "customerTaxType": "BKN_PEMUNGUT_PPN", "salesman4Id": null, "taxSameAsBill": false, "customerLimitAgeValue": null, "taxAddressId": 248575, "salesman2Id": null, "defaultSalesmanId": null, "priceCategoryId": 50, "billAddressId": 248573, "defaultWarehouseId": null, "reseller": false, "customerNoVa": null, "shipAddress": {"country": "Indonesia", "branchId": null, "zipCode": "", "notes": "Ds testing", "address": "<PERSON><PERSON><PERSON>an\n<PERSON>", "city": "Yogyakarta", "latitude": null, "optLock": 1, "vendorId": null, "employeeId": null, "suspended": false, "taxLocation": false, "deleted": false, "province": "", "warehouseId": null, "street": "<PERSON><PERSON><PERSON>an", "customerId": 231939, "name": "Ds testing", "concatFullAddress": "<PERSON><PERSON><PERSON>an <PERSON>", "id": 248574, "projectId": null, "headQuarter": false, "longitude": null}, "numericField9": null, "defaultSalesDisc": null, "numericField8": null, "numericField7": null, "numericField6": null, "numericField5": null, "numericField4": null, "efakturSendEmail": null, "numericField3": null, "numericField2": null, "numericField1": null, "nitku": null, "customerLimitAmount": false, "discountCategoryId": 50, "customerNo": "G14170", "categoryId": 102000}, "reverseInvoiceStatusName": "", "detailTax": []}