<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
// use Faker\Factory as Faker;
use App\Models\Customer;
use App\Models\Address;
use App\Models\Store;
use App\Models\Area;

class CustomersTablesSeeder extends Seeder
{
    public function run()
    {
        $jsonUrl = 'database/data/customers_palagan.json';
        $jsonString = file_get_contents(base_path($jsonUrl));
        $customersPalagan = json_decode($jsonString, true);

        foreach ($customersPalagan as $item) {
            $data = [];
            $store = Store::where('slug', 'palagan')->first();
            $store_id = $store->id;
            $address_note = false;
            foreach ($item as $key => $val) {
                if ($key == 'address_note') {
                    if ($val != '') {
                        $address_note = $val;
                    }
                } elseif ($key == 'phone') {
                    $phone = str_replace(' ', '', $val);
                    $phone = str_replace('-', '', $phone);
                    $phone = str_replace('+', '', $phone);
                    // if (substr($phone,0,2) == '62') {
                    //   $phone = '0'.substr($phone,2);
                    // }
                    if (substr($phone, 0, 1) == '0') {
                        $phone = '62'.substr($phone, 1);
                    }
                    if (substr($phone, 0, 1) == '8') {
                        $phone = '62'.$phone;
                    }
                    $data[$key] = $phone;
                } else {
                    $data[$key] = $val;
                }
            }
            if ($address_note) {
                $data['address'] = $data['address'].' ('.$address_note.')';
            }
            $address = $data['address'];
            $latlng = $data['latlng'];
            unset($data['address']);
            unset($data['latlng']);
            $customer = Customer::where('phone', $data['phone'])->first();
            if ($customer) {
                $label = $customer['name'];
            } else {
                $label = 'Alamat Utama';
                $customer = Customer::create($data);
            }
            Address::create([
                'customer_id' => $customer['id'],
                'store_id' => $store_id,
                'label' => $label,
                'latlng' => $latlng,
                'address' => $address,
            ]);
        }


        // $jsonUrl = 'database/data/customers_kemetiran.json';
        // $jsonString = file_get_contents(base_path($jsonUrl));
        // $customersPalagan = json_decode($jsonString, true);

        // foreach ($customersPalagan as $item) {
        //     $data = [];
        //     $store = Store::where('slug', 'kemetiran')->first();
        //     $store_id = $store->id;
        //     $address_note = false;
        //     foreach ($item as $key => $val) {
        //         if ($key == 'address_note') {
        //             if ($val != '') {
        //                 $address_note = $val;
        //             }
        //         } elseif ($key == 'phone') {
        //             $phone = str_replace(' ', '', $val);
        //             $phone = str_replace('-', '', $phone);
        //             $phone = str_replace('+', '', $phone);
        //             $phone = str_replace('(', '', $phone);
        //             $phone = str_replace(')', '', $phone);
        //             // if (substr($phone,0,2) == '62') {
        //             //   $phone = '0'.substr($phone,2);
        //             // }
        //             if (substr($phone, 0, 1) == '0') {
        //                 $phone = '62'.substr($phone, 1);
        //             }
        //             if (substr($phone, 0, 1) == '8') {
        //                 $phone = '62'.$phone;
        //             }
        //             $data[$key] = $phone;
        //         } else {
        //             $data[$key] = $val;
        //         }
        //     }
        //     if ($address_note) {
        //         $data['address'] = $data['address'].' ('.$address_note.')';
        //     }
        //     $address = $data['address'];
        //     $latlng = $data['latlng'];
        //     unset($data['address']);
        //     unset($data['latlng']);
        //     $customer = Customer::where('phone', $data['phone'])->first();
        //     if ($customer) {
        //         $label = $customer['name'];
        //     } else {
        //         $label = 'Alamat Utama';
        //         $customer = Customer::create($data);
        //     }
        //     Address::create([
        //       'customer_id' => $customer['id'],
        //       'store_id' => $store_id,
        //       'label' => $label,
        //       'latlng' => $latlng,
        //       'address' => $address,
        //     ]);
        // }

        Area::create([
            'name' => 'Green Hills',
            'latlng' => '-7.7193771,110.3930584',
        ]);

        Area::create([
            'name' => 'Pesona Merapi',
            'latlng' => '-7.7237193,110.3913694',
        ]);
    }
}