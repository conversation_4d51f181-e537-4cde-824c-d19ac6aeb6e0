<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class PermissionRoleTableSeeder extends Seeder
{
    public function run()
    {
        // Super Admin
        // ----------------------------------------
        $superadmin_permissions = Permission::all();
        Role::findOrFail(1)->permissions()->sync($superadmin_permissions->pluck('id'));

        // Owner
        // ----------------------------------------
        $owner_permissions = $superadmin_permissions->filter(function ($permission) {
            return $permission->title != 'super_admin';
        });
        Role::findOrFail(2)->permissions()->sync($owner_permissions);

        // CS
        // ----------------------------------------
        $cs_permissions = $owner_permissions->filter(function ($permission) {
            return
                substr($permission->title, 0, 5) != 'role_' &&
                substr($permission->title, 0, 11) != 'permission_';
        });
        Role::findOrFail(3)->permissions()->sync($cs_permissions);

        // Admin Toko
        // ----------------------------------------
        $admin_permissions = $owner_permissions->filter(function ($permission) {
            return
                substr($permission->title, 0, 6) == 'order_' ||
                substr($permission->title, 0, 9) == 'feedback_' ||
                substr($permission->title, 0, 9) == 'customer_' ||
                substr($permission->title, 0, 8) == 'address_';
        });
        Role::findOrFail(4)->permissions()->sync($admin_permissions);

        // Driver
        // ----------------------------------------
        Role::findOrFail(5)->permissions()->sync($admin_permissions);

        // Admin Toko & Driver
        // ----------------------------------------
        Role::findOrFail(6)->permissions()->sync($admin_permissions);
    }
}