<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UsersTableSeeder extends Seeder
{
    public function run()
    {
        $users = [
            [
                'name'           => 'Super Admin',
                'email'          => '<EMAIL>',
                'password'       => bcrypt('123123'),
                'role_id'        => 1,
                'store_id'       => null,
            ],
            [
                'name'           => 'Owner',
                'email'          => '<EMAIL>',
                'password'       => bcrypt('123123'),
                'role_id'        => 2,
                'store_id'       => null,
            ],
            [
                'name'           => 'CS',
                'email'          => '<EMAIL>',
                'password'       => bcrypt('123123'),
                'role_id'        => 3,
                'store_id'       => null,
            ],
            [
                'name'           => 'Admin Toko',
                'email'          => '<EMAIL>',
                'password'       => bcrypt('123123'),
                'role_id'        => 4,
                'store_id'       => 1,
            ],
            [
                'name'           => 'Driver',
                'email'          => '<EMAIL>',
                'password'       => bcrypt('123123'),
                'role_id'        => 5,
                'store_id'       => 1,
            ],
            [
                'name'           => 'Gasplus',
                'email'          => '<EMAIL>',
                'password'       => bcrypt('gasplus876521'),
                'role_id'        => 3,
                'store_id'       => null,
            ],
            [
                'name'           => 'Niko Okta',
                'email'          => '<EMAIL>',
                'password'       => bcrypt('Bismill4h'),
                'role_id'        => 1,
                'store_id'       => null,
            ],
            [
                'name'           => 'Admin + Driver',
                'email'          => '<EMAIL>',
                'password'       => bcrypt('123123'),
                'role_id'        => 6,
                'store_id'       => null,
            ],
        ];

        foreach ($users as $value) {
            User::create($value);
        }
    }
}