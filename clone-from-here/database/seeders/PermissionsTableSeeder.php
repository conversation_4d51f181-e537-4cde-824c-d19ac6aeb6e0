<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionsTableSeeder extends Seeder
{
    public function run()
    {
        $permissions = [
            ['title'      => 'super_admin'],
            // Permission
            // -----------------------------------------------
            ['title'      => 'permission_create'],
            ['title'      => 'permission_edit'],
            ['title'      => 'permission_show'],
            ['title'      => 'permission_delete'],
            ['title'      => 'permission_access'],
            // Role
            // -----------------------------------------------
            ['title'      => 'role_create'],
            ['title'      => 'role_edit'],
            ['title'      => 'role_show'],
            ['title'      => 'role_delete'],
            ['title'      => 'role_access'],
            // User
            // -----------------------------------------------
            ['title'      => 'user_create'],
            ['title'      => 'user_edit'],
            ['title'      => 'user_show'],
            ['title'      => 'user_delete'],
            ['title'      => 'user_access'],
            // Product
            // -----------------------------------------------
            ['title'      => 'product_create'],
            ['title'      => 'product_edit'],
            ['title'      => 'product_show'],
            ['title'      => 'product_delete'],
            ['title'      => 'product_access'],
            // Category
            // -----------------------------------------------
            ['title'      => 'category_create'],
            ['title'      => 'category_edit'],
            ['title'      => 'category_show'],
            ['title'      => 'category_delete'],
            ['title'      => 'category_access'],
            // City
            // -----------------------------------------------
            ['title'      => 'city_create'],
            ['title'      => 'city_edit'],
            ['title'      => 'city_show'],
            ['title'      => 'city_delete'],
            ['title'      => 'city_access'],
            // Store
            // -----------------------------------------------
            ['title'      => 'store_create'],
            ['title'      => 'store_edit'],
            ['title'      => 'store_show'],
            ['title'      => 'store_delete'],
            ['title'      => 'store_access'],
            // Bank
            // -----------------------------------------------
            ['title'      => 'bank_create'],
            ['title'      => 'bank_edit'],
            ['title'      => 'bank_show'],
            ['title'      => 'bank_delete'],
            ['title'      => 'bank_access'],
            // Status Order
            // -----------------------------------------------
            ['title'      => 'status_order_create'],
            ['title'      => 'status_order_edit'],
            ['title'      => 'status_order_show'],
            ['title'      => 'status_order_delete'],
            ['title'      => 'status_order_access'],
            // Order
            // -----------------------------------------------
            ['title'      => 'order_create'],
            ['title'      => 'order_edit'],
            ['title'      => 'order_show'],
            ['title'      => 'order_delete'],
            ['title'      => 'order_access'],
            // Feedback
            // -----------------------------------------------
            ['title'      => 'feedback_create'],
            ['title'      => 'feedback_edit'],
            ['title'      => 'feedback_show'],
            ['title'      => 'feedback_delete'],
            ['title'      => 'feedback_access'],
            // Customer
            // -----------------------------------------------
            ['title'      => 'customer_create'],
            ['title'      => 'customer_edit'],
            ['title'      => 'customer_show'],
            ['title'      => 'customer_delete'],
            ['title'      => 'customer_access'],
            // Address
            // -----------------------------------------------
            ['title'      => 'address_create'],
            ['title'      => 'address_edit'],
            ['title'      => 'address_show'],
            ['title'      => 'address_delete'],
            ['title'      => 'address_access'],
            // Deposit
            // -----------------------------------------------
            ['title'      => 'deposit_create'],
            ['title'      => 'deposit_edit'],
            ['title'      => 'deposit_show'],
            ['title'      => 'deposit_delete'],
            ['title'      => 'deposit_access'],
            // Chat
            // -----------------------------------------------
            ['title'      => 'chat_create'],
            ['title'      => 'chat_edit'],
            ['title'      => 'chat_show'],
            ['title'      => 'chat_delete'],
            ['title'      => 'chat_access'],
            // Chat Label
            // -----------------------------------------------
            ['title'      => 'chat_label_create'],
            ['title'      => 'chat_label_edit'],
            ['title'      => 'chat_label_show'],
            ['title'      => 'chat_label_delete'],
            ['title'      => 'chat_label_access'],
            // Chat WhatsApp
            // -----------------------------------------------
            ['title'      => 'chat_whatsapp_create'],
            ['title'      => 'chat_whatsapp_edit'],
            ['title'      => 'chat_whatsapp_show'],
            ['title'      => 'chat_whatsapp_delete'],
            ['title'      => 'chat_whatsapp_access'],
            // Quick Reply
            // -----------------------------------------------
            ['title'      => 'quick_reply_create'],
            ['title'      => 'quick_reply_edit'],
            ['title'      => 'quick_reply_show'],
            ['title'      => 'quick_reply_delete'],
            ['title'      => 'quick_reply_access'],
        ];

        foreach ($permissions as $value) {
            Permission::create($value);
        }
    }
}