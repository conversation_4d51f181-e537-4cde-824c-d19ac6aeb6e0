<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RolesTableSeeder extends Seeder
{
    public function run()
    {
        $roles = [
            [
                'title'      => 'Super Admin',
            ],
            [
                'title'      => 'Owner',
            ],
            [
                'title'      => 'CS',
            ],
            [
                'title'      => 'Admin Toko',
            ],
            [
                'title'      => 'Driver',
            ],
            [
                'title'      => 'Admin Toko & Driver',
            ]
        ];

        foreach ($roles as $value) {
            Role::create($value);
        }
    }
}