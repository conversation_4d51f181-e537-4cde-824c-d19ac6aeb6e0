<?php

namespace App\Helper;

use DateTime;
use App\Models\User;
use App\Models\Order;
use App\Models\Store;
use App\Models\Total;
use App\Models\Status;
use App\Models\Address;
use App\Models\Deposit;
use App\Models\Invoice;
use App\Models\Setting;
use App\Models\Customer;
use Maestroerror\HeicToJpg;
use App\Models\Notification;
use App\Models\OrderProduct;
use App\Models\AdditionalCost;
// use Illuminate\Filesystem\Filesystem;
use App\Models\PpobCustomerData;
// use Spatie\ImageOptimizer\OptimizerChainFactory;
use Illuminate\Support\Facades\File;
use Illuminate\Database\Eloquent\Builder;
use Intervention\Image\ImageManagerStatic as Image;
// use Illuminate\Support\Facades\DB;

// use App\Jobs\ProcessNotif;

class Helper
{
    public function miniEncrypt($string)
    {
        $key = env('APP_KEY');
        $cipher = 'AES-128-ECB'; // Short encryption method
        $encrypted = openssl_encrypt($string, $cipher, $key, OPENSSL_RAW_DATA);
        return rtrim(strtr(base64_encode($encrypted), '+/', '-_'), '='); // URL-safe encoding
    }

    public function miniDecrypt($encryptedString)
    {
        $key = env('APP_KEY');
        $cipher = 'AES-128-ECB';
        $decoded = base64_decode(strtr($encryptedString, '-_', '+/')); // Reverse transformation
        return openssl_decrypt($decoded, $cipher, $key, OPENSSL_RAW_DATA);
    }

    public function accurateGetDBSession($company_id)
    {
        $accurate_token = Setting::where('name', 'accurate_token')->first();

        $params = [
            'id' => $company_id,
        ];
        $url = 'https://account.accurate.id/api/open-db.do?' . http_build_query($params);
        $opts = array(
            'http' =>
            array(
                'method'  => 'GET',
                'header'  => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'Authorization: Bearer ' . $accurate_token->value['access_token'],
                ],
            )
        );
        $context  = stream_context_create($opts);
        $result_session = @file_get_contents($url, false, $context);
        if ($result_session === FALSE) {
            return null;
        }
        $result_session = json_decode($result_session);
        if (!empty($result_session->error_message)) {
            return $result_session->error_message;
        }
        $result_session->company_id = $company_id;
        // ? Save Accurate DB Session
        Setting::updateOrCreate(
            [
                'name' => 'accurate_session',
            ],
            ['value' => $result_session,]
        );
        return $result_session;
    }

    public function accurateGetDBList()
    {
        $accurate_token = Setting::where('name', 'accurate_token')->first();

        if ($accurate_token) {
            // ? Fetch Accurate DB List
            $url = 'https://account.accurate.id/api/db-list.do';
            $opts = array(
                'http' =>
                array(
                    'method'  => 'GET',
                    'header'  => [
                        'Content-Type: application/x-www-form-urlencoded',
                        'Authorization: Bearer ' . $accurate_token->value['access_token'],
                    ],
                )
            );
            $context  = stream_context_create($opts);
            $result_db_list = @file_get_contents($url, false, $context);
            if ($result_db_list === FALSE) {
                return [];
            }
            $result_db_list = json_decode($result_db_list);
            if (!empty($result_db_list->error_message)) {
                return $result_db_list->error_message;
            }
            return $result_db_list->d;
        } else {
            return [];
        }
    }

    public function accurateGetToken($code)
    {
        // ? Fetch Accurate OAuth Token
        $url = 'https://account.accurate.id/oauth/token';
        $data = [
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => env('APP_URL') . '/accurate-callback',
        ];
        $postdata = http_build_query($data);
        $opts = array(
            'http' =>
            array(
                'method'  => 'POST',
                'header'  => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'Authorization: Basic ' . base64_encode(env('ACCURATE_CLIENT_ID') . ':' . env('ACCURATE_CLIENT_SECRET')),
                ],
                'content' => $postdata
            )
        );
        $context  = stream_context_create($opts);
        $result = @file_get_contents($url, false, $context);
        if ($result === FALSE) {
            return null;
        }
        $result = json_decode($result);
        if (!empty($result->error_message)) {
            return $result->error_message;
        }
        $result->created_at = time();
        // ? Save Accurate OAuth Token
        Setting::updateOrCreate(
            [
                'name' => 'accurate_token',
            ],
            ['value' => $result,]
        );
        return $result;
    }
    public function fetchApiAccurate($path, $method, $params, $body, $is_all_page = false, $is_exact_host = false, $is_return_error = false)
    {
        if (!(bool)env('ACCURATE_SYNC')) return null;

        // return 'WIP';
        $accurate_token = Setting::where('name', 'accurate_token')->first();
        $accurate_session = Setting::where('name', 'accurate_session')->first();

        if ($accurate_token && $accurate_session) {
            if (array_key_exists('session', $accurate_session->value['d'])) {
                $company_id = $accurate_session->value['company_id'];
                $accurate_session = $accurate_session->value['d'];
                $accurate_session['company_id'] = $company_id;
            } else {
                $accurate_session = $accurate_session->value;
            }

            // ? Check if Token need to refresh
            $is_token_need_refresh = false;
            if (array_key_exists('created_at', $accurate_token->value)) {
                $current_time = time();
                $created_at = $accurate_token->value['created_at'];
                $created_at = strtotime('+13 days', $created_at);
                if ($current_time > $created_at) $is_token_need_refresh = true;
            } else {
                $is_token_need_refresh = true;
            }
            if ($is_token_need_refresh) {
                // ? Refresh Accurate OAuth Token
                $url = 'https://account.accurate.id/oauth/token';
                $data = [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $accurate_token->value['refresh_token'],
                ];
                $postdata = http_build_query($data);
                $opts = array(
                    'http' =>
                    array(
                        'method'  => 'POST',
                        'header'  => [
                            'Content-Type: application/x-www-form-urlencoded',
                            'Authorization: Basic ' . base64_encode(env('ACCURATE_CLIENT_ID') . ':' . env('ACCURATE_CLIENT_SECRET')),
                        ],
                        'content' => $postdata
                    )
                );
                $context  = stream_context_create($opts);
                $result_token = @file_get_contents($url, false, $context);
                if ($result_token === FALSE) {
                    return null;
                }
                $result_token = json_decode($result_token);
                if (!empty($result_token->error_message)) {
                    return null;
                }
                $result_token->created_at = time();
                // ? Save Accurate OAuth Token
                Setting::updateOrCreate(
                    [
                        'name' => 'accurate_token',
                    ],
                    ['value' => $result_token,]
                );
                $accurate_token->value = $result_token;
            }

            // ? Check if Session need to refresh
            $current_time = time();

            $session_accessible =  $accurate_session['accessibleUntil'];
            $session_accessible = str_replace('/', '-', $session_accessible);
            $session_accessible = strtotime($session_accessible);
            $session_accessible = strtotime('-2 days', $session_accessible);
            // if (true) {
            if ($current_time > $session_accessible) {
                // ? Refresh Accurate OAuth Session
                $params_session = [
                    'id' => $accurate_session['company_id'],
                    'session' => $accurate_session['session'],
                ];
                $url = 'https://account.accurate.id/api/db-refresh-session.do?' . http_build_query($params_session);
                $opts = array(
                    'http' =>
                    array(
                        'method'  => 'GET',
                        'header'  => [
                            'Content-Type: application/x-www-form-urlencoded',
                            'Authorization: Bearer ' . $accurate_token->value['access_token'],
                        ],
                    )
                );
                $context  = stream_context_create($opts);
                $result_session = @file_get_contents($url, false, $context);
                if ($result_session === FALSE) {
                    return null;
                }
                $result_session = json_decode($result_session);
                if (!empty($result_session->error_message)) {
                    return $result_session->error_message;
                }
                $result_session->company_id = $accurate_session['company_id'];
                $result_session->d = (array)$result_session->d;
                // ? Save Accurate OAuth Session
                Setting::updateOrCreate(
                    [
                        'name' => 'accurate_session',
                    ],
                    ['value' => $result_session,]
                );
                if (array_key_exists('session', $result_session->d)) {
                    $accurate_session = $result_session->d;
                    $accurate_session['company_id'] = $company_id;
                } else {
                    $accurate_session = $result_session;
                }
            }
            // return 'berhasil';

            // ? Fetch API
            // $params = [
            //     'fields' => 'id,name',
            // ];

            if ($is_all_page) {
                $all_data = [];
                $page = 0;
                do {
                    $url = $is_exact_host ? $path : $accurate_session['host'] . $path;
                    $params['sp.page'] = $page++;
                    if (!empty($params)) {
                        $url .= '?' . http_build_query($params);
                    }
                    $http = [
                        'method'  => $method,
                        'header'  => [
                            'Content-Type: application/x-www-form-urlencoded',
                            'Authorization: Bearer ' . $accurate_token->value['access_token'],
                            'X-Session-ID: ' . $accurate_session['session'],
                        ],
                    ];
                    if (!empty($body)) {
                        $data = http_build_query($body);
                        $http['content'] = $data;
                    }
                    $opts = [
                        'http' => $http,
                    ];
                    $context  = stream_context_create($opts);
                    $result = @file_get_contents($url, false, $context);
                    if ($result === FALSE) {
                        return null;
                    }
                    $result = json_decode($result);
                    if ($result && !$result->s) {
                        return null;
                    }
                    if (!empty($result->error_message)) {
                        return $result->error_message;
                    }
                    $all_data = array_merge($all_data, $result->d);

                    $next = false;
                    if (isset($result->sp) && !empty($result->sp) && $result->sp->page < $result->sp->pageCount) {
                        $next = true;
                    }
                } while ($next);

                return (object)[
                    's' => true,
                    'd' => $all_data,
                    'sp' => isset($result->sp) ? $result->sp : null,
                ];
            } else {
                $url = $is_exact_host ? $path : $accurate_session['host'] . $path;
                if (!empty($params)) {
                    $url .= '?' . http_build_query($params);
                }
                $http = [
                    'method'  => $method,
                    'header'  => [
                        'Content-Type: application/x-www-form-urlencoded',
                        'Authorization: Bearer ' . $accurate_token->value['access_token'],
                        'X-Session-ID: ' . $accurate_session['session'],
                    ],
                ];
                if (!empty($body)) {
                    $data = http_build_query($body);
                    $http['content'] = $data;
                }
                $opts = [
                    'http' => $http,
                ];
                $context  = stream_context_create($opts);
                $result = @file_get_contents($url, false, $context);
                if ($result === FALSE) {
                    if ($is_return_error) {
                        return (object)[
                            's' => false,
                            'd' => 'Error fetchApiAccurate',
                        ];
                    }
                    return null;
                }
                $result = json_decode($result);
                if ((is_object($result) && property_exists($result, 's') && !$result->s)) {
                    if ($is_return_error && property_exists($result, 'd') && $result->d) {
                        if (is_array($result->d)) {
                            return (object)[
                                's' => false,
                                'd' => $result->d[0],
                            ];
                        }
                        return (object)[
                            's' => false,
                            'd' => $result->d,
                        ];
                    }
                    return null;
                }
                if (!empty($result->error_message)) {
                    if ($is_return_error) {
                        return (object)[
                            's' => false,
                            'd' => $result->error_message,
                        ];
                    }
                    return null;
                }
                if (!$result) {
                    if ($is_return_error) {
                        return (object)[
                            's' => false,
                            'd' => 'Error fetchApiAccurate',
                        ];
                    }
                    return null;
                }
                return $result;
            }
        }
    }

    public function fetchBasic($fullurl, $method, $params = null, $body = [], $header = null)
    {

        $url = $fullurl;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        $http = [
            'method'  => $method,
            // 'header'  => [
            //     'Content-Type: application/x-www-form-urlencoded',
            //     'Authorization: Bearer ' . $accurate_token->value['access_token'],
            //     'X-Session-ID: ' . $accurate_session['session'],
            // ],
        ];
        if (!empty($header)) {
            $http['header'] = $header;
        }
        if (!empty($body)) {
            $data = http_build_query($body);
            $http['content'] = $data;
        }
        $opts = [
            'http' => $http,
        ];
        $context  = stream_context_create($opts);
        $result = @file_get_contents($url, false, $context);
        if ($result === FALSE) {
            return null;
        }
        $result = json_decode($result);
        return $result;
    }

    public function accurateDeleteCustomer($address)
    {
        if ($address && $address->accurate_customer_id) {
            $params = [
                'id' => $address->accurate_customer_id,
            ];
            $body = [];
            $result_delete = $this->fetchApiAccurate('/accurate/api/customer/delete.do', 'DELETE', $params, $body);
            if (is_object($result_delete) && !$result_delete->s) {
                $result_delete = $this->accurateUpsertCustomer($address, false, true);
                if ($result_delete) {
                    return true;
                }
            }
            if ($result_delete) {
                return $result_delete->s;
            }
            return false;
        }
        return false;
    }

    public function accurateUpsertCustomer($address, $is_only_creation = false, $is_deletion = false)
    {
        $params = [];
        $body = [];
        // $address = Address::where('accurate_customer_id', $accurate_customer_id)->withTrashed()->with('customer')->first();
        if ($address) {
            $customer = $address->customer;
            if ($is_only_creation && !empty($address->accurate_customer_id)) return null;
            if (!$is_only_creation && empty($address->accurate_customer_id)) {

                // ? Get Accurate Customer ID


                return null;
            }
            if (!empty($address->accurate_customer_id)) {
                $body['id'] = $address->accurate_customer_id;
            }
            $body['name'] = $customer->name;
            $body['mobilePhone'] = $customer->phone;
            $body['categoryName'] = 'RT';
            $body['billCountry'] = 'Indonesia';
            $body['shipCountry'] = 'Indonesia';
            $body['taxCountry'] = 'Indonesia';
            $body['shipSameAsBill'] = true;
            $body['taxSameAsBill'] = true;
            $body['currencyCode'] = 'IDR';
            $body['billCity'] = $address->store->city->name;
            $body['shipCity'] = $address->store->city->name;
            $body['taxCity'] = $address->store->city->name;
            $body['billStreet'] = $address->address;
            $body['shipStreet'] = $address->address;
            $body['taxStreet'] = $address->address;
            if (!empty($address->store->accurate_branch_id)) {
                $body['branchId'] = $address->store->accurate_branch_id;
            }
            if (!empty($customer->email)) {
                $body['email'] = $customer->email;
            }
            if (!empty($customer->note_special)) {
                $body['notes'] = $customer->note_special;
            }
            if ($is_deletion && !$is_only_creation && $address->accurate_customer_id) {
                $body['name'] .= ' (DELETED at ' . date('Y-m-d H:i:s') . ')';
                // $body['email'] .= ' (DELETED at ' . date('Y-m-d H:i:s') . ')';
                $body['mobilePhone'] .= ' (DELETED at ' . date('Y-m-d H:i:s') . ')';
                // $body['notes'] .= ' (DELETED at ' . date('Y-m-d H:i:s') . ')';
            }
            // return $body;
            $result_save = $this->fetchApiAccurate('/accurate/api/customer/save.do', 'POST', $params, $body);
            if (is_object($result_save) && $result_save->s) {
                $accurate_data = $result_save->r;
                $address->synced_at = date('Y-m-d H:i:s');
                $address->accurate_customer_id = $accurate_data->id;
                $address->accurate_customer_code = $accurate_data->customerNo;
                // if (!$is_deletion) {
                $address->saveQuietly();
                // }
                return $address;
            }
        }
        return null;
    }

    public function accurateDeleteInvoice($order)
    {
        if ($order && $order->accurateInvoice && !empty($order->accurateInvoice->accurate_id)) {

            // ? Delete receipt first if exist
            if ($order->accurateReceipt && !empty($order->accurateReceipt->accurate_id)) {
                $params = [
                    'id' => $order->accurateReceipt->accurate_id,
                ];
                $body = [];
                $result_delete = $this->fetchApiAccurate('/accurate/api/sales-receipt/delete.do', 'DELETE', $params, $body);
                // if (!$result_delete) return false;
            }

            // ? Delete invoice
            $params = [
                'id' => $order->accurateInvoice->accurate_id,
            ];
            $body = [];
            $result_delete = $this->fetchApiAccurate('/accurate/api/sales-invoice/delete.do', 'DELETE', $params, $body);
            // return $result_delete->s;
        }
        // return false;
    }

    public function accurateUpsertReceipt($order, $is_only_creation = false)
    {
        $order = $order->fresh()->load([
            'products',
            'orderproducts',
            'additionalcosts'
        ]);
        $params = [];
        $body = [];

        if (!$order) {
            // throw new Exception($order->id . ' ~ Order not found');
            return null;
        } else {
            if (!$order->accurateInvoice) {
                // throw new Exception($order->id . ' ~ Accurate invoice is empty');
                return null;
                // $this->fail();
                // $this->fail([
                //     'order_id' => $order->id,
                //     "message" => "Accurate invoice is empty!"
                // ]);
            } else if (!$order->store->accurate_cash_glaccount_id) {
                // throw new Exception($order->id . ' ~ Accurate gl account is empty');
                return null;
                // $this->fail();
                // $this->fail([
                //     'order_id' => $order->id,
                //     "message" => "Accurate gl account is empty!"
                // ]);
            } else if (!$order->address->accurate_customer_code) {
                // throw new Exception($order->id . ' ~ Accurate customer code is empty');
                return null;
                // $this->fail();
                // $this->fail(
                //     [
                //         'order_id' => $order->id,
                //         "message" => "Accurate customer code is empty!"
                //     ]
                // );
            } else if (!$order->store->accurate_branch_id) {
                // throw new Exception($order->id . ' ~ Accurate branch id is empty');
                return null;
                // $this->fail();
                // $this->fail([
                //     'order_id' => $order->id,
                //     "message" => "Accurate branch id is empty!"
                // ]);
            }
        }
        $helper = new Helper;

        // ? Success
        $invoice_number = $order->accurateInvoice->accurate_no;
        $params = [];
        $body = [
            'bankNo' => $order->store->accurate_cash_glaccount_id, // * Kas Toko Kabupaten
            'customerNo' => $order->address->accurate_customer_code, // * Aan (Toko Tas Ayu Collection jl Jambon)
            'chequeAmount' => $order->total,
            // 'detailInvoice[n].id' => 00000, // * Only to edit item (NOT REQUIRED)
            'detailInvoice[0].invoiceNo' => $invoice_number,
            'detailInvoice[0].paymentAmount' => $order->total,
            // 'detailInvoice[n]._status' => 'delete', // * Only to delete item (NOT REQUIRED)
            'transDate' => date('d/m/Y'),
            // 'chequeDate' => date('d/m/Y'), // * (NOT REQUIRED)
            'branchId' => $order->store->accurate_branch_id, // * Branch Gasplus Kabupaten
            // 'currencyCode' => 'IDR', // * (NOT REQUIRED)
            // 'description' => 'Test catatan 01', // * (NOT REQUIRED)
            'paymentMethod' => 'CASH_OTHER', // * (NOT REQUIRED)
        ];
        $result_create_receipt = $helper->fetchApiAccurate('/accurate/api/sales-receipt/save.do', 'POST', $params, $body);
        $dataAccurateReceipt = [];
        $dataAccurateReceipt['accuratable_key'] = 'sales-receipt';
        if (is_object($result_create_receipt) && $result_create_receipt->s) {
            $accurate_data = $result_create_receipt->r;
            $dataAccurateReceipt['error_message'] = null;
            $dataAccurateReceipt['synced_at'] = date('Y-m-d H:i:s');
            $dataAccurateReceipt['accurate_id'] = $accurate_data->id;
            $dataAccurateReceipt['accurate_no'] = $accurate_data->number;
        } else if (is_object($result_create_receipt) && !$result_create_receipt->s && isset($result_create_receipt->d)) {
            $dataAccurateReceipt['error_message'] = is_array($result_create_receipt->d) ? $result_create_receipt->d[0] : $result_create_receipt->d;
            // throw new Exception($order->id . ' ~ ' . $dataAccurateReceipt['error_message']);
            // $this->fail();
            // $this->fail($dataAccurateReceipt);
        } else {
            $dataAccurateReceipt['error_message'] = 'Error fetchApiAccurate';
            // throw new Exception($order->id . ' ~ ' . $dataAccurateReceipt['error_message']);
            // $this->fail();
            // $this->fail($dataAccurateReceipt);
        }
        if ($order->accurateReceipt) {
            $order->accurateReceipt()->update($dataAccurateReceipt);
        } else {
            $order->accurateReceipt()->create($dataAccurateReceipt);
        }
        return $result_create_receipt;
    }

    public function accurateUpsertInvoice($order, $is_only_creation = false)
    {
        $order = $order->fresh()->load([
            'products',
            'orderproducts',
            'additionalcosts',
            'address',
            'store',
            'accurateInvoice',
        ]);
        $params = [];
        $body = [];
        if ($order && $order->address->accurate_customer_code && $order->store->accurate_warehouse_name && $order->store->accurate_branch_id) {
            if (!$is_only_creation && empty($order->accurateInvoice->accurate_id)) return null;

            // Build request body
            $body = $this->buildInvoiceRequestBody($order, $is_only_creation);

            // Make API call
            $result_create_faktur = $this->fetchApiAccurate('/accurate/api/sales-invoice/save.do', 'POST', $params, $body);

            // Check for duplicates in response
            if (is_object($result_create_faktur) && $result_create_faktur->s && isset($result_create_faktur->r->detailItem)) {
                $hasDuplicates = $this->checkForDuplicateItems($result_create_faktur->r->detailItem);

                if ($hasDuplicates) {
                    // Rebuild request body with corrected data
                    $body = $this->buildInvoiceRequestBody($order, $is_only_creation, true);

                    // Make another API call with corrected data
                    $result_create_faktur = $this->fetchApiAccurate('/accurate/api/sales-invoice/save.do', 'POST', $params, $body);
                }
            }

            // Process response and update database
            $dataAccurateInvoice = $this->processInvoiceResponse($result_create_faktur, $order);

            return $result_create_faktur;
        }
        return null;
    }

    private function buildInvoiceRequestBody($order, $is_only_creation = false, $preventDuplicates = false)
    {
        $body = [];

        if ($order->accurateInvoice && $order->accurateInvoice->accurate_id) {
            $body['id'] = $order->accurateInvoice->accurate_id;
        }

        $body['customerNo'] = $order->address->accurate_customer_code;
        $body['transDate'] = date_format(date_create($order->created_at), 'd/m/Y');

        if (!empty($order->received_at)) {
            $body['shipDate'] = date_format(date_create($order->received_at), 'd/m/Y');
        }

        $body['branchId'] = $order->store->accurate_branch_id;
        $body['currencyCode'] = 'IDR';

        if (!empty($order->note)) {
            $body['description'] = $order->note;
        }

        $body['documentCode'] = 'DIGUNGGUNG';

        $iteration = 0;
        $processedItems = [];

        // Add products
        foreach ($order->products as $product) {
            $itemKey = $product->code . '_' . $product->pivot->qty . '_' . $product->pivot->price;

            // Skip duplicates if prevention is enabled
            if ($preventDuplicates && in_array($itemKey, $processedItems)) {
                continue;
            }

            if (!$is_only_creation && $product->pivot->accurate_invoice_item_id) {
                $body['detailItem[' . $iteration . '].id'] = $product->pivot->accurate_invoice_item_id;
            }

            $body['detailItem[' . $iteration . '].itemNo'] = $product->code;
            $body['detailItem[' . $iteration . '].unitPrice'] = $product->pivot->price;
            $body['detailItem[' . $iteration . '].quantity'] = $product->pivot->qty;
            $body['detailItem[' . $iteration . '].warehouseName'] = $order->store->accurate_warehouse_name;

            $processedItems[] = $itemKey;
            $iteration++;
        }

        // Add deleted products
        $order_product_deleted_list = OrderProduct::where('order_id', $order->id)
            ->whereNotNull('deleted_at')
            ->whereNotNull('accurate_invoice_item_id')
            ->withTrashed()
            ->get();

        foreach ($order_product_deleted_list as $product_deleted) {
            $body['detailItem[' . $iteration . '].id'] = $product_deleted->accurate_invoice_item_id;
            $body['detailItem[' . $iteration . ']._status'] = 'delete';
            $iteration++;
        }

        // Add additional costs
        foreach ($order->additionalcosts as $additionalcost) {
            $itemKey = $additionalcost->code . '_1_' . $additionalcost->total_cost;

            // Skip duplicates if prevention is enabled
            if ($preventDuplicates && in_array($itemKey, $processedItems)) {
                continue;
            }

            if (!$is_only_creation && $additionalcost->accurate_invoice_item_id) {
                $body['detailItem[' . $iteration . '].id'] = $additionalcost->accurate_invoice_item_id;
            }

            $body['detailItem[' . $iteration . '].itemNo'] = $additionalcost->code;
            $body['detailItem[' . $iteration . '].unitPrice'] = $additionalcost->total_cost;
            $body['detailItem[' . $iteration . '].quantity'] = 1;
            $body['detailItem[' . $iteration . '].warehouseName'] = $order->store->accurate_warehouse_name;

            $processedItems[] = $itemKey;
            $iteration++;
        }

        // Add deleted additional costs
        $additionalcost_deleted_list = AdditionalCost::where('order_id', $order->id)
            ->whereNotNull('deleted_at')
            ->whereNotNull('accurate_invoice_item_id')
            ->withTrashed()
            ->get();

        foreach ($additionalcost_deleted_list as $additionalcost_deleted) {
            $body['detailItem[' . $iteration . '].id'] = $additionalcost_deleted->accurate_invoice_item_id;
            $body['detailItem[' . $iteration . ']._status'] = 'delete';
            $iteration++;
        }

        return $body;
    }

    private function checkForDuplicateItems($detailItems)
    {
        $processedItems = [];
        $hasDuplicates = false;

        foreach ($detailItems as $item) {
            $itemKey = $item->item->no . '_' . $item->quantity . '_' . $item->unitPrice;

            if (in_array($itemKey, $processedItems)) {
                $hasDuplicates = true;
                break;
            }

            $processedItems[] = $itemKey;
        }

        return $hasDuplicates;
    }

    private function processInvoiceResponse($result_create_faktur, $order)
    {
        $dataAccurateInvoice = [];
        $dataAccurateInvoice['accuratable_key'] = 'sales-invoice';

        if (is_object($result_create_faktur) && $result_create_faktur->s) {
            $accurate_data = $result_create_faktur->r;
            $dataAccurateInvoice['error_message'] = null;
            $dataAccurateInvoice['synced_at'] = date('Y-m-d H:i:s');
            $dataAccurateInvoice['accurate_id'] = $accurate_data->id;
            $dataAccurateInvoice['accurate_no'] = $accurate_data->number;

            // Process each detail item
            $processedItems = [];
            foreach ($accurate_data->detailItem as $detailItem) {
                $code = $detailItem->item->no;
                $qty = $detailItem->quantity;
                $price = $detailItem->unitPrice;
                $accurateDetailItemId = $detailItem->id;
                $itemKey = $code . '_' . $qty . '_' . $price;

                // Skip if we've already processed this item (handles duplicates)
                if (in_array($itemKey, $processedItems)) {
                    continue;
                }

                // Update order products
                OrderProduct::where('order_id', $order->id)
                    ->where('qty', $qty)
                    ->where('price', $price)
                    ->whereNull('deleted_at')
                    ->whereNull('accurate_invoice_item_id')
                    ->whereHas('product', function ($q) use ($code) {
                        $q->where('code', $code);
                    })->update([
                        'accurate_invoice_item_id' => $accurateDetailItemId,
                    ]);

                // Update additional costs
                AdditionalCost::where('order_id', $order->id)
                    ->where('code', $code)
                    ->whereNull('deleted_at')
                    ->whereNull('accurate_invoice_item_id')
                    ->update([
                        'accurate_invoice_item_id' => $accurateDetailItemId,
                    ]);

                $processedItems[] = $itemKey;
            }

            // Reset accurate_invoice_item_id for deleted items
            OrderProduct::where('order_id', $order->id)
                ->whereNotNull('deleted_at')
                ->whereNotNull('accurate_invoice_item_id')
                ->withTrashed()
                ->update([
                    'accurate_invoice_item_id' => null,
                ]);

            AdditionalCost::where('order_id', $order->id)
                ->whereNotNull('deleted_at')
                ->whereNotNull('accurate_invoice_item_id')
                ->withTrashed()
                ->update([
                    'accurate_invoice_item_id' => null,
                ]);
        } else if (is_object($result_create_faktur) && !$result_create_faktur->s && isset($result_create_faktur->d)) {
            $dataAccurateInvoice['error_message'] = is_array($result_create_faktur->d) ? $result_create_faktur->d[0] : $result_create_faktur->d;
        } else {
            $dataAccurateInvoice['error_message'] = 'Error fetchApiAccurate';
        }

        if ($order->accurateInvoice) {
            $order->accurateInvoice()->update($dataAccurateInvoice);
        } else {
            $order->accurateInvoice()->create($dataAccurateInvoice);
        }

        return $dataAccurateInvoice;
    }

    public function accurateCreatePurchase($purchase)
    {
        $purchase = $purchase->fresh()->load([
            'store',
            'products',
        ]);
        if ($purchase->store->accurate_branch_id && $purchase->store->accurate_warehouse_name) {
            $params = [];
            $body = [
                'vendorNo' => $purchase->vendor_acc_no,
                'billNumber' => $purchase->bill_number,
                'branchId' => $purchase->store->accurate_branch_id,
                // 'currencyCode' => 'IDR',
                'description' => $purchase->note,
                'transDate' => date('d/m/Y', strtotime($purchase->transaction_date)),
            ];
            $iteration = 0;
            foreach ($purchase->products as $product) {
                $body['detailItem[' . $iteration . '].itemNo'] = $product->code;
                $body['detailItem[' . $iteration . '].unitPrice'] = $product->pivot->price;
                // $body['detailItem['.$iteration.'].itemUnitName'] = 'Galon'; // * (NOT REQUIRED)
                $body['detailItem[' . $iteration . '].quantity'] = $product->pivot->qty;
                $body['detailItem[' . $iteration . '].warehouseName'] = $purchase->store->accurate_warehouse_name; // * (NOT REQUIRED)
                // $body['detailItem['.$iteration.']._status'] = 'delete'; // * Only to delete item (NOT REQUIRED)
                $iteration++;
            }
            $result_create_purchase = $this->fetchApiAccurate('/accurate/api/purchase-invoice/save.do', 'POST', $params, $body, false, false, true);
            $dataAccuratePurchase = [];
            $dataAccuratePurchase['accuratable_key'] = 'purchase-invoice';
            if (is_object($result_create_purchase) && isset($result_create_purchase->s) && $result_create_purchase->s) {
                $accurate_data = $result_create_purchase->r;
                $dataAccuratePurchase['error_message'] = null;
                $dataAccuratePurchase['synced_at'] = date('Y-m-d H:i:s');
                $dataAccuratePurchase['accurate_id'] = $accurate_data->id;
                $dataAccuratePurchase['accurate_no'] = $accurate_data->number;
                //TODO: If update need to store id on ProductPurchase
            } else {
                $dataAccuratePurchase['error_message'] = 'Error fetchApiAccurate';
                if (is_string($result_create_purchase)) {
                    $arrayError = explode('ERROR: ', $result_create_purchase);
                    if (count($arrayError) > 1) {
                        $dataAccuratePurchase['error_message'] = $arrayError[1];
                    }
                }
            }
            // if ($purchase->accurateInvoice) {
            //     $purchase->accurateInvoice()->update($dataAccuratePurchase);
            // } else {
            $purchase->accuratePurchase()->create($dataAccuratePurchase);
            // }
            return [
                'is_success' => isset($dataAccuratePurchase['accurate_id']),
                'message' => isset($dataAccuratePurchase['accurate_id']) ? 'Success' : $dataAccuratePurchase['error_message'],
            ];
        }
        return [
            'is_success' => false,
            'message' => 'Error fetchApiAccurate'
        ];
    }

    // public function accurateUpsertDeleteAddress($address_id, $is_only_creation = false, $is_deletion = false)
    // {
    //     $address_updated = null;
    //     $params = [];
    //     $body = [];
    //     $address = Address::where('id', $address_id)->withTrashed()->with('customer')->first();
    //     if ($address) {
    //         $customer = $address->customer;
    //         if (!$is_only_creation && empty($customer->accurate_id)) return null;
    //         $body['id'] = $customer->accurate_id;
    //         if ($address->label === 'Alamat Utama') {
    //             $body['billCity'] = $address->store->city->name;
    //             $body['shipCity'] = $address->store->city->name;
    //             $body['taxCity'] = $address->store->city->name;
    //             $body['billStreet'] = $address->address;
    //             $body['shipStreet'] = $address->address;
    //             $body['taxStreet'] = $address->address;
    //         }
    //         if ($address->accurate_id) {
    //             $body['detailShipAddress[0].id'] = $address->accurate_id;
    //         }
    //         $body['detailShipAddress[0].city'] = $address->store->city->name;
    //         $body['detailShipAddress[0].country'] = 'Indonesia';
    //         $body['detailShipAddress[0].street'] = $address->address;
    //         $body['detailShipAddress[0].street'] = $address->address;

    //         if ($is_deletion && !$is_only_creation && $address->accurate_id) {
    //             $body['detailShipAddress[0]._status'] = 'delete';
    //         }
    //         if ($is_only_creation && !$is_deletion) {
    //             unset($body['detailShipAddress[0].id']);
    //         }
    //         // return $body;
    //         $result_save = $this->fetchApiAccurate('/accurate/api/customer/save.do', 'POST', $params, $body);
    //         if ($result_save->s) {
    //             $accurate_data = $result_save->r;
    //             $address_to_updte_index = array_search($address->address, array_column($accurate_data->detailShipAddress, 'street'));
    //             if ($address_to_updte_index !== false) {;
    //                 $address->synced_at = date('Y-m-d H:i:s');
    //                 $address->accurate_id = $result_save->r->detailShipAddress[$address_to_updte_index]->id;
    //                 $address->saveQuietly();
    //                 $address_updated = $address;
    //                 return $address_updated;
    //             }
    //         }
    //     }
    //     return null;
    // }

    public function accurateBulkUpsertCustomerAddress($address_ids, $is_only_creation = false, $is_only_update = true)
    {
        $addresses_updated = [];
        // $addresses_to_update = [];
        $batches = array_chunk($address_ids, 100);
        foreach ($batches as $batch_addresses) {
            $params = [];
            $body = [];
            foreach ($batch_addresses as $key => $address_id) {
                $address = Address::where('id', $address_id)->with('customer')->first();
                if ($address) {
                    $customer = $address->customer;
                    if ($is_only_update && empty($address->accurate_customer_id)) continue;
                    if (!empty($address->accurate_customer_id)) {
                        $body['data[' . $key . '].id'] = $address->accurate_customer_id;
                    }
                    $body['data[' . $key . '].name'] = $customer->name;
                    $body['data[' . $key . '].categoryName'] = 'RT';
                    $body['data[' . $key . '].billCity'] = $address->store->city->name;
                    $body['data[' . $key . '].shipCity'] = $address->store->city->name;
                    $body['data[' . $key . '].taxCity'] = $address->store->city->name;
                    $body['data[' . $key . '].billCountry'] = 'Indonesia';
                    $body['data[' . $key . '].shipCountry'] = 'Indonesia';
                    $body['data[' . $key . '].taxCountry'] = 'Indonesia';
                    $body['data[' . $key . '].billStreet'] = $address->address;
                    $body['data[' . $key . '].shipStreet'] = $address->address;
                    $body['data[' . $key . '].taxStreet'] = $address->address;
                    $body['data[' . $key . '].shipSameAsBill'] = true;
                    $body['data[' . $key . '].taxSameAsBill'] = true;
                    $body['data[' . $key . '].currencyCode'] = 'IDR';
                    $body['data[' . $key . '].mobilePhone'] = $customer->phone;
                    if (!empty($address->store->accurate_branch_id)) {
                        $body['data[' . $key . '].branchId'] = $address->store->accurate_branch_id;
                    }
                    if (!empty($customer->email)) {
                        $body['data[' . $key . '].email'] = $customer->email;
                    }
                    if (!empty($customer->note_special)) {
                        $body['data[' . $key . '].notes'] = $customer->note_special;
                    }
                    // if ($address->accurate_address_id) {
                    //     $body['data[' . $key . '].detailShipAddress[0].id'] = $address->accurate_id;
                    // }
                    // $body['data[' . $key . '].detailShipAddress[0].city'] = $address->store->city->name;
                    // $body['data[' . $key . '].detailShipAddress[0].country'] = 'Indonesia';
                    // $body['data[' . $key . '].detailShipAddress[0].street'] = $address->address;
                    // $body['data[' . $key . '].detailShipAddress[0].street'] = $address->address;
                }
                if ($is_only_creation) {
                    unset($body['data[' . $key . '].id']);
                }
                // array_push($addresses_to_update, [
                //     'address_id' => $address->id,
                //     'accurate_code' => $customer->accurate_code,
                // ]);
            }
            if (!empty($body)) {
                $result_bulk_save = $this->fetchApiAccurate('/accurate/api/customer/bulk-save.do', 'POST', $params, $body);
                if (is_object($result_bulk_save) && $result_bulk_save->s) {
                    foreach ($result_bulk_save->d as $accurate_result) {
                        if (is_object($accurate_result) && $accurate_result->s) {
                            $accurate_data = $accurate_result->r;
                            // $accurate_code = $accurate_data->customerNo;
                            // $address_to_updte_index = array_search($accurate_code, array_column($addresses_to_update, 'accurate_code'));
                            // if ($address_to_updte_index === false) continue;
                            // $address_to_update = $addresses_to_update[$address_to_updte_index];
                            // $address_id = $address_to_update['address_id'];
                            $address_to_update = Address::where('accurate_customer_code', $accurate_data->customerNo)->with('customer')->first();

                            if ($address_to_update) {

                                // $customer = $address->customer;
                                // $customer->accurate_id = $accurate_data->id;
                                // $customer->accurate_code = $accurate_data->customerNo;
                                // $customer->synced_at = date('Y-m-d H:i:s');
                                // $customer->saveQuietly();

                                // $address_updated_index = array_search($address->address, array_column($accurate_data->detailShipAddress, 'street'));
                                // if ($address_updated_index !== false) {
                                $address_to_update->synced_at = date('Y-m-d H:i:s');
                                $address_to_update->accurate_customer_id = $accurate_data->id;
                                $address_to_update->accurate_customer_code = $accurate_data->customerNo;
                                $address_to_update->saveQuietly();
                                // };
                                array_push($addresses_updated, $address_to_update);
                            }
                        }
                    }
                }
            }
        }
        return $addresses_updated;
    }

    public static function revalidateFrontEnd($slug = null)
    {
        $store_slug = $slug;
        if (!$slug) {
            $store_slug = Store::where('slug', '!=', 'belanjakantor-online')->get()->map(function ($store) {
                return $store->slug;
            })->implode(',');
        }
        $ch = curl_init();
        $url = env('PWA_URL') . '/api/revalidate?secret=' . env('APP_KEY') . '&slug=' . $store_slug;
        // $url = env('API_URL').'/api/revalidate?secret='.env('API_TOKEN').'awefaewf';
        // $url = env('API_URL').'/api/awefwfrevalidate?secret='.env('API_TOKEN').'awefaewf';
        // $url = 'https://cs.ordergasplus.online/api/citiesstores';

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // curl_setopt($ch,CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        // curl_setopt($ch,CURLOPT_FRESH_CONNECT, true);
        // curl_setopt($ch,CURLOPT_FOLLOWLOCATION, false);
        // curl_setopt($ch,CURLOPT_FAILONERROR, true);
        $output = curl_exec($ch);
        $output = json_decode($output);

        $detail_output = '🛑 Front-End: Update gagal!';
        if ($output && property_exists($output, 'revalidated') && (bool)$output->revalidated) {
            // $detail_output = 'Revalidate: Berhasil!';
            $detail_output = '';
        } else if ($output && property_exists($output, 'message') && $output->message) {
            $detail_output = '🛑 Front-End: ' . $output->message;
        }
        curl_close($ch);

        return $detail_output;
    }

    public static function revalidateBelanjaKantor()
    {
        $ch = curl_init();
        $url = env('BELANJAKANTOR_URL') . '/api/revalidate?secret=' . env('APP_KEY');
        // $url = env('API_URL').'/api/revalidate?secret='.env('API_TOKEN').'awefaewf';
        // $url = env('API_URL').'/api/awefwfrevalidate?secret='.env('API_TOKEN').'awefaewf';
        // $url = 'https://cs.ordergasplus.online/api/citiesstores';

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // curl_setopt($ch,CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        // curl_setopt($ch,CURLOPT_FRESH_CONNECT, true);
        // curl_setopt($ch,CURLOPT_FOLLOWLOCATION, false);
        // curl_setopt($ch,CURLOPT_FAILONERROR, true);
        $output = curl_exec($ch);
        $output = json_decode($output);

        $detail_output = '🛑 Front-End: Update gagal!';
        if ($output && property_exists($output, 'revalidated') && (bool)$output->revalidated) {
            // $detail_output = 'Revalidate: Berhasil!';
            $detail_output = '';
        } else if ($output && property_exists($output, 'message') && $output->message) {
            $detail_output = '🛑 Front-End: ' . $output->message;
        }
        curl_close($ch);

        return $detail_output;
    }

    public function countCalendar($order)
    {
        $statuses = Status::all();
        $date = new DateTime($order->created_at);
        foreach ($statuses as $status) {
            $total = Order::where('status_id', $status->id)
                ->where('store_id', $order->store_id)
                ->whereDate('created_at', $date->format('Y-m-d'))
                ->count();
            Total::updateOrCreate(
                [
                    'date' => $date->format('Y-m-d'),
                    'store_id' => $order->store_id,
                    'totalable_type' => 'App\Models\Status',
                    'totalable_id' => $status->id
                ],
                ['total' => $total],
            );

            $drivers = User::where('role_id', 5)
                ->where('store_id', $order->store_id)
                ->get();
            foreach ($drivers as $driver) {
                $total = Order::where('status_id', $status->id)
                    ->where('store_id', $order->store_id)
                    ->where('driver_id', $driver->id)
                    ->whereDate('created_at', $date->format('Y-m-d'))
                    ->count();
                Total::updateOrCreate(
                    [
                        'date' => $date->format('Y-m-d'),
                        'store_id' => $order->store_id,
                        'user_id' => $driver->id,
                        'totalable_type' => 'App\Models\Status',
                        'totalable_id' => $status->id
                    ],
                    ['total' => $total],
                );
            }
        }
    }

    public function countPerformaProducts($order)
    {
        foreach ($order->products as $product) {
            $total = OrderProduct::where('product_id', $product->id)
                ->whereHas('order', function (Builder $query) use ($order) {
                    $query->where('store_id', $order->store_id)
                        ->whereIn('status_id', [4, 6, 7])
                        ->whereDate('created_at', $order->created_at);
                })
                ->sum('qty');

            $date = new DateTime($order->created_at);
            Total::updateOrCreate(
                [
                    'date' => $date->format('Y-m-d'),
                    'store_id' => $order->store_id,
                    'totalable_type' => 'App\Models\Product',
                    'totalable_id' => $product->id
                ],
                ['total' => $total],
            );
        }
    }

    public function countPerformaDrivers($order)
    {
        $total = Order::where('store_id', $order->store_id)
            ->where('driver_id', $order->driver_id)
            ->whereIn('status_id', [4, 6, 7])
            ->whereDate('created_at', $order->created_at)
            ->count();

        $total_overtime = Order::where('store_id', $order->store_id)
            ->where(function (Builder $query) {
                $query->where('duration', '>', 5400)
                    ->orWhere('duration', '<', -5400);
            })
            ->where('driver_id', $order->driver_id)
            ->whereIn('status_id', [4, 6, 7])
            ->whereDate('created_at', $order->created_at)
            ->count();

        $date = new DateTime($order->created_at);

        Total::updateOrCreate(
            [
                'date' => $date->format('Y-m-d'),
                'store_id' => $order->store_id,
                'totalable_type' => 'App\Models\User',
                'totalable_id' => $order->driver_id,
                'type' => null,
            ],
            ['total' => $total],
        );

        Total::updateOrCreate(
            [
                'date' => $date->format('Y-m-d'),
                'store_id' => $order->store_id,
                'totalable_type' => 'App\Models\User',
                'totalable_id' => $order->driver_id,
                'type' => 'overtime',
            ],
            ['total' => $total_overtime],
        );
    }

    public function getUserStoreSlug()
    {
        // Super Admin, Owner and CS
        if (auth()->user()->role_id <= 2) {
            $store = Store::first();
            if (!$store || ($store && !$store->slug)) {
                return redirect()->route('do-logout');
            }
            $store_slug = $store->slug;
        } elseif (in_array((int)auth()->user()->role_id, [3, 4, 6])) {
            $store = null;
            if (auth()->user()->stores && auth()->user()->stores->count() > 0) {
                $store = auth()->user()->stores->first();
            } else if (auth()->user()->store) {
                $store = auth()->user()->store;
            }
            if (!$store || ($store && !$store->slug)) {
                return redirect()->route('do-logout');
            }
            $store_slug = $store->slug;
        } else {
            $store = auth()->user()->store;
            if (!$store || ($store && !$store->slug)) {
                return redirect()->route('do-logout');
            }
            $store_slug = auth()->user()->store->slug;
        }
        return $store_slug;
    }

    public function checkUserStoreSlug($current_store_slug, $route_name = 'calendar')
    {
        if (Store::where('slug', $current_store_slug)->count() > 0) {
            if (auth()->user()->role_id <= 2) {
                return ['status' => true];
            } elseif (in_array((int)auth()->user()->role_id, [3, 4, 6])) {
                $slug = '';
                $selected_store = auth()->user()->stores->where('slug', $current_store_slug)->first();
                if ($selected_store) {
                    $slug = $selected_store->slug;
                } else {
                    $slug = auth()->user()->stores->count() > 0 ? auth()->user()->stores->first()->slug : (auth()->user()->store ? auth()->user()->store->slug : null);
                }
                return ['status' => $slug === $current_store_slug, 'store_slug' => $slug];
            } else {
                $user_store_slug = auth()->user()->store->slug;
                return ['status' => $user_store_slug === $current_store_slug, 'store_slug' => $user_store_slug];
            }
        } elseif ($current_store_slug === 'all' && $route_name === 'calendar' && in_array((int)auth()->user()->role_id, [1, 2, 3, 4, 6])) {
            return ['status' => true];
        } else {
            return ['status' => false, 'store_slug' => $this->getUserStoreSlug()];
        }
    }

    public function convertPhone($number)
    {
        $phone = $number;
        $phone = preg_replace("/[^0-9]/", "", $phone);
        // $phone = str_replace(' ', '', $phone);
        // $phone = str_replace('-', '', $phone);
        // $phone = str_replace('+', '', $phone);
        // $phone = str_replace('(', '', $phone);
        // $phone = str_replace(')', '', $phone);
        if (substr($phone, 0, 1) == '0') {
            $phone = '62' . substr($phone, 1);
        } elseif (substr($phone, 0, 1) == '8') {
            $phone = '62' . $phone;
        }
        return $phone;
    }

    public function getDistance($latlng1, $latlng2)
    {
        $earth_radius = 6371; // in Km
        // $earth_radius = 3959; // in Miles

        $latlng1_arr = explode(',', $latlng1);
        $latlng2_arr = explode(',', $latlng2);

        if (is_array($latlng1_arr) && is_array($latlng2_arr) && count($latlng1_arr) === 2 && count($latlng2_arr) === 2 && is_numeric($latlng1_arr[0]) && is_numeric($latlng1_arr[1]) && is_numeric($latlng2_arr[0]) && is_numeric($latlng2_arr[1])) {
            $latitude1 = $latlng1_arr[0];
            $longitude1 = $latlng1_arr[1];
            $latitude2 = $latlng2_arr[0];
            $longitude2 = $latlng2_arr[1];

            $dLat = deg2rad($latitude2 - $latitude1);
            $dLon = deg2rad($longitude2 - $longitude1);

            $a = sin($dLat / 2) * sin($dLat / 2) + cos(deg2rad($latitude1)) * cos(deg2rad($latitude2)) * sin($dLon / 2) * sin($dLon / 2);
            $c = 2 * asin(sqrt($a));
            $d = $earth_radius * $c;

            return $d * 1000; // in meter
        }

        return 0; // in meter
    }

    public function getDirectionMapbox($latlng1, $latlng2)
    {
        // Mapbox API key
        $apiKey = 'sk.eyJ1IjoiY2F0bW9iaWwiLCJhIjoiY2x1M2p2NWV2MTBqcjJuczM0em0wMDAwcCJ9.Wy5TY491M4ws_kknhS-QJQ';

        $latlng1_arr = explode(',', $latlng1);
        $latlng2_arr = explode(',', $latlng2);

        if (is_array($latlng1_arr) && is_array($latlng2_arr) && count($latlng1_arr) === 2 && count($latlng2_arr) === 2 && is_numeric($latlng1_arr[0]) && is_numeric($latlng1_arr[1]) && is_numeric($latlng2_arr[0]) && is_numeric($latlng2_arr[1])) {

            // $latitude1 = preg_replace('/\s+/', '', $latlng1_arr[1]);
            // $longitude1 = preg_replace('/\s+/', '', $latlng1_arr[0]);
            // $latitude2 = preg_replace('/\s+/', '', $latlng2_arr[1]);
            // $longitude2 = preg_replace('/\s+/', '', $latlng2_arr[0]);

            $latitude1 = $latlng1_arr[1];
            $longitude1 = $latlng1_arr[0];
            $latitude2 = $latlng2_arr[1];
            $longitude2 = $latlng2_arr[0];

            $latlng1_latlng2 = $latitude1 . ',' . $longitude1 . ';' . $latitude2 . ',' . $longitude2;

            $direction = @file_get_contents('https://api.mapbox.com/directions/v5/mapbox/driving/' . $latlng1_latlng2 . '?alternatives=false&continue_straight=false&geometries=geojson&overview=full&steps=false&notifications=none&access_token=' . $apiKey);
            if ($direction === FALSE) {
                return 0;
            }
            // return $direction = 'https://api.mapbox.com/directions/v5/mapbox/driving/' . $latlng1_latlng2 . '?alternatives=false&continue_straight=false&geometries=geojson&overview=full&steps=false&notifications=none&access_token=' . $apiKey;
            $direction = json_decode($direction);
            if (!empty($direction->error_message)) {
                return 0;
                // return $direction->error_message;
            }

            $distance = $direction->routes[0]->distance;

            return round($distance);
        }

        return 0; // in meter
    }

    public function countDistances($stores, $address)
    {
        return $address;
        if (!$address) return;
        $new_distance_sync = [];
        foreach ($stores as $store) {
            if (!$store->latlng) continue;
            if ($address->store->city_id !== $store->city_id) continue;
            $storedistance = $address->storedistances->where('pivot.store_id', $store->id)->first();
            $pivot_data = [
                'distance_meter' => null,
                'distance_meter_by_route' => null,
            ];
            if ($storedistance) {
                $pivot_data = [
                    'distance_meter' => $storedistance->pivot->distance_meter,
                    'distance_meter_by_route' => $storedistance->pivot->distance_meter_by_route,
                ];
            }
            if (!$pivot_data['distance_meter']) {
                $distance = $this->getDistance($store->latlng, $address->latlng);
                $pivot_data['distance_meter'] = round($distance);
            }
            if (!$pivot_data['distance_meter_by_route'] && $pivot_data['distance_meter'] <= 7000) {
                $distance_by_route = $this->getDirectionMapbox($store->latlng, $address->latlng);
                $pivot_data['distance_meter_by_route'] = $distance_by_route;
            }
            if ($storedistance) {
                if ($storedistance->pivot->distance_meter !== $pivot_data['distance_meter'] || $storedistance->pivot->distance_meter_by_route !== $pivot_data['distance_meter_by_route']) {
                    $address->storedistances()->updateExistingPivot($store->id, $pivot_data);
                }
            } else {
                $new_distance_sync[$store->id] = $pivot_data;
            }
        }
        // return $distance_sync;
        // $address->storedistances()->updateExistingPivot($store->id, $pivot_data);
        if (count($new_distance_sync) > 0) {
            $address->storedistances()->syncWithoutDetaching($new_distance_sync);
        }
    }

    public function saveImage($type, $model, $photo, $size, $key, $model_type, $name = '')
    {
        // TODO: Convert to .webp in the end
        $quality = 80;
        $format = 'jpeg';
        $img_path = 'public/gasplus';
        $thumbnail_path = 'public/gasplus/thumbnails';
        $path = 'img' . DIRECTORY_SEPARATOR . $type;
        $photo_name = $type . '-' . $model->id . '-' . microtime(true) . '-' . $name . '.' . $format;

        // Image
        $full_img_path = $img_path . DIRECTORY_SEPARATOR . $path;
        if (!File::isDirectory($full_img_path)) {
            File::makeDirectory($full_img_path, 0777, true, true);
        }

        // Convert HEIC to JPG if necessary
        if ($photo->getClientOriginalExtension() === 'heic') {
            $imagick = new \Imagick();
            $imagick->readImage($photo->getRealPath());
            $imagick->setImageFormat('jpeg');
            $photo_image = Image::make($imagick->getImageBlob())->encode('jpg', $quality);
            $imagick->clear();
            $imagick->destroy();
        } else {
            $photo_image = Image::make($photo->getRealPath());
        }

        $photo_image->resize(null, $size, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        })->save($full_img_path . DIRECTORY_SEPARATOR . $photo_name, $quality, $format);

        // Thumbnail
        $full_thumbnail_path = $thumbnail_path . DIRECTORY_SEPARATOR . $path;
        if (!File::isDirectory($full_thumbnail_path)) {
            File::makeDirectory($full_thumbnail_path, 0777, true, true);
        }
        $photo_thumbnail = clone $photo_image;
        $photo_thumbnail->resize(null, 30, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        })->save($full_thumbnail_path . DIRECTORY_SEPARATOR . $photo_name, $quality, $format);

        $file_name = $path . DIRECTORY_SEPARATOR . $photo_name;

        return [
            'model_type' => $model_type,
            'model_id' => $model->id,
            'model_key' => $key,
            'file_name' => $file_name,
            'mime_type' => $photo_image->mime(),
            'disk' => 'public',
            'size' => $photo_image->filesize(),
        ];
    }

    public function createOrderMessage($order, $is_short = true, $is_asdt = false)
    {
        $options = $order->options;
        $is_hide_deposit = $options ? $options['is_hide_deposit'] : false;
        $data_products = '';
        foreach ($order->products as $product) {
            $data_products .= '(' . $product->pivot->qty . 'x) ';
            $data_products .= $product->name . ' ';
            if ($product->isPpob) {
                $data_products .= $product->pivot->ppob_nominal === 0 ? '' : '- ' . ((int)$product->pivot->ppob_nominal) / 1000 . 'rb ';
                $data_products .= '(' . $product->pivot->ppob_label . ' / ' . $product->pivot->ppob_key . ') ';
            }
            $data_products .= '@Rp' . number_format($product->pivot->price, 0, '', '.');
            $data_products .= '<br>';
        }

        $additional_costs = '';
        foreach ($order->additionalcosts as $additionalcost) {
            $additional_costs .= '(+) ' . $additionalcost->name . ' ';
            $additional_costs .= '@Rp' . number_format($additionalcost->total_cost ? $additionalcost->total_cost : $additionalcost->cost, 0, '', '.');
            $additional_costs .= '<br>';
        }

        $data_payment = ucwords($order->payment);
        // if ($order->payment == 'cash' && $order->amount_will_pay) {
        //     $data_payment .= ' (Rp'.number_format($order->amount_will_pay, 0, '', '.').')';
        // }
        $data_payment .= '<br><br>';
        if ($order->payment == 'cash') {
            $data_payment .= '_bisa juga transfer ke:_<br><br>';
        }

        foreach ($order->store->banks as $bank) {
            if (strtolower($bank->bank_name) !== 'deposit') {
                $data_payment .= '*' . $bank->bank_name . ' ' . $bank->account_number . ' 🏧*<br>';
                $data_payment .= 'a.n. ' . $bank->holder_name . '<br>';
                $data_payment .= '<br>';
            }
        }

        // $data_payment .= $order->payment == 'cash' ? '_Jika bayar transfer, harap mengirimkan foto *BUKTI TRANSFER*._' : '_Harap mengirimkan foto *BUKTI TRANSFER*._';
        $data_payment .= '_Bukti transfer kirim ke no *CS ( wa.me/' . $order->store->whatsapp_1 . ' )*_';
        $data_payment .= '<br>';

        $data_deposit = '';
        if ($order->amount_deposit_used > 0) {
            $data_deposit .= 'Deposit Terpakai: -Rp' . number_format($order->amount_deposit_used, 0, '', '.') . '<br>';
        } elseif ($order->amount_deposit_used < 0) {
            $data_deposit .= 'Kekurangan Lalu: +Rp' . number_format(abs($order->amount_deposit_used), 0, '', '.') . '<br>';
        }

        $data_balance = '';
        if ($order->deposit_balance_after > 0) {
            $data_balance .= '<br>';
            $data_balance .= '_Sisa Deposit: Rp' . number_format(abs($order->deposit_balance_after), 0, '', '.') . '_';
            $data_balance .= '<br>';
        } elseif ($order->deposit_balance_after < 0) {
            $data_balance .= '<br>';
            $data_balance .= '_Kekurangan Bayar: Rp' . number_format(abs($order->deposit_balance_after), 0, '', '.') . '_';
            $data_balance .= '<br>';
        }

        $data_deposit_balance_before = '';
        if ($order->deposit_balance_before != 0 && $order->deposit_balance_before != null) {
            $data_deposit_balance_before .= 'Saldo Deposit: Rp' . number_format(abs($order->deposit_balance_before), 0, '', '.') . '<br>';
            // $data_deposit_balance_before .= '<br>';
        }

        $data_total_tagihan = '*Total Tagihan: Rp ' . number_format($order->total, 0, '', '.') . '*<br>';

        $data_total_after_deposit = '*Total Tagihan: Rp ' . number_format($order->total_after_deposit, 0, '', '.') . '*<br>';
        // if ($order->total_after_deposit == 0) {
        //     $data_total_after_deposit .= '*TIDAK PERLU BAYAR (FREE)*<br>';
        // }

        $data = [
            '${store_name}' => $order->store->name,
            '${order_code}' => $order->code,
            '${customer_id}' => $order->customer->id,
            '${customer_name}' => $order->customer->name,
            '${customer_phone}' => $order->customer->phone,
            '${receiver_phone}' => $order->receiver_phone,
            '${customer_address}' => $order->address->address,
            '${order_products}' => $data_products,
            '${additional_costs}' => $order->address->is_secondfloor ? $additional_costs : '',
            '${topup_deposit}' => $order->deposit_job > 0 ? '(+) Topup Deposit @Rp' . number_format($order->deposit_job, 0, '', '.') . '<br>' : '',
            '${refund_deposit}' => $order->deposit_job < 0 ? '(-) Refund Deposit @Rp' . number_format(abs($order->deposit_job), 0, '', '.') . '<br>' : '',
            '${order_note}' => $order->note ? '<br>Catatan: *' . $order->note . '*' : '',
            '${payment}' => $data_payment,
            '${deposit_balance_before}' => $data_deposit_balance_before,
            '${total_qty}' => $order->products()->sum('qty'),
            '${total_nota}' => 'Rp' . number_format($order->total, 0, '', '.'),
            '${deposit_info}' => $data_deposit,
            '${total_tagihan}' => $data_total_tagihan,
            '${total_after_deposit}' => $data_total_after_deposit,
            '${deposit_balance}' => $data_balance,

            '${store_slug}' => $order->store->slug,
            '${total}' => $order->total,
            '${order_code}' => $order->code,
        ];

        $msg = '';
        $msg .= '*🔖 Lihat CATALOG* produk di ' . env('PWA_URL') . ($is_asdt ? '' : '/catalog/${customer_phone}<br><br>');
        $msg .= '*ORDER :: ${store_name} :: ${order_code}*<br>';
        $msg .= '<br>';
        $msg .= $is_asdt ? '' : 'Nama: ${customer_name}<br>';
        $msg .= $is_asdt ? '' : 'HP Pelanggan: wa.me/${customer_phone}<br>';
        $msg .= !$is_asdt ? '' : 'HP Penerima: wa.me/${receiver_phone}<br>';
        $msg .= $is_asdt ? '' : 'Alamat: ${customer_address}<br>';
        $msg .= '<br>';
        $msg .= '*Produk:*<br>';
        $msg .= '${order_products}';
        $msg .= '${order_note}';
        $msg .= '<br>';
        // $msg .= 'Pembayaran: ${payment}';
        // $msg .= '<br>';
        $msg .= !$is_hide_deposit ? '${deposit_balance_before}<br>' : '';
        // $msg .= 'Total Qty: ${total_qty}<br>';
        $msg .= 'Total Nota: ${total_nota} (${total_qty} produk)<br>';
        $msg .= !$is_hide_deposit ? '${deposit_info}' : '';
        $msg .= 'Ongkos Kirim: FREE (Kurir Gasplus)<br>';
        // $msg .= 'Ekspedisi: Kurir Gasplus<br>';
        // $msg .= '${total_tagihan}';
        $msg .= '${total_after_deposit}';

        $msg .= !$is_hide_deposit ? '${deposit_balance}' : '';

        $msg .= '<br>';
        $msg .= '<br>';
        $msg .= '_Bukti transfer kirim ke no *CS ( wa.me/' . $order->store->whatsapp_1 . ' )*_<br>';
        $msg .= '<br>';
        $msg .= '_Transfer / QRIS klik link di bawah:_<br>';
        $msg .= '<br>';
        $msg .= '🏧 *Lihat No.Rekening* 👉 ' . env('APP_URL') . '/info/payment/${store_slug}/tf?total=${total}&order_code=${order_code}';
        $msg .= '<br>';
        $msg .= '<br>';
        $msg .= '📱 *Lihat QRIS* 👉 ' . env('APP_URL') . '/images/qris/${store_slug}.jpeg';

        // $msg .= '<br>';
        // $msg .= '<br>';
        // $msg .= '🛑 *BERHENTI MENERIMA PESAN INI* 👉 '.env('APP_URL').'/info/notif?phone='.$order->customer->phone.'&tkn='.$order->customer->id.'&is=off';
        // $msg .= '🛑 *TIDAK MAU MENERIMA PESAN INI* 👉 '.env('PWA_URL').'/toko/${store_slug}/pengaturan?phone=${customer_phone}&notif=off';
        // $msg .= '${total_after_deposit}';
        // $msg .= '${deposit_balance}';
        $msg .= '<br>';




        $msg_short = '';
        $msg_short .= '*🔖 Lihat CATALOG* produk di ' . env('PWA_URL') . ($is_asdt ? '' : '/catalog/${customer_phone}') . '<br><br>';
        $msg_short .= '*ORDER :: ${store_name} :: ${order_code}*<br>';
        $msg_short .= '${order_products}';
        $msg_short .= '${additional_costs}';
        $msg_short .= $is_asdt ? '' : '${topup_deposit}${refund_deposit}';
        $msg_short .= $is_asdt ? '' : '*Nama: ${customer_name}*<br>';
        $msg_short .= !$is_asdt ? '' : '*HP: wa.me/${receiver_phone}*<br>';
        $msg_short .= $is_asdt ? '' : 'Alamat: ${customer_address}<br>';
        $msg_short .= '${order_note}';
        $msg_short .= '<br>';
        $msg_short .= !$is_hide_deposit && !$is_asdt ? '${deposit_balance_before}' : '';
        $msg_short .= 'Total Nota: ${total_nota}<br>';
        $msg_short .= !$is_hide_deposit && !$is_asdt ? '${deposit_info}' : '';
        $msg_short .= '${total_after_deposit}';
        $msg_short .= !$is_hide_deposit && !$is_asdt ? '${deposit_balance}' : '';
        $msg_short .= $is_asdt ? '' : '<br>';
        $msg_short .= $is_asdt ? '' : '*Klik link di bawah* untuk melihat:<br>';
        $msg_short .= $is_asdt ? '' : '- Perincian Nota<br>';
        $msg_short .= $is_asdt ? '' : '- Informasi Transfer & QRIS<br>';
        $msg_short .= $is_asdt ? '' : '- Track Status Order<br>';
        $msg_short .= $is_asdt ? '' : env('PWA_URL') . '/toko/${store_slug}/transaksi?phone=${customer_phone}';

        return strtr($is_short ? $msg_short : $msg, $data);
    }

    public function createPpobMessage($order_product)
    {
        if ($order_product->ppob_status !== 'SUCCESS') return "";
        $order_product->load('ppobresponsedata');

        if ($order_product->product->code === 'PLN_TKN') {
            if (!$order_product->ppobresponsedata || !isset($order_product->ppobresponsedata->response_data['sn'])) return "";
            $ppob_customer_data = PpobCustomerData::where('key', $order_product->ppob_key)
                ->where('product_id', $order_product->product_id)
                ->where('customer_id', $order_product->order->customer_id)
                ->first();
            if (!$ppob_customer_data) return "";
            $data = [
                '${product_name}' => $order_product->product->name,
                '${product_price}' => (int)$order_product->price / 1000 . 'rb',
                '${customer_name}' => $order_product->ppob_label,
                '${customer_id}' => $ppob_customer_data ? $ppob_customer_data->inquiry_data['customer_id'] : '-',
                '${meter_no}' => $ppob_customer_data ? $ppob_customer_data->inquiry_data['meter_no'] : '-',
                '${segment_power}' => $ppob_customer_data ? $ppob_customer_data->inquiry_data['segment_power'] : '-',
                '${time}' => $order_product->ppobresponsedata->updated_at->format('H:i'),
                '${date}' => $order_product->ppobresponsedata->updated_at->format('d M Y'),
                '${total_payment}' => 'Rp' . number_format($order_product->price, 0, '', '.'),
                '${ref_id}' => $order_product->ppob_ref_id,
                '${sn}' => explode('/', $order_product->ppobresponsedata->response_data['sn'])[0],
                '${customer_phone}' => $order_product->order->customer->phone,
            ];

            $msg = '';
            $msg .= '*${product_name} - ${product_price}*<br>';
            // $msg .= '<br>';
            $msg .= 'No.Meter: ${meter_no}<br>';
            $msg .= 'ID.Pelanggan: ${customer_id}<br>';
            $msg .= '*Nama: ${customer_name}*<br>';
            $msg .= '<br>';
            $msg .= 'Tarif/Data: ${segment_power}<br>';
            $msg .= 'Waktu: ${time}<br>';
            $msg .= 'Tanggal: ${date}<br>';
            $msg .= 'Harga: ${total_payment}<br>';
            $msg .= 'Ref ID: ${ref_id}<br>';
            $msg .= '<br>';
            $msg .= '⚡️ *TOKEN: ${sn}*<br>';
            $msg .= '<br>';
            $msg .= '<br>';
            $msg .= '*🔖 Lihat CATALOG* produk di ' . env('PWA_URL') . '/catalog/${customer_phone}';
        } else if ($order_product->product->code === 'EMONEY') {
            if (!$order_product->ppobresponsedata || !isset($order_product->ppobresponsedata->response_data['sn'])) return "";
            $ppob_customer_data = PpobCustomerData::where('key', $order_product->ppob_key)
                ->where('product_id', $order_product->product_id)
                ->where('customer_id', $order_product->order->customer_id)
                ->where('ppob_product_code', $order_product->ppob_product_code)
                ->first();
            if (!$ppob_customer_data) return "";
            $data = [
                '${product_name}' => $order_product->product->name,
                '${product_price}' => $ppob_customer_data->inquiry_data['product_description'] . ' ' . $ppob_customer_data->inquiry_data['product_nominal'],
                '${customer_name}' => $order_product->ppob_label,
                '${status}' => $order_product->ppobresponsedata->response_data['message'],
                '${time}' => $order_product->ppobresponsedata->updated_at->format('H:i'),
                '${date}' => $order_product->ppobresponsedata->updated_at->format('d M Y'),
                '${total_payment}' => 'Rp' . number_format($order_product->price, 0, '', '.'),
                '${ref_id}' => $order_product->ppob_ref_id,
                '${sn}' => $order_product->ppobresponsedata->response_data['sn'],
                '${customer_phone}' => $order_product->order->customer->phone,
            ];

            $msg = '';
            $msg .= '*${product_name} - ${product_price}*<br>';
            // $msg .= '<br>';
            $msg .= '<br>';
            $msg .= '*Status: ${status}*<br>';
            $msg .= 'Waktu: ${time}<br>';
            $msg .= 'Tanggal: ${date}<br>';
            $msg .= 'Harga: ${total_payment}<br>';
            $msg .= 'Ref ID: ${ref_id}<br>';
            $msg .= 'SN: ${sn}<br>';
            $msg .= '<br>';
            $msg .= '<br>';
            $msg .= '*🔖 Lihat CATALOG* produk di ' . env('PWA_URL') . '/catalog/${customer_phone}';
        } else if ($order_product->product->code === 'AIR_PDAM') {
            if (empty($order_product->ppobresponsedata->response_data['response_code']) || $order_product->ppobresponsedata->response_data['response_code'] !== '00') return "";

            $desc = $order_product->ppobresponsedata->response_data['desc'];
            $bill_details = '';
            if (isset($desc['bill']['detail']) && is_array($desc['bill']['detail'])) {
                foreach ($desc['bill']['detail'] as $bill) {
                    $bill_details .= 'Periode: ' . DateTime::createFromFormat('Ym', $bill['period'])->format('F Y') . '<br>';
                    $bill_details .= 'Meter Awal: ' . $bill['first_meter'] . '<br>';
                    $bill_details .= 'Meter Akhir: ' . $bill['last_meter'] . '<br>';
                    $bill_details .= 'Denda: Rp' . number_format($bill['penalty'], 0, '', '.') . '<br>';
                    $bill_details .= 'Tagihan: Rp' . number_format($bill['bill_amount'], 0, '', '.') . '<br>';
                    $bill_details .= 'Retribusi: Rp' . number_format($bill['misc_amount'], 0, '', '.') . '<br>';
                    $bill_details .= '----------------<br>';
                }
            }

            $helper_iak = new HelperIak();
            $receipt = $helper_iak->getReceiptPostpaid($order_product->order, $order_product->ppob_tr_id);

            $data = [
                '${product_name}' => $order_product->product->name,
                '${status}' => $order_product->ppobresponsedata->response_data['message'],
                '${product_price}' => 'Rp' . number_format($order_product->price, 0, '', '.'),
                '${customer_name}' => $order_product->ppob_label,
                '${customer_id}' => $order_product->ppob_key,
                '${time}' => $order_product->ppobresponsedata->updated_at->format('H:i'),
                '${date}' => $order_product->ppobresponsedata->updated_at->format('d M Y'),
                '${total_payment}' => 'Rp' . number_format($order_product->price, 0, '', '.'),
                '${ref_id}' => $order_product->ppob_ref_id,
                '${noref}' => $order_product->ppobresponsedata->response_data['noref'],
                '${address}' => $desc['address'] ?? '-',
                '${pdam_name}' => $desc['pdam_name'] ?? '-',
                '${bill_details}' => $bill_details,
                '${customer_phone}' => $order_product->order->customer->phone,
                '${receipt}' => !$receipt ? '' : '<br>*🧾 Bukti Pembayaran:* ' . $receipt['path'] . '<br>',
            ];

            $msg = '';
            $msg .= '*${product_name} - ${status}*<br>';
            $msg .= 'ID Pelanggan: ${customer_id}<br>';
            $msg .= '*Nama: ${customer_name}*<br>';
            $msg .= 'Alamat: ${address}<br>';
            $msg .= 'PDAM: ${pdam_name}<br>';
            $msg .= '<br>';
            $msg .= 'Waktu: ${time}<br>';
            $msg .= 'Tanggal: ${date}<br>';
            $msg .= 'Total: ${total_payment}<br>';
            $msg .= 'Ref ID: ${ref_id}<br>';
            $msg .= 'No Ref: ${noref}<br>';
            $msg .= '${receipt}';
            $msg .= !$bill_details ? '' : '<br>';
            $msg .= !$bill_details ? '' : '*Rincian Tagihan:*<br>';
            $msg .= !$bill_details ? '' : '${bill_details}';
            $msg .= '<br>';
            $msg .= '*🔖 Lihat CATALOG* produk di ' . env('PWA_URL') . '/catalog/${customer_phone}';
        }

        return strtr($msg, $data);
    }

    public function queueSendNotif($datas, $prior = 'medium', $with_delay_next = true)
    {
        foreach ($datas as $key => $data) {
            $new_notif = [
                "notifable_type" => "App\Models\Order",
                "notifable_id" => $data["order_id"],
                "to" => $this->convertPhone($data['to']),
            ];
            if (isset($data['message'])) {
                $new_notif['message'] = $data['message'];
            }
            if (isset($data['footer'])) {
                $new_notif['footer'] = $data['footer'];
            }
            if (isset($data['buttons'])) {
                $new_notif['buttons'] = json_encode($data['buttons']);
            }
            if (isset($data['button_links'])) {
                $new_notif['button_links'] = json_encode($data['button_links']);
            }
            if (isset($data['forward_message'])) {
                $new_notif['forward_message'] = $data['forward_message'];
            }
            Notification::create($new_notif);
            //     $count_high = \DB::table('jobs')->where('queue', '=', 'high')->count();
            //     $count_medium = \DB::table('jobs')->where('queue', '=', 'medium')->count();
            //     $count_low = \DB::table('jobs')->where('queue', '=', 'low')->count();
            //     $delay_high = ($count_high + 1) * 7;
            //     // $delay_medium = $count_medium * rand(10, 15);
            //     $delay_medium = ($count_medium + 1) * 5;
            //     // $delay_low = ($key > 0 && !$with_delay_next ? $count_low - $key : $count_low) * 18;
            //     $delay_low = ($count_low + 1) * 16;
            //     $delay = [
            //         "high" => $delay_high,
            //         // "default" => $delay_default,
            //         "medium" => $delay_medium,
            //         "low" => $delay_low,
            //     ];

            //     ProcessNotif::dispatch($data)->onQueue($prior)->delay(now()->addSeconds($delay[$prior]));
        }
    }

    public function processSendNotifToWablas($datas)
    {
        return true; // disable this feature

        $curl = curl_init();

        $dataToSend = '{"data": [';
        foreach ($datas as $key => $data) {
            $dataToSend .= '{';
            $dataToSend .= '"phone": "' . $this->convertPhone($data['to']) . '",';
            $dataToSend .= '"message": "' . preg_replace("/\r\n|\r|\n/", '<br>', $data['message']) . '",';
            $dataToSend .= '"source": "postman"';
            $dataToSend .= '}';
            if ($key !== array_key_last($datas)) {
                $dataToSend .= ',';
            }
        }
        $dataToSend .= ']}';

        curl_setopt_array($curl, array(
            CURLOPT_URL => ENV('WABLAS_URL') . '/api/v2/send-message',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 20,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $dataToSend,
            CURLOPT_HTTPHEADER => array(
                'Authorization: ' . ENV('WABLAS_API_KEY'),
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        return $response;
    }

    public function processSendNotifToServer($data)
    {
        // $url = 'http://109.106.255.226:8000/api/send';
        $base_url = ENV('WA_NOTIF_URL') ? ENV('WA_NOTIF_URL') : 'http://109.106.255.226:8000';
        $url = $base_url . '/api/send';
        $chs = [];
        $responses = [];
        $mh = curl_multi_init();

        // const templateButtons = [
        //     {
        //       index: 1,
        //       urlButton: {
        //         displayText: "🏧 Transfer",
        //         url: "'.env('APP_URL').'/info/payment/gasplus-palagan/tf",
        //       },
        //     },
        //     {
        //       index: 2,
        //       urlButton: {
        //         displayText: "📱 QRIS Gopay/OVO/LinkAja/Dana/Lainnya",
        //         url: "'.env('APP_URL').'/images/qris/gasplus-palagan.jpeg",
        //       },
        //     },
        //   ];

        // foreach ($datas as $key => $data) {
        $key_constant = 0;
        $chs[$key_constant] = curl_init($url);

        $body = [
            'key' => config('app.key'),
            'to'      => $this->convertPhone($data['to']),
            'type'    => isset($data['type']) && !empty($data['type']) ? $data['type'] : 'message',
        ];
        if (isset($data['message'])) {
            $body['message'] = $data['message'];
        }
        if (isset($data['footer'])) {
            $body['footer'] = $data['footer'];
        }
        if (isset($data['buttons'])) {
            $body['buttons'] = json_encode($data['buttons']);
        }
        if (isset($data['button_links'])) {
            $body['button_links'] = json_encode($data['button_links']);
        }
        if (isset($data['forward_message'])) {
            $body['forward_message'] = $data['forward_message'];
        }
        $content = json_encode($body);
        // dd($content);
        curl_setopt($chs[$key_constant], CURLOPT_HEADER, false);
        curl_setopt($chs[$key_constant], CURLOPT_RETURNTRANSFER, true);
        curl_setopt(
            $chs[$key_constant],
            CURLOPT_HTTPHEADER,
            array("Content-type: application/json")
        );
        curl_setopt($chs[$key_constant], CURLOPT_POST, true);
        curl_setopt($chs[$key_constant], CURLOPT_POSTFIELDS, $content);
        curl_setopt($chs[$key_constant], CURLOPT_TIMEOUT, 20);

        curl_multi_add_handle($mh, $chs[$key_constant]);
        // }

        $running = null;
        do {
            curl_multi_exec($mh, $running);
            // curl_multi_select($mh);
        } while ($running);

        foreach (array_keys($chs) as $key) {
            // echo curl_getinfo($chs[$key], CURLINFO_HTTP_CODE);
            // echo curl_getinfo($chs[$key], CURLINFO_EFFECTIVE_URL);
            // echo "\n";
            curl_multi_remove_handle($mh, $chs[$key]);
        }
        curl_multi_close($mh);

        // foreach(array_keys($chs) as $key){
        //     $jsonString = curl_multi_getcontent($chs[$key]);
        //     array_push($responses, json_decode(html_entity_decode(stripslashes($jsonString))));
        // }
        // return $responses;

        $jsonString = curl_multi_getcontent($chs[$key_constant]);
        return json_decode(html_entity_decode(stripslashes($jsonString)), true);
    }

    public function generateDepositHistory($customer_id, $from = '2022-02-01 00:00:00')
    {
        $last_deposit = Deposit::where('customer_id', $customer_id)->orderBy('created_at', 'DESC')->first();
        $to_datetime = $last_deposit ? new DateTime($last_deposit->created_at) : new DateTime();
        $to_datetime->modify('+1 day');

        $from_datetime = new DateTime($from);
        $from_datetime->modify('-1 day');

        // GET MENUAL DEPOSIT
        // $deposits = Deposit::whereBetween('created_at', [$from_datetime, $to_datetime])
        //     ->where('customer_id', $customer_id)
        //     ->where('note', 'like', '%manual%')
        //     ->orderBy('created_at', 'ASC')
        //     ->get();

        // DELETE DEPOSIT
        // Deposit::whereBetween('created_at', [$from_datetime, $to_datetime])
        //     ->where('customer_id', $customer_id)
        //     ->delete();

        // GET ORDERS
        $orders = Order::whereBetween('created_at', [$from_datetime, $to_datetime])
            ->where('customer_id', $customer_id)
            ->orderBy('created_at', 'ASC')
            ->get();

        // GENERATE DEPOSIT FROM ORDER
        foreach ($orders as $order) {
            $deposit = Deposit::where('order_id', $order->id)->first();
            $data = [
                'customer_id' => $order->customer_id,
                'order_id' => $order->id,
                'amount' => 0,
                'balance' => 0,
                'created_at' => $order->created_at,
                'note' => 'JOB TEMPORARY',
            ];
            if ($deposit) {
                $deposit->update($data);
            } else {
                Deposit::create($data);
            }
        }

        // GET INVOICES
        $invoices = Invoice::whereBetween('created_at', [$from_datetime, $to_datetime])
            ->where('customer_id', $customer_id)
            ->whereNotNull('confirmed_at')
            ->orderBy('confirmed_at', 'ASC')
            ->get();

        // GENERATE DEPOSIT FROM INVOICE
        foreach ($invoices as $invoice) {
            $deposit = Deposit::where('invoice_id', $invoice->id)->first();
            $data = [
                'customer_id' => $invoice->customer_id,
                'invoice_id' => $invoice->id,
                'amount' => 0,
                'balance' => 0,
                'created_at' => $invoice->confirmed_at,
                'note' => 'INVOICE TEMPORARY',
            ];
            if ($deposit) {
                $deposit->update($data);
            } else {
                Deposit::create($data);
            }
        }

        // INSERT MANUAL DEPOSIT
        // foreach ($deposits as $deposit) {
        //     Deposit::create($deposit->toArray());
        // }

        // CHECK AND UPDATE DEPOSIT HISTORY
        $init_deposit = false;

        $from_dt = new DateTime($from);
        // $from_dt->modify('+1 day');
        $first_deposit = Deposit::whereDate('created_at', '<', $from_dt)
            ->where('customer_id', $customer_id)
            ->orderBy('created_at', 'DESC')
            ->first();
        $last_balance = $first_deposit ? ($first_deposit->order ? $first_deposit->order->deposit_balance_after : $first_deposit->balance) : 0;

        $deposits = Deposit::whereBetween('created_at', [$from_datetime, $to_datetime])
            ->where('customer_id', $customer_id)
            ->orderBy('created_at', 'ASC')
            ->get();

        foreach ($deposits as $key => $deposit) {
            // DEPOSIT FROM ORDER
            if ($deposit->order) {
                $order = $deposit->order;
                if ($order->deleted_at) {
                    $deposit->delete();
                } else {
                    $order->update([
                        'deposit_balance_before' => $last_balance,
                    ]);
                    $order->countTotal();
                    // INIT DEPOSIT
                    if ($last_balance == 0 && !$init_deposit && $order->deposit_balance_before != 0) {
                        $init_deposit = true;
                        $created_at = new DateTime($order->created_at);
                        $deposit->update([
                            'customer_id' => $order->customer_id,
                            'amount' => $order->deposit_balance_before ? $order->deposit_balance_before : 0,
                            'balance' => $order->deposit_balance_before ? $order->deposit_balance_before : 0,
                            'created_at' => $created_at->modify('-1 minute'),
                            'note' => 'INITIAL DEPOSIT'
                        ]);
                    }
                    $deposit_data = [];
                    switch ($order->status_id) {
                        // JOB DIAMBIL
                        case 3:
                            // JOB BERJALAN
                        case 2:
                            // JOB BEBAS
                        case 1:
                            $deposit_data = [
                                'customer_id' => $order->customer_id,
                                'order_id' => $order->id,
                                'amount' => $order->amount_deposit_used * -1,
                                'balance' => $order->deposit_balance_after ? $order->deposit_balance_after : 0,
                                'created_at' => $order->created_at,
                                'note' => 'JOB DIBUAT' . (!empty($order->note) ? ': ' . $order->note : '')
                            ];
                            break;

                        // JOB TERKIRIM
                        case 4:
                            $deposit_data = [
                                'customer_id' => $order->customer_id,
                                'order_id' => $order->id,
                                'amount' => $order->amount_deposit_used * -1,
                                'balance' => $order->deposit_balance_after ? $order->deposit_balance_after : 0,
                                'created_at' => $order->created_at,
                                'note' => 'JOB TERKIRIM' . (!empty($order->driver_note) ? ': ' . $order->driver_note : '')
                            ];
                            if ($order->deposit_balance_before != 0 && $order->amount_split_to_cash > 0) {
                                $deposit_data['amount'] = $deposit_data['amount'] + $order->amount_split_to_cash;
                                $deposit_data['balance'] = $deposit_data['balance'] + $order->amount_split_to_cash;
                                // $deposit_data['note'] = 'JOB TERKIRIM';
                            }
                            break;

                        // JOB BATAL
                        case 5:
                            $deposit_data = [
                                'customer_id' => $order->customer_id,
                                'order_id' => $order->id,
                                'amount' => 0,
                                'balance' => $order->deposit_balance_before ? $order->deposit_balance_before : 0,
                                'created_at' => $order->created_at,
                                'note' => 'JOB CANCEL' . (!empty($order->driver_note) ? ': ' . $order->driver_note : ''),
                            ];
                            $order->update([
                                'amount_deposit_used' => 0,
                                'total_after_deposit' => $order->total,
                                'deposit_balance_after' => $order->deposit_balance_before,
                            ]);
                            break;

                        // JOB SELESAI
                        case 6:
                            if ($order->payment_method_confirmed == 'invoice') {
                                $deposit_data = [
                                    'customer_id' => $order->customer_id,
                                    'order_id' => $order->id,
                                    'amount' => 0,
                                    'balance' => $order->deposit_balance_before ? $order->deposit_balance_before : 0,
                                    // 'amount' => $order->deposit_balance_after - $order->deposit_balance_before,
                                    // 'balance' => $order->deposit_balance_after ? $order->deposit_balance_after : 0,
                                    'created_at' => $order->created_at,
                                    'note' => 'JOB CONFIRMED (AR)' . (!empty($order->confirm_note) ? ': ' . $order->confirm_note : null),
                                ];
                                $order->update([
                                    'amount_deposit_used' => 0,
                                    'total_after_deposit' => $order->total,
                                    'deposit_balance_after' => $order->deposit_balance_before,
                                    'amount_pay' => null,
                                    // 'amount_split_to_cash' => null,
                                ]);
                            } else {
                                $deposit_data = [
                                    'customer_id' => $order->customer_id,
                                    'order_id' => $order->id,
                                    'amount' => $order->deposit_balance_after - $order->deposit_balance_before,
                                    'balance' => $order->deposit_balance_after ? $order->deposit_balance_after : 0,
                                    'created_at' => $order->created_at,
                                    'note' => 'JOB CONFIRMED' . (!empty($order->confirm_note) ? ': ' . $order->confirm_note : null),
                                ];
                            }
                            break;

                        default:
                            break;
                    }
                    $deposit->update($deposit_data);
                    $last_balance = $order->deposit_balance_after ? $order->deposit_balance_after : 0;
                    $customer = Customer::find($customer_id);
                    $customer->setDeposit($last_balance);
                }

                // DEPOSIT FROM INVOICE
            } else if ($deposit->invoice) {
                $invoice = $deposit->invoice;
                if ($invoice->deleted_at) {
                    $deposit->delete();
                } else {

                    $amount = $invoice->amount_pay - $invoice->total_after_discount;
                    $last_balance += $amount;
                    $deposit_data = [
                        'customer_id' => $invoice->customer_id,
                        'invoice_id' => $invoice->id,
                        'amount' => $amount,
                        'balance' => $last_balance,
                        'created_at' => $invoice->confirmed_at,
                        'note' => 'INVOICE CONFIRMED' . (!empty($invoice->note_confirm) ? ': ' . $invoice->note_confirm : null),
                    ];
                    $deposit->update($deposit_data);
                    $customer = Customer::find($invoice->customer_id);
                    $customer->setDeposit($last_balance);
                }

                // DEPOSIT MANUAL
            } else {
                $last_balance += $deposit->amount;
                $deposit->update([
                    'balance' => $last_balance,
                ]);
                $customer = Customer::find($customer_id);
                $customer->setDeposit($last_balance);
            }
            // if ($key === array_key_last($deposits)) {
            //     echo 'LAST ELEMENT!';
            // }
            // if ($deposit === end($deposits)) {
            //     $customer = Customer::find($customer_id);
            //     $customer->setDeposit($last_balance);
            // }
        }

        // return $first_deposit;
        return [
            'from_time' => $from_datetime->format('Y-m-d H:i'),
            'to_time' => $to_datetime->format('Y-m-d H:i'),
            'start_at' => $first_deposit ? $first_deposit->created_at : 0,
            'start_balance' => $first_deposit ? $first_deposit->balance : 0,
        ];
    }
}
