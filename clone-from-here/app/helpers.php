<?php

use Illuminate\Support\Facades\Auth;
use App\Models\Order;

if (!function_exists('currentOrders')) {
    function currentOrders()
    {
        if (in_array(Auth::user()->role_id, [5, 6])) {
            return $current_orders = Order::distinct()
                ->where('store_id', Auth::user()->store_id)
                ->whereDate('created_at', date('Y-m-d'))
                ->where('driver_id', Auth::user()->id)
                ->whereIn('status_id', [2, 3])
                ->with([
                    'store',
                    'customer',
                    'address',
                    'products',
                    'driver',
                    'status',
                ])->orderBy('updated_at', 'desc')->get();
        }
        return null;
    }

    function isPageRefreshed()
    {
        $pageRefreshed = isset($_SERVER['HTTP_CACHE_CONTROL']) && ($_SERVER['HTTP_CACHE_CONTROL'] === 'max-age=0' ||  $_SERVER['HTTP_CACHE_CONTROL'] == 'no-cache');
        return $pageRefreshed == 1 ? true : false;
    }
}
