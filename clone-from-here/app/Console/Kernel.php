<?php

namespace App\Console;

// use App\Helper\Helper;
// use App\Models\Setting;
// use GuzzleHttp\Promise\Create;

use App\ScheduleObjects\RecordCustomerStat;
use Illuminate\Console\Scheduling\Schedule;
// use App\Http\Controllers\AccurateApiController;
use App\ScheduleObjects\SendNotifReminder;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->call(function () {
        //     Setting::updateOrCreate(
        //         [
        //             'name' => 'test-schedule',
        //         ],
        //         ['value' => [
        //             'test' => 'success'
        //         ]]
        //     );
        // })->everyMinute();
        $schedule->command('queue:work --daemon --rest=0.5 --sleep=5')->everyMinute()->withoutOverlapping()->runInBackground();
        // $schedule->command('queue:work --daemon')->everyMinute()->withoutOverlapping()->runInBackground();
        $schedule->command('telescope:prune --hours=48')->daily()->withoutOverlapping()->runInBackground();
        $schedule->call(new SendNotifReminder)->cron('0 6,8,10,12,14,16 * * *')->name('send_notif_reminder')->withoutOverlapping()->runInBackground();
        $schedule->call(new RecordCustomerStat)->cron('0 0 28-31 * *')->when(function () {
            return now()->endOfMonth()->isToday();
        })->name('record_customer_last_order_statistic')->withoutOverlapping()->runInBackground();
        // $schedule->call(new SendNotifReminder)->everyMinute()->name('send_notif_reminder')->withoutOverlapping()->runInBackground();

        // $schedule->call(function () {
        //     $helper = new Helper;
        //     $res = $helper->fetchApiAccurate('https://account.accurate.id/api/webhook-renew.do', 'GET', null, null, false, true);
        //     Setting::updateOrCreate(
        //         [
        //             'name' => 'renew_webhook_life',
        //         ],
        //         ['value' => $res]
        //     );
        //     // (new AccurateApiController)->accurateSyncStockDb();
        // })
        //     ->daily()
        //     ->name('renew-webhook-life')
        //     ->withoutOverlapping();
        // /usr/local/bin/php /home/<USER>/public_html/cs2/artisan schedule:run >> /dev/null 2>&1
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
