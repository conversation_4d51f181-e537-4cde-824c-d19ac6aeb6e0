<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
// use Illuminate\Database\Eloquent\Builder;
use App\Helper\Helper;
use Illuminate\Http\Request;
use DateTime;
// use DateInterval;
// use DatePeriod;
// use Illuminate\Support\Facades\DB;

// use App\Models\Area;
// use App\Models\Bank;
// use App\Models\Order;
use App\Models\Armada;
use App\Models\Category;
// use App\Models\Marketing;
use App\Models\CostCategory;
use App\Models\OperatingCost;
// use App\Models\Total;
use App\Models\Store;
use App\Models\User;
use App\Models\Employee;
// use App\Models\Address;
// use App\Models\ProductStore;
use App\Models\OrderProduct;
// use App\Models\Deposit;

// use App\Models\Store;
use Illuminate\Support\Facades\File;
// use Barryvdh\DomPDF\Facade\Pdf;

class OperatingCostsController extends Controller
{
    public function index(Request $request, $store_slug)
    {
        $helper = new Helper();
        $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'joblist');
        if (!$validateStoreSlug['status']) {
            return redirect()->route('joblist', [
                'store_slug' => $validateStoreSlug['store_slug'],
                'date' => $request->query('date'),
                'status' => $request->query('status'),
            ]);
        }
        $date_now = $request->query('date') ? $request->query('date') : date('Y-m-d');

        $store = Store::where('slug', $store_slug)->first();

        $operatingcosts = OperatingCost::distinct()
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereDate('submit_at', $date_now)
            ->whereNotIn('cost_category_id', [1, 2, 3, 4])
            ->whereHas('items', function ($q) {
                $q->whereNull('deleted_at')
                    ->whereNotIn('cost_category_id', [1, 2, 3, 4]);
            })
            ->with([
                'items',
                'items.costcategory',
                'operatingcostreceiptphotos',
                'user',
                'store',
                'costcategory',
                'armada',
                'employee',
            ])
            ->get()
            ->map(function ($oc) {
                $oc->operatingcostreceiptphotos = $oc->operatingcostreceiptphotos ? $oc->operatingcostreceiptphotos->map(
                    function ($photo) {
                        $photo->url = $photo->thumbnail($width = 1000, $height = 1000, $filters = []);
                        // $photo->created = $photo->created_at->format('d-j-y H:i:s');
                        return $photo;
                    }
                ) : null;
                return $oc;
            });

        return view(
            'manager.operatingcost-list',
            compact(
                'store_slug',
                'store',
                'date_now',
                'operatingcosts',
            )
        );
    }

    public function addoperatingcost(Request $request, $store_slug)
    {
        $totalkonsumsitoko = $request->get('totalkonsumsitoko') ?? null;
        $helper = new Helper();
        $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'operatingcost-add');
        if (!$validateStoreSlug['status']) {
            return redirect()->route('operatingcost-add', [
                'store_slug' => $validateStoreSlug['store_slug']
            ]);
        }
        $current_user = User::find(auth()->user()->id)
            ->with([
                'employee',
                'employee.armada',
            ])->first();
        $cost_categories = CostCategory::all();
        if ((int)auth()->user()->role_id <= 2) {
            $stores = Store::all();
        } else {
            $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
        }
        $armadas = Armada::all();

        $store = Store::where('slug', $store_slug)->first();
        $sdm = User::whereIn('role_id', [4, 5, 6])
            ->where('store_id', $store->id)
            ->whereNull('deleted_at')
            ->with([
                'employee',
                // 'employee.armada',
            ])
            ->get();

        // $customers = Customer::distinct()
        // ->whereHas('addresses', function($query) use ($store) {
        //     $query->where('store_id', $store->id);
        // })
        // ->whereHas('orders', function($query) {
        //     $query->where('payment_method_confirmed', 'invoice')
        //         ->where('status_id', 6)
        //         ->where('is_invoiced', 0);
        // })->orderBy('name')
        // ->with([
        //     'addresses',
        //     'merger',
        //     // 'merger.members',
        //     'merger.maincustomer',
        //     'merger.maincustomer.addresses',
        //     ])
        // ->get();

        return view('manager.operatingcost-add', compact(
            'current_user',
            'store_slug',
            'cost_categories',
            'armadas',
            'stores',
            'store',
            'sdm',
            'totalkonsumsitoko',
            // 'customers',
        ));
    }

    public function postoperatingcost(Request $request)
    {
        $helper = new Helper();
        $data = $request->all();
        $store = Store::find($data['store_id']);
        $user = auth()->user();

        // Biaya SDM
        if ((int)$data['cost_category_id'] === 1) {
            foreach ($data["employee_id"] as $key => $employee_id) {
                $operating_cost = OperatingCost::create([
                    'user_id' => $user->id,
                    'store_id' => $data['store_id'],
                    'employee_id' => $employee_id,
                    'cost_category_id' => $data['cost_category_id'],
                    'note' => $data['note'],
                ]);
                foreach ($data["item_cost_category_id"][$key] as $sub_key => $item_cost_category_id) {
                    $cost = $data['item_cost'][$key][$sub_key];
                    $cost = str_replace('Rp', '', $cost);
                    $cost = str_replace('.', '', $cost);
                    $cost = (int)$cost;
                    $operating_cost->items()->create([
                        'cost_category_id' => $item_cost_category_id,
                        'price' => $cost,
                    ]);
                }
                $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'operating-cost';
                if (!File::isDirectory($image_path)) {
                    File::makeDirectory($image_path, 0777, true, true);
                }
                $receipt_photo = $request->file('receipt_photo')[$key];
                if ($receipt_photo) {
                    $helper = new Helper();
                    $data_receipt_photo = $helper->saveImage(
                        'operating-cost',
                        $operating_cost,
                        $receipt_photo,
                        1000,
                        'operatingcostreceiptphotos',
                        'App\Models\OperatingCost',
                    );
                    $is_success = $operating_cost->operatingcostreceiptphotos()->create($data_receipt_photo);
                }
            }

            // Biaya Bulanan Toko & Biaya Armada
        } else {
            $armada = null;
            $employee = null;
            if (isset($data['armada_id'])) {
                $armada = Armada::find($data['armada_id']);
                if ($armada) {
                    $employee = $armada->employee;
                }
            }
            $operating_cost = OperatingCost::create([
                'user_id' => $user->id,
                'store_id' => $data['store_id'],
                'armada_id' => $armada ? $armada->id : null,
                'employee_id' => $employee ? $employee->id : null,
                'cost_category_id' => $data['cost_category_id'],
                'note' => $data['note'],
            ]);
            foreach ($data["item_cost_category_id"] as $key => $item_cost_category_id) {
                $cost = $data['item_cost'][$key];
                $cost = str_replace('Rp', '', $cost);
                $cost = str_replace('.', '', $cost);
                $cost = (int)$cost;
                $operating_cost->items()->create([
                    'cost_category_id' => $item_cost_category_id,
                    'price' => $cost,
                ]);
            }

            $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'operating-cost';
            if (!File::isDirectory($image_path)) {
                File::makeDirectory($image_path, 0777, true, true);
            }
            $receipt_photo = $request->file('receipt_photo');
            if ($receipt_photo) {
                $helper = new Helper();
                $data_receipt_photo = $helper->saveImage(
                    'operating-cost',
                    $operating_cost,
                    $receipt_photo,
                    1000,
                    'operatingcostreceiptphotos',
                    'App\Models\OperatingCost',
                );
                $is_success = $operating_cost->operatingcostreceiptphotos()->create($data_receipt_photo);
            }
        }

        return redirect()->route('jobs', [
            'store_slug' => $store->slug,
        ])->with('success', 'TAMBAH PENGELUARAN TOKO BERHASIL');
    }

    public function getInsentifJob(Request $request)
    {
        $data = $request->all();
        $store = Store::find($data['store_id']);
        $store_insentives = $store->categorystores;
        $categories = Category::where('insentive', '>', 0)->get();
        $begin = new DateTime($data['date_start']);
        $end = new DateTime($data['date_end']);
        $driver_id = Employee::find($data['employee_id'])->user_id;

        $sum_driver_insentive = 0;
        //? Loop Categories inside Driver
        foreach ($categories as $category) {
            $sum_qty = OrderProduct::whereHas('order', function ($q) use ($data, $driver_id, $begin, $end) {
                $q
                    ->whereNull('deleted_at')
                    ->where('store_id', $data['store_id'])
                    ->where('driver_id', $driver_id)
                    ->whereIn('status_id', [4, 6, 7])
                    ->whereBetween('created_at', [$begin, $end]);
            })
                ->whereHas('product', function ($q) use ($category) {
                    $q
                        ->whereHas('categories', function ($sq) use ($category) {
                            $sq
                                ->where('id', $category->id);
                        });
                })
                ->sum('qty');

            $store_insentive = $store_insentives->firstWhere('category_id', $category->id);
            $insentive = $store_insentive && $store_insentive->insentive ? $store_insentive->insentive : $category->insentive;
            $insentive_result = round(($sum_qty * $insentive) / 1000);
            $sum_driver_insentive += $insentive_result;
        }
        //? END - Loop Categories inside Driver

        return $sum_driver_insentive;
    }
}
