<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
// use Illuminate\Database\Eloquent\Builder;
use App\Helper\Helper;
use Illuminate\Http\Request;
use DateTime;
// use DateInterval;
// use DatePeriod;
// use Illuminate\Support\Facades\DB;

// use App\Models\Area;
use App\Models\Bank;
use App\Models\Order;
// use App\Models\Marketing;
use App\Models\Invoice;
// use App\Models\Total;
// use App\Models\User;
use App\Models\Store;
use App\Models\Customer;
// use App\Models\Address;
// use App\Models\ProductStore;
// use App\Models\OrderProduct;
// use App\Models\Deposit;

// use App\Models\Store;
use Illuminate\Support\Facades\File;
use Barryvdh\DomPDF\Facade\Pdf;

class InvoicesController extends Controller
{
    public function addinvoice(Request $request, $store_slug)
    {
        $helper = new Helper();
        $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'invoice-add');
        if (!$validateStoreSlug['status']) {
            return redirect()->route('invoice-add', [
                'store_slug' => $validateStoreSlug['store_slug']
            ]);
        }
        if ((int)auth()->user()->role_id <= 2) {
            $stores = Store::all();
        } else {
            $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
        }

        $store = Store::where('slug', $store_slug)->first();

        $customers = Customer::distinct()
            ->whereHas('addresses', function ($query) use ($store) {
                $query->where('store_id', $store->id);
            })
            ->whereHas('orders', function ($query) {
                $query->where('payment_method_confirmed', 'invoice')
                    ->where('status_id', 6)
                    ->where('is_invoiced', 0);
            })->orderBy('name')
            ->with([
                'addresses',
                'merger',
                // 'merger.members',
                'merger.maincustomer',
                'merger.maincustomer.addresses',
            ])
            ->get();

        return view('manager.invoice-add', compact(
            'store_slug',
            'stores',
            'store',
            'customers',
        ));
    }

    public function getcustomers(Request $request, $store_id)
    {
        $helper = new Helper();
        $store = Store::find($store_id);
        abort_if(!$store, 400, 'Toko tidak ditemukan');
        $validateStoreSlug = $helper->checkUserStoreSlug($store->slug, 'invoice');
        abort_if(!$validateStoreSlug['status'], 400, 'Invalid store');

        $customers = Customer::distinct()
            ->whereHas('addresses', function ($query) use ($store) {
                $query->where('store_id', $store->id);
            })
            ->whereHas('orders', function ($query) {
                $query->where('payment_method_confirmed', 'invoice')
                    ->where('status_id', 6)
                    ->where('is_invoiced', 0);
            })->orderBy('name')
            ->with([
                'addresses',
                'merger',
                // 'merger.members',
                'merger.maincustomer',
                'merger.maincustomer.addresses',
            ])
            ->get();

        return response()->json(
            [
                'success' => true,
                'customers' => $customers,
            ],
            200
        );
    }

    public function getorders(Request $request, $customer_id)
    {
        $helper = new Helper();
        $customer = Customer::where('id', $customer_id)
            ->with([
                'merger',
                'merger.members',
            ])
            ->first();
        abort_if(!$customer, 400, 'Pelanggan tidak ditemukan');
        // $validateStoreSlug = $helper->checkUserStoreSlug($store->slug);
        // abort_if(!$validateStoreSlug['status'], 400, 'Invalid store');

        $orders = Order::distinct()
            ->where('customer_id', $customer->id)
            ->where('status_id', 6)
            ->where('is_invoiced', 0)
            ->where('payment_method_confirmed', 'invoice')
            ->orderBy('created_at', 'asc')
            ->with(['products'])
            ->get();
        // ->toArray();

        if ($customer->merger) {
            foreach ($customer->merger->members as $key => $cus) {
                $ord = Order::distinct()
                    ->where('customer_id', $cus->id)
                    ->where('status_id', 6)
                    ->where('is_invoiced', 0)
                    ->where('payment_method_confirmed', 'invoice')
                    ->orderBy('created_at', 'asc')
                    ->with(['products'])
                    ->get();
                $orders = $orders->merge($ord);
            }
        }

        return response()->json(
            [
                'success' => true,
                'orders' => $orders,
            ],
            200
        );
    }

    public function postinvoice(Request $request, $store_slug)
    {
        $helper = new Helper();
        // $validateStoreSlug = $helper->checkUserStoreSlug($store_slug);
        // abort_if(!$validateStoreSlug['status'], 400, 'Invalid store');

        $data = $request->all();
        $store = Store::find($data['store_id']);
        $helper = new Helper();
        $created_at = new DateTime();

        $discount = $data['discount'];
        if (!empty($discount)) {
            $discount = str_replace('Rp', '', $discount);
            $discount = str_replace('.', '', $discount);
            $discount = intval($discount);
        }

        $invoice = Invoice::create([
            'store_id' => $data['store_id'],
            'customer_id' => $data['customer_id'],
            'receiver_phone' => $helper->convertPhone($data['receiver_phone']),
            'total_bill' => $data['total_bill'],
            'discount' => $discount,
            'total_after_discount' => $data['total_after_discount'],
            'note' => $data['note'],
            'created_at' => $created_at,
        ]);

        $invoice->orders()->sync($data['order_ids']);

        Order::whereIn('id', $data['order_ids'])
            ->update([
                'is_invoiced' => 1,
            ]);

        return redirect()->route('job-list', [
            'store_slug' => $store->slug,
            'type' => 'invoice',
        ])->with('success', 'TAMBAH INVOICE BERHASIL');
    }

    public function downloadinvoice($invoice_id)
    {
        $path = 'public/gasplus/invoices';
        if (!File::isDirectory($path)) {
            File::makeDirectory($path, 0777, true, true);
        }

        $invoice = Invoice::where('id', $invoice_id)
            ->with([
                'orders',
                'orders.products',
                'customer',
                'customer.addresses',
                'store',
                'store.banks',
                'bank',
                'confirmedby',
            ])
            ->first();

        $file_name = $invoice->code . '.pdf';

        $pdf = Pdf::loadView('pdf.invoice', ['invoice' => $invoice])
            ->save($path . DIRECTORY_SEPARATOR . $file_name);

        // return view('pdf.invoice', compact(
        //     'invoice',
        // ));
        return $pdf->stream($invoice->code . '-' . time() . '.pdf');
        // return $pdf->download($file_name);   
    }

    public function downloadinvoicemanual()
    {
        // $path = 'public/gasplus/invoices';
        // if (!File::isDirectory($path)) {
        //     File::makeDirectory($path, 0777, true, true);
        // }

        // $invoice = Invoice::where('id', $invoice_id)
        //     ->with([
        //         'orders',
        //         'orders.products',
        //         'customer',
        //         'customer.addresses',
        //         'store',
        //         'store.banks',
        //         'bank',
        //         'confirmedby',
        //     ])
        //     ->first();
        $invoice = (object) [
            'code' => 'INV-C220830-3A',
            'store' => (object) [
                'name' => 'Gasplus Condongcatur',
                'whatsapp_1' => '************',
                'address' => 'Jl. Rajawali Raya No.56',
                'banks' => [
                    (object) [
                        'bank_name' => 'BCA',
                        'account_number' => '**********',
                        'holder_name' => 'Nitisari Setiadi',
                    ]
                ]
            ],
            'customer' => (object) [
                'name' => 'Panda Food Factory',
                'phone' => '************',
                'addresses' =>  [
                    (object) [
                        'address' => 'Panda Food Factory, Jl. Raya Krangkungan No.23, Sanggrahan, Condongcatur, Kec. Depok, Kabupaten Sleman, Daerah Istimewa Yogyakarta 55281'
                    ]
                ]
            ],
            'created_at' => '30-08-2022',
            'orders' => [
                (object) [
                    'code' => 'C220821-11F',
                    'created_at' => '21-08-2022',
                    'products' => [
                        (object) [
                            'code' => 'IP',
                            'name' => 'Isi Pelangi',
                            'pivot' => (object) [
                                'price' => 16000,
                                'qty' => 6,
                            ],
                        ],
                    ],
                ],
                (object) [
                    'code' => 'C220823-8F',
                    'created_at' => '23-08-2022',
                    'products' => [
                        (object) [
                            'code' => 'IP',
                            'name' => 'Isi Pelangi',
                            'pivot' => (object) [
                                'price' => 16000,
                                'qty' => 6,
                            ],
                        ],
                    ],
                ],
                (object) [
                    'code' => 'C220824-16B',
                    'created_at' => '24-08-2022',
                    'products' => [
                        (object) [
                            'code' => 'IP',
                            'name' => 'Isi Pelangi',
                            'pivot' => (object) [
                                'price' => 16000,
                                'qty' => 6,
                            ],
                        ],
                    ],
                ],
                (object) [
                    'code' => 'C220825-7O',
                    'created_at' => '25-08-2022',
                    'products' => [
                        (object) [
                            'code' => 'IP',
                            'name' => 'Isi Pelangi',
                            'pivot' => (object) [
                                'price' => 16000,
                                'qty' => 7,
                            ],
                        ],
                    ],
                ],
                (object) [
                    'code' => 'C220826-5X',
                    'created_at' => '26-08-2022',
                    'products' => [
                        (object) [
                            'code' => 'IP',
                            'name' => 'Isi Pelangi',
                            'pivot' => (object) [
                                'price' => 16000,
                                'qty' => 4,
                            ],
                        ],
                        (object) [
                            'code' => 'KA220',
                            'name' => 'Aqua 220ml',
                            'pivot' => (object) [
                                'price' => 35000,
                                'qty' => 2,
                            ],
                        ],
                    ],
                ],
                (object) [
                    'code' => 'C220827-18X',
                    'created_at' => '27-08-2022',
                    'products' => [
                        (object) [
                            'code' => 'IP',
                            'name' => 'Isi Pelangi',
                            'pivot' => (object) [
                                'price' => 16000,
                                'qty' => 5,
                            ],
                        ],
                    ],
                ],
            ],
            'discount' => 17000,
        ];

        $file_name = $invoice->code . '.pdf';

        $pdf = Pdf::loadView('pdf.invoice-manual', ['invoice' => $invoice]);
        // ->save($path.DIRECTORY_SEPARATOR.$file_name);

        // return view('pdf.invoice', compact(
        //     'invoice',
        // ));
        return $pdf->stream($invoice->code . '-' . time() . '.pdf');
        // return $pdf->download($file_name);   
    }

    public function cancelinvoice($invoice_id)
    {
        $helper = new Helper();

        $invoice = Invoice::find($invoice_id);
        abort_if(!$invoice, 400, 'Invoice tidak ditemukan');

        $invoice->orders()->update([
            'is_invoiced' => 0,
        ]);

        $invoice->delete();

        return response()->json(
            [
                'success' => true,
            ],
            200
        );
    }

    public function confirminvoice(Request $request)
    {
        $helper = new Helper();
        $data = $request->all();

        $invoice = Invoice::find($data['invoice_id']);
        abort_if(!$invoice, 400, 'Invoice tidak ditemukan');

        $amount_pay = $data['amount_pay'];
        if (!empty($amount_pay)) {
            $amount_pay = str_replace('Rp', '', $amount_pay);
            $amount_pay = str_replace('.', '', $amount_pay);
            $amount_pay = intval($amount_pay);
        }

        $invoice->amount_pay = $amount_pay;
        $invoice->confirmed_by = auth()->user()->id;
        $invoice->confirmed_at = date('Y-m-d H:i:s');
        $invoice->note_confirm = isset($data['note_confirm']) && !empty($data['note_confirm']) ? $data['note_confirm'] : null;
        $invoice->bank_id = intval($data['bank_id']);
        $bank = Bank::find($data['bank_id']);
        if (str_contains(strtolower($bank->bank_name), 'qris')) {
            $invoice->payment_method_confirmed = 'qris';
        } else {
            $invoice->payment_method_confirmed = 'transfer';
        }
        $invoice->save();

        $helper->generateDepositHistory($invoice->customer_id, $invoice->confirmed_at);

        return response()->json(
            [
                'success' => true,
                'invoice' => $invoice,
            ],
            200
        );
    }
}