<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Helper\Helper;
use App\Helper\HelperSocialchat;
use Illuminate\Http\Request;
use App\Models\Setting;
use App\Models\Socialchat;
// use App\Models\Address;
// use App\Models\Customer;
use App\Models\Store;
// use App\Models\Product;
use App\Models\Order;
// use PhpOffice\PhpSpreadsheet\IOFactory;
// use DateTime;
// use Illuminate\Support\Facades\Storage;

class SocialchatApiController extends Controller
{
    public function test()
    {
        return 'TEST';
        // $helper = new Helper;
        // $order = Order::find('232169');
        // $msg = $helper->createOrderMessage($order);
        // $msg = str_replace(PHP_EOL, '', $msg);
        // $msg = str_replace('<br>', PHP_EOL, $msg);
        // dd($msg);
        // return '';
        $helperSocialchat = new HelperSocialchat;
        $store = Store::find(8);
        return $res = $helperSocialchat->getChannelId($store);
        // return $res = $helperSocialchat->getConversationId('6281331885989');
        // $res = Setting::where('name', 'socialchat_conversation')->first();
        // return $res->value['_id'];


        $appId = env('SOCIALCHAT_APP_ID');
        $secretKey = env('SOCIALCHAT_SECRET_KEY');
        $bearer = base64_encode($appId . '_' . $secretKey);
        $params = [
            'page' => 1,
            'limit' => 10,
            'channelIds' => [
                // '628112926455:<EMAIL>'
                '663366f1c39e7f818e2b86ea'
            ],
            'channelTypes' => [
                'whatsapp-unofficial',
            ],
            // // 'search' => 'Gasplus',
            'search' => '6285888886869',
            'fromDate' => '2024-05-01T06:08:33.000Z',
            'toDate' => '2024-05-04T06:08:33.000Z',
            // 'id' => '<EMAIL><EMAIL>',
        ];
        // $url = 'https://api.socialchat.id/partner/channel?' . http_build_query($params);
        // $url = 'https://api.socialchat.id/partner/conversation?' . http_build_query($params);
        // $url = 'https://api.socialchat.id/partner/message/6633707caf9ef08bd39b7b96?' . http_build_query($params);
        $url = 'https://api.socialchat.id/partner/message/6633707caf9ef08bd39b7b96';
        $data = [
            // "name" => "Socialchat",
            // "phone" => "08112926455",
            "text" => "Coba lagi",
            // "text" => $msg,
        ];
        $postdata = http_build_query($data);
        $opts = array(
            'http' =>
            array(
                // 'method'  => 'GET',
                'method'  => 'POST',
                'header'  => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'Authorization: Bearer ' . $bearer,
                ],
                'content' => $postdata
            )
        );
        $context  = stream_context_create($opts);
        $result = @file_get_contents($url, false, $context);
        $result = json_decode($result);
        if (!empty($result->error_message)) {
            return $result->error_message;
        }
        return $result;
    }

    public function getConversationId(Request $request)
    {
        $data = $request->all();
        $helper = new Helper;
        $helperSocialchat = new HelperSocialchat;
        $conversation_id = "";
        $customer_id = $data['customer_id'];
        $store_id = $data['store_id'];
        $phone = $helper->convertPhone($data['phone']);
        $socialchat = Socialchat::where("store_id", $store_id)
            ->where('customer_id', $customer_id)
            ->where('phone', $phone)
            ->orderBy('id', 'desc')
            ->first();
        if ($socialchat) {
            $conversation_id = $socialchat->conversation_id;
        } else {
            $store = Store::find($store_id);
            $conversation_id = $helperSocialchat->getConversationId($store, $phone);
            if ($conversation_id) {
                Socialchat::create([
                    'store_id' => $store_id,
                    'customer_id' => $customer_id,
                    'phone' => $phone,
                    'conversation_id' => $conversation_id,
                ]);
            }
        }

        return response()->json(
            [
                'conversation_id' => $conversation_id,
            ],
            200
        );
    }
}
