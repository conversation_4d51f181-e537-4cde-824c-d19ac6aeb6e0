<?php

namespace App\Http\Controllers;

use DateTime;
// use DatePeriod;
// use DateInterval;
use Carbon\Carbon;
use App\Models\Area;
use App\Models\User;
use App\Models\Order;
use App\Models\Store;
// use App\Models\Total;
use App\Helper\Helper;
use App\Models\Address;
// use App\Models\Invoice;
// use App\Models\Category;
use App\Models\Deposit;
use App\Models\Product;
use App\Models\Customer;
use App\Helper\HelperIak;
use App\Models\Marketing;
use App\Models\Socialchat;
use App\Models\OrderProduct;
// use App\Models\AdditionalCost;
use App\Models\ProductStore;
use Illuminate\Http\Request;
// use App\Models\OrderProduct;
use App\Models\CustomerProduct;
// use App\Models\OperatingCostItem;
// use Illuminate\Support\Facades\DB;
use App\Models\PpobInquiryData;
use App\Helper\HelperSocialchat;
use App\Models\PpobCustomerData;
// use Illuminate\Database\Eloquent\Builder;
use App\Models\PpobResponseData;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

// use App\Models\Store;

class OrdersController extends Controller
{

  public function create(Request $request, $store_slug)
  {
    // $type = $request->get('type');
    // $is_asdt = $type === 'asdt';
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'order-add');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('order-add', [
        'store_slug' => $validateStoreSlug['store_slug'],
      ]);
    }
    $stores = Store::all();

    $store = Store::where('slug', $store_slug)->first();
    $drivers = User::whereIn('role_id', [5, 6])
      ->whereHas('store', function ($q) use ($store_slug) {
        $q->where('slug', $store_slug);
      })
      ->get();

    return view(
      'manager.order-add-vue',
      compact(
        'store_slug',
        'stores',
        // 'type',
        // 'is_asdt',
        'store',
        'drivers',
      )
    );
  }

  public function store(Request $request)
  {
    $data = $request->all();

    $order_type = $data['order_type'];

    $helper = new Helper();

    if (isset($data['customer_phone'])) {
      $data['customer_phone'] = $helper->convertPhone($data['customer_phone']);
    }

    if ($order_type === 'old') {
      $rules = [
        'created_at' => 'required',
        'store_id' => 'required',
        'address_id' => 'required',
        'customer_id' => 'required',
        'products.*.product_id' => 'required',
        'products.*.qty' => 'required_if:products.*.is_ppob,false',
        'products.*.ppob_key' => 'required_if:products.*.is_ppob,true',
        'products.*.ppobkey_inquiry_data' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_product_code' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_nominal' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_price' => 'required_if:products.*.is_ppob,true',
        'payment' => 'required',
        'store_id_another_address' => 'required_if:is_add_another_address,true',
        'another_address' => 'required_if:is_add_another_address,true',
      ];
      $messages = [
        'created_at.required' => 'Tanggal & Jam is required.',
        'store_id.required' => 'Toko is required.',
        'address_id.required' => 'Alamat is required.',
        'customer_id.required' => 'Pelanggan is required.',
        'products.*.product_id.required' => 'Produk is required.',
        'products.*.qty.required_if' => 'Qty is required.',
        'products.*.ppob_key.required_if' => 'ID Pelanggan is required.',
        'products.*.ppobkey_inquiry_data.required_if' => 'Data Pelanggan is required.',
        'products.*.ppob_product_code.required_if' => 'Product Code is required.',
        'products.*.ppob_nominal.required_if' => 'Nominal is required.',
        'products.*.ppob_price.required_if' => 'Price is required.',
        'payment.required' => 'Pembayaran is required.',
        'store_id_another_address.required_if' => 'Toko is required.',
        'another_address.required_if' => 'Alamat Lain Pelanggan is required.',
      ];
    } else if ($order_type === 'new') {
      $rules = [
        'created_at' => 'required',
        'store_id_new' => 'required',
        'customer_name' => 'required',
        'customer_phone' => ['required', 'unique:customers,phone'],
        'customer_address' => 'required',
        'products.*.product_id' => 'required',
        'products.*.qty' => 'required_if:products.*.is_ppob,false',
        'products.*.ppob_key' => 'required_if:products.*.is_ppob,true',
        'products.*.ppobkey_inquiry_data' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_product_code' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_nominal' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_price' => 'required_if:products.*.is_ppob,true',
        'payment' => 'required',
      ];
      $messages = [
        'created_at.required' => 'Tanggal & Jam is required.',
        'store_id_new.required' => 'Toko is required.',
        'customer_name.required' => 'Nama Pelanggan is required.',
        'customer_phone.required' => 'No. WA Pelanggan is required.',
        'customer_phone.unique' => 'No. WA Pelanggan sudah terdaftar.',
        'customer_address.required' => 'Alamat is required.',
        'products.*.product_id.required' => 'Produk is required.',
        'products.*.qty.required_if' => 'Qty is required.',
        'products.*.ppob_key.required_if' => 'ID Pelanggan is required.',
        'products.*.ppobkey_inquiry_data.required_if' => 'Data Pelanggan is required.',
        'products.*.ppob_product_code.required_if' => 'Nominal is required.',
        'products.*.ppob_nominal.required_if' => 'Nominal is required.',
        'products.*.ppob_price.required_if' => 'Nominal is required.',
        'payment.required' => 'Pembayaran is required.',
      ];
    } else if ($order_type === 'asdt') {
      $rules = [
        'created_at' => 'required',
        'store_id' => 'required',
        // 'address_id' => 'required',
        // 'customer_id' => 'required',
        'products.*.product_id' => 'required',
        'products.*.qty' => 'required_if:products.*.is_ppob,false',
        'products.*.ppob_key' => 'required_if:products.*.is_ppob,true',
        'products.*.ppobkey_inquiry_data' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_product_code' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_nominal' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_price' => 'required_if:products.*.is_ppob,true',
        'payment' => 'required',
      ];
      $messages = [
        'created_at.required' => 'Tanggal & Jam is required.',
        'store_id.required' => 'Toko is required.',
        // 'address_id.required' => 'Alamat is required.',
        // 'customer_id.required' => 'Pelanggan is required.',
        'products.*.product_id.required' => 'Produk is required.',
        'products.*.qty.required_if' => 'Qty is required.',
        'products.*.ppob_key.required_if' => 'ID Pelanggan is required.',
        'products.*.ppobkey_inquiry_data.required_if' => 'Data Pelanggan is required.',
        'products.*.ppob_product_code.required_if' => 'Product Code is required.',
        'products.*.ppob_nominal.required_if' => 'Nominal is required.',
        'products.*.ppob_price.required_if' => 'Price is required.',
        'payment.required' => 'Pembayaran is required.',
      ];
    } else if ($order_type === 'konsumsitoko') {
      $rules = [
        'created_at' => 'required',
        'store_id' => 'required',
        // 'address_id' => 'required',
        // 'customer_id' => 'required',
        'products.*.product_id' => 'required',
        'products.*.qty' => 'required_if:products.*.is_ppob,false',
        'products.*.ppob_key' => 'required_if:products.*.is_ppob,true',
        'products.*.ppobkey_inquiry_data' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_product_code' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_nominal' => 'required_if:products.*.is_ppob,true',
        'products.*.ppob_price' => 'required_if:products.*.is_ppob,true',
        'payment' => 'required',
      ];
      $messages = [
        'created_at.required' => 'Tanggal & Jam is required.',
        'store_id.required' => 'Toko is required.',
        // 'address_id.required' => 'Alamat is required.',
        // 'customer_id.required' => 'Pelanggan is required.',
        'products.*.product_id.required' => 'Produk is required.',
        'products.*.qty.required_if' => 'Qty is required.',
        'products.*.ppob_key.required_if' => 'ID Pelanggan is required.',
        'products.*.ppobkey_inquiry_data.required_if' => 'Data Pelanggan is required.',
        'products.*.ppob_product_code.required_if' => 'Product Code is required.',
        'products.*.ppob_nominal.required_if' => 'Nominal is required.',
        'products.*.ppob_price.required_if' => 'Price is required.',
        'payment.required' => 'Pembayaran is required.',
      ];
    } else if ($order_type === 'marketing') {
      $rules = [
        'created_at' => 'required',
        'store_id_marketing' => 'required',
        'area_id' => 'required',
        'latlng' => 'required',
        'driver_id' => 'required',
      ];
      $messages = [
        'created_at.required' => 'Tanggal & Jam is required.',
        'store_id_marketing.required' => 'Toko is required.',
        'area_id.required' => 'Perumahan is required.',
        'latlng.required' => 'Koordinat is required.',
        'driver_id.required' => 'Delman is required.',
      ];
    }
    $validation = Validator::make($data, $rules, $messages);
    if ($validation->fails()) {
      return response()->json(['errors' => $validation->errors()->toArray()], 422);
    }

    // return response()->json([
    //   'message' => 'Data processed successfully',
    //   'validation' => $validation,
    //   'data' => $data,
    // ]);

    $store_id = null;
    $is_with_product = false;
    $is_asdt_konsumsitoko = $order_type === 'asdt' || $order_type === 'konsumsitoko';
    $is_konsumsitoko = $order_type === 'konsumsitoko';
    $is_asdt = $order_type === 'asdt';
    $is_marketing = $order_type === 'marketing';
    if ($order_type === 'old') {
      if (isset($data['is_add_another_address']) && $data['is_add_another_address']) {
        $store_id = $data['store_id_another_address'];
      } else {
        $store_id = $data['store_id_old'];
      }
    } else if ($order_type == 'new') {
      $store_id = $data['store_id_new'];
    } else if ($order_type == 'marketing') {
      $store_id = $data['store_id_marketing'];
    } else if ($is_asdt_konsumsitoko) {
      $store_id = $data['store_id'];
    }
    $store = Store::find($store_id);
    $created_at = new DateTime($data['created_at']);

    // JOB MERKETING / BAGI BROSUR
    if ($order_type === 'marketing') {
      $area_id = $data['area_id'];
      if (!is_numeric($area_id)) {
        $area_id = Area::create([
          'store_id' => $store->id,
          'name' => $data['area_id'],
          'latlng' => $data['latlng'],
        ])->id;
      } else {
        Area::where('id', $data['area_id'])
          ->update([
            'latlng' => $data['latlng'],
          ]);
      }

      $order = Marketing::create([
        'store_id' => $store->id,
        'area_id' => $area_id,
        'driver_id' => $data['driver_id'],
        'status_id' => 3,
        'note_for_driver' => isset($data['note_for_driver']) && !empty($data['note_for_driver']) ? $data['note_for_driver'] : null,
        'created_at' => $created_at,
      ]);
    } else if ($is_asdt_konsumsitoko) {
      // ASDT / KONSUMSI TOKO
      $keyword = $order_type === 'konsumsitoko' ? 'KONSUMSI TOKO' : 'ASDT';
      $address = Address::where('store_id', $store->id)
        ->whereHas('customer', function ($q) use ($keyword) {
          $q->where('name', 'like', '%' . $keyword . '%')
            ->whereNull('deleted_at');
        })
        ->first();
      if (!$address) return response()->json(['errors' => ['asdt' => ['Data pelanggan ' . $order_type . ' tidak ditemukan!']],], 422);
      $order = Order::create([
        'store_id' => $store->id,
        'customer_id' => $address->customer->id,
        'address_id' => $address->id,
        'status_id' => 4,
        'payment' => $data['payment'] ?? null,
        'payment_method_ask' => isset($data['payment']) && !empty($data['payment']) ? ($data['payment'] === 'cash' ? 'cash' : 'non-cash') : null,
        'receiver_phone' => isset($data['receiver_phone']) && !empty($data['receiver_phone']) ? $helper->convertPhone($data['receiver_phone']) : null,
        'note' => isset($data['note']) && !empty($data['note']) ? $data['note'] : null,
        'note_for_driver' => isset($data['note_for_driver']) && !empty($data['note_for_driver']) ? $data['note_for_driver'] : null,
        'created_at' => $created_at,
        'received_by' => $address->customer->name,
        'received_at' => $created_at,
      ]);
      $data_products = [];
      foreach ($data["products"] as $product) {
        $product_id = $product['product_id'];
        if ($product_id) {
          $productstore = ProductStore::where('store_id', $store->id)
            ->where('product_id', $product_id)
            ->first();
          $customerproduct = CustomerProduct::where('customer_id', $address->customer->id)
            ->where('product_id', $product_id)
            ->first();
          $local_price = $productstore ? ($productstore->local_price ? $productstore->local_price : $productstore->product->price) : 0;
          $special_price = $customerproduct && $customerproduct->local_price ? $customerproduct->local_price : $local_price;
          $data_products[$product_id] = [
            'qty' => $product['qty'],
            'price' => $special_price,
          ];
        }
      }
      $is_with_product = count($data_products) > 0;
      if ($is_with_product) {
        // $order->products()->sync($data_products);
        foreach ($data_products as $id => $product) {
          OrderProduct::create([
            'order_id' => $order->id,
            'product_id' => $id,
            'qty' => $product['qty'],
            'price' => $product['price'],
          ]);
        }
        // $order->setAdditionalCost();
        $order->countTotal();
        Deposit::create([
          'order_id' => $order->id,
          'customer_id' => $order->customer_id,
          'amount' => ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) ? ($order->amount_deposit_used * -1) : 0,
          'balance' => $order->deposit_balance_after,
          'note' => 'ADD JOB',
        ]);
        if ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) {
          $order->customer->setDeposit($order->deposit_balance_after);
        }
      }
    } else {
      // JOB ORDER

      $is_secondfloor = isset($data['is_secondfloor']) ? $data['is_secondfloor'] : 0;
      if ($order_type == 'new') {
        $customer_id = Customer::create([
          'name' => $data['customer_name'],
          'phone' => $helper->convertPhone($data['customer_phone']),
          'payment' => isset($data['payment']) && !empty($data['payment']) ? $data['payment'] : null,
          'note' => isset($data['note']) && !empty($data['note']) ? $data['note'] : null,
        ])->id;
        $address_id = Address::create([
          'store_id' => $store->id,
          'customer_id' => $customer_id,
          'address' => $data['customer_address'],
          'is_secondfloor' => $is_secondfloor,
          'latlng' => isset($data['customer_latlng']) && !empty($data['customer_latlng']) ? $data['customer_latlng'] : null,
        ])->id;
      } else {
        if (isset($data['is_add_another_address']) && $data['is_add_another_address']) {
          $address = Address::find($data['address_id']);
          $customer_id = $address->customer_id;
          $address_id = Address::create([
            'store_id' => $store->id,
            'customer_id' => $customer_id,
            'address' => $data['another_address'],
            'is_secondfloor' => isset($data['another_address_is_secondfloor']) ? $data['another_address_is_secondfloor'] : 0,
            'latlng' => isset($data['another_address_latlng']) && !empty($data['another_address_latlng']) ? $data['another_address_latlng'] : null,
          ])->id;
        } else {
          $address = Address::find($data['address_id']);
          $address->update([
            'is_secondfloor' => $is_secondfloor,
          ]);
          $customer_id = $address->customer_id;
          $address_id = $address->id;
        }
      }
      // if (date('H') < 8 && date('H') > 6) {
      //     $created_at->setTime(8, 0, 0);
      // }
      $options = null;
      if (isset($data['is_hide_deposit']) && !empty($data['is_hide_deposit'])) {
        $options['is_hide_deposit'] = true;
      }
      $order = Order::create([
        'store_id' => $store->id,
        'customer_id' => $customer_id,
        'address_id' => $address_id,
        'status_id' => 1,
        'payment' => isset($data['payment']) && !empty($data['payment']) ? $data['payment'] : null,
        'note' => isset($data['note']) && !empty($data['note']) ? $data['note'] : null,
        'note_for_driver' => isset($data['note_for_driver']) && !empty($data['note_for_driver']) ? $data['note_for_driver'] : null,
        'receiver_phone' => isset($data['receiver_phone']) && !empty($data['receiver_phone']) ? $helper->convertPhone($data['receiver_phone']) : null,
        'created_at' => $created_at,
        'is_urgent' => isset($data['is_urgent']) && !empty($data['is_urgent']) ? $data['is_urgent'] : 0,
        'options' => $options,
      ]);

      // Job Products
      foreach ($data["products"] as $product) {
        if ($product['product_id']) {
          $data_product = $product;
          $productstore = ProductStore::where('store_id', $store->id)
            ->where('product_id', $product['product_id'])
            ->first();
          $customerproduct = CustomerProduct::where('customer_id', $customer_id)
            ->where('product_id', $product['product_id'])
            ->first();
          $local_price = $productstore ? ($productstore->local_price ? $productstore->local_price : $productstore->product->price) : 0;
          $special_price = $customerproduct && $customerproduct->local_price ? $customerproduct->local_price : $local_price;
          $data_product['order_id'] = $order->id;
          $data_product['price'] = $special_price;

          if ($product['is_ppob']) {
            $gasplus_product = Product::find($product['product_id']);
            //? Air PDAM
            if ($gasplus_product->code === 'AIR_PDAM' && !empty($data_product['ppobkey_inquiry_data'])) {
              // return $data_product;
              $data_product['price'] = (int) $special_price + (int) $data_product['ppobkey_inquiry_data']['price'];
              $data_product['ppob_ref_id'] = $data_product['ppobkey_inquiry_data']['ref_id'];
              $data_product['ppob_tr_id'] = $data_product['ppobkey_inquiry_data']['tr_id'];
              $data_product['ppob_product_code'] = $data_product['ppobkey_inquiry_data']['code'];
              $data_product['ppob_label'] = $data_product['ppobkey_inquiry_data']['tr_name'];
              $data_product['ppob_key'] = $data_product['ppobkey_inquiry_data']['hp'];
              PpobInquiryData::updateOrCreate(
                [
                  'ppob_ref_id' => $data_product['ppobkey_inquiry_data']['ref_id'],
                  'ppob_product_code' => $data_product['ppobkey_inquiry_data']['code'],
                  'ppob_key' => $data_product['ppobkey_inquiry_data']['hp'],
                  'product_id' => $data_product['product_id'],
                  'customer_id' => $order->customer_id,
                ],
                [
                  'data' => $data_product['ppobkey_inquiry_data'],
                ]
              );
              $helper_iak = new HelperIak;
              $res = $helper_iak->fetchApiIakPostpaid('/api/v1/bill/check', 'POST', [], [
                'commands' => 'pay-pasca',
                'tr_id' => $data_product['ppobkey_inquiry_data']['tr_id'],
              ], $data_product['ppobkey_inquiry_data']['tr_id']);
              if ($res && isset($res->response_code)) {
                switch ($res->response_code) {
                  case "00":
                    $data_product['ppob_status'] = 'SUCCESS';
                    break;
                  case "39":
                    $data_product['ppob_status'] = 'PROCESS';
                    break;
                  default:
                    $data_product['ppob_status'] = 'FAILED';
                    break;
                }
                $ppob_response_data = PpobResponseData::updateOrCreate(
                  [
                    'ppob_ref_id' => $data_product['ppob_ref_id'],
                    // 'order_product_id' => $order_product->id,
                    'product_id' => $data_product['product_id'],
                    'customer_id' => $order->customer_id,
                  ],
                  [
                    'response_data' => $res,
                    'error_message' => $res->response_code !== "00" ? $res->message : null,
                  ]
                );
                $data_product['ppob_response_data_id'] = $ppob_response_data->id;
                PpobCustomerData::updateOrCreate(
                  [
                    'key' => $data_product['ppob_key'],
                    'product_id' => $data_product['product_id'],
                    'customer_id' => $order->customer_id,
                    'ppob_product_code' => $data_product['ppobkey_inquiry_data']['code'],
                  ],
                  [
                    'inquiry_data' => $product['ppobkey_inquiry_data'],
                  ]
                );
              } else {
                $data_product['ppob_status'] = 'FAILED';
                $ppob_response_data = PpobResponseData::updateOrCreate(
                  [
                    'ppob_ref_id' => $data_product['ppob_ref_id'],
                    // 'order_product_id' => $order_product->id,
                    'product_id' => $data_product['product_id'],
                    'customer_id' => $order->customer_id,
                  ],
                  [
                    'response_data' => "{}",
                    'error_message' => "Failed to fetch API",
                  ]
                );
                $data_product['ppob_response_data_id'] = $ppob_response_data->id;
              }
            }
            //? PLN Token & eMoney
            if (in_array($gasplus_product->code, ['PLN_TKN', 'EMONEY'])) {
              $data_product['price'] = (int) $special_price + (int) $data_product['ppob_price'];
              $data_product['ppob_ref_id'] = $order->code . '-' . uniqid();
              $helper_iak = new HelperIak;
              $res = $helper_iak->fetchApiIakPrepaid('/api/top-up', 'POST', [], [
                'customer_id' => $data_product['ppob_key'],
                'product_code' => $data_product['ppob_product_code'],
                'ref_id' => $data_product['ppob_ref_id'],
              ], $data_product['ppob_ref_id']);
              if ($res && isset($res->status)) {
                switch ($res->status) {
                  case 0:
                    $data_product['ppob_status'] = 'PROCESS';
                    break;
                  case 1:
                    $data_product['ppob_status'] = 'SUCCESS';
                    break;
                  default:
                    $data_product['ppob_status'] = 'FAILED';
                    break;
                }
                $ppob_response_data = PpobResponseData::updateOrCreate(
                  [
                    'ppob_ref_id' => $data_product['ppob_ref_id'],
                    // 'order_product_id' => $order_product->id,
                    'product_id' => $data_product['product_id'],
                    'customer_id' => $order->customer_id,
                  ],
                  [
                    'response_data' => $res,
                    'error_message' => $res->status >= 2 ? $res->message : null,
                  ]
                );
                $data_product['ppob_response_data_id'] = $ppob_response_data->id;
              } else {
                $data_product['ppob_status'] = 'FAILED';
                $ppob_response_data = PpobResponseData::updateOrCreate(
                  [
                    'ppob_ref_id' => $data_product['ppob_ref_id'],
                    // 'order_product_id' => $order_product->id,
                    'product_id' => $data_product['product_id'],
                    'customer_id' => $order->customer_id,
                  ],
                  [
                    'response_data' => "{}",
                    'error_message' => "Failed to fetch API",
                  ]
                );
                $data_product['ppob_response_data_id'] = $ppob_response_data->id;
              }
              if ($product['ppobkey_inquiry_data']) {
                PpobCustomerData::updateOrCreate(
                  [
                    'key' => $data_product['ppob_key'],
                    'product_id' => $data_product['product_id'],
                    'customer_id' => $order->customer_id,
                    'ppob_product_code' => $gasplus_product->code === 'EMONEY' ? $data_product['ppob_product_code'] : null,
                  ],
                  [
                    'inquiry_data' => $product['ppobkey_inquiry_data'],
                  ]
                );
                $data_product['ppob_label'] = $product['ppobkey_inquiry_data']['name'];
              }
            }
          }

          OrderProduct::create($data_product);
        }
      }
      if (count($data["products"]) > 0) {
        $order->setAdditionalCost();
        $order->countTotal();
        Deposit::create([
          'order_id' => $order->id,
          'customer_id' => $order->customer_id,
          'amount' => ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) ? ($order->amount_deposit_used * -1) : 0,
          'balance' => $order->deposit_balance_after,
          'note' => 'ADD JOB',
        ]);
        if ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) {
          $order->customer->setDeposit($order->deposit_balance_after);
        }
      }

      // Job Deposit
      if (isset($data['deposit']) && !empty($data['deposit'])) {
        $order->update([
          'deposit_job' => str_replace(".", "", $data['deposit']),
          'status_id' => $is_with_product ? 1 : 4,
        ]);
      }
    }

    // ? Accurate create Invoice
    if (!$is_marketing) {
      $helper->accurateUpsertInvoice($order, true);
    }

    $query_url = [
      'store_slug' => $store->slug,
      // 'status' => 'total',
    ];

    if ($is_konsumsitoko) {
      $query_url['totalkonsumsitoko'] = $order->total;
    } else if ($is_marketing) {
      // $query_url['status'] = 'diambil';
      $query_url['type'] = 'marketing';
    } else {
      $query_url['status'] = 'total';
    }

    $is_send_notif = true;
    if ($is_konsumsitoko || $is_marketing) {
      $is_send_notif = false;
    }
    if ($is_asdt && !$order->receiver_phone) {
      $is_send_notif = false;
    }
    // if (!$data["is_send_notif"]) {
    //   $is_send_notif = false;
    // }
    if ($is_send_notif) {
      $close_hour_array = explode(":", $order->store->close_hour);
      $close_hour = new DateTime();
      $close_hour->setTime((int) $close_hour_array[0], (int) $close_hour_array[1]);
      $is_deliver_tomorrow = $created_at > $close_hour;
      $msg_order_new = $order->store->msg_order_new ? $order->store->msg_order_new : 'Mohon menunggu. Pesanan sedang kami proses.';
      $msg_to_customer = $helper->createOrderMessage($order, true, $is_asdt);
      if (!$is_asdt) {
        $msg_to_customer = $msg_order_new . '<br><br>' . $msg_to_customer;
      }
      if ($is_deliver_tomorrow) {
        $msg_to_customer = '*Maaf, saat ini toko sudah tutup 🙏. Pesanan akan dikirim BESOK*.<br><br>' . $msg_to_customer;
      }


      // ? Get conversation chat
      $helperSocialchat = new HelperSocialchat();
      $conversationId = null;
      $phone = $order->receiver_phone ?? $order->customer->phone;
      $socialchat = Socialchat::where("store_id", $order->store_id)
        ->where('customer_id', $order->customer_id)
        ->where('phone', $phone)
        ->orderBy('id', 'desc')
        ->first();
      if ($socialchat) {
        $conversationId = $socialchat->conversation_id;
      } else {
        $conversationId = $helperSocialchat->getConversationId($order->store, $phone);
        if ($conversationId) {
          Socialchat::create([
            'store_id' => $order->store_id,
            'customer_id' => $order->customer_id,
            'phone' => $phone,
            'conversation_id' => $conversationId,
          ]);
        }
      }

      // ? Try send notif via socialchat
      $isSuccesSendNotifViaSocialchat = false;
      if ($conversationId) {
        $order->customer->socialchat_conversation_id = $conversationId;
        $order->customer->save();
        $msg_to_customer = str_replace(PHP_EOL, '', $msg_to_customer);
        $msg_to_customer = str_replace('<br>', PHP_EOL, $msg_to_customer);
        $isSuccesSendNotifViaSocialchat = $helperSocialchat->sendMessage($conversationId, $msg_to_customer);
        $query_url['alert'] = 'notif-sent';
        $query_url['alertmsg'] = 'Job ' . $order->code . ' berhasil terkirim.';
      }

      // ? Check Product has PPOB and sent notif
      foreach ($order->products as $product) {
        if ($product->is_ppob && $product->pivot->ppob_status === 'SUCCESS' && $product->pivot->notif_sent_at === null) {
          $order_product = OrderProduct::find($product->pivot->id);
          $msg_ppob = $helper->createPpobMessage($order_product);
          if ($msg_ppob) {
            // // ? Get PDF Receipt
            // $helper_iak = new HelperIak;
            // $receipt = $helper_iak->getReceiptPostpaid($order, $order_product->ppob_tr_id);
            // $media = [];
            // if ($receipt) {
            //   $media = [
            //     'type' => 'document',
            //     // 'name' => $receipt['file_name'] . '.jpeg',
            //     'name' => $receipt['file_name'] . '.pdf',
            //     // 'mimetype' => 'image/jpeg',
            //     'url' => $receipt['path'],
            //   ];
            //   $helperSocialchat->sendMessage($conversationId, '', $media);
            // }
            $msg_ppob = str_replace(PHP_EOL, '', $msg_ppob);
            $msg_ppob = str_replace('<br>', PHP_EOL, $msg_ppob);
            $isSuccesSendNotif = $helperSocialchat->sendMessage($conversationId, $msg_ppob);
            if ($isSuccesSendNotif) {
              $order_product->notif_sent_at = now();
              $order_product->updated_at = now();
              $order_product->save();
            }
          }
        }
      }

      // ? Send notif manual via wa.me
      if (!$conversationId || !$isSuccesSendNotifViaSocialchat) {
        $query_url['alert'] = 'notif-confirm';
        $query_url['msg'] = urlencode($msg_to_customer);
        $query_url['to'] = $helper->convertPhone($order->receiver_phone ? $order->receiver_phone : $order->customer->phone);
      }
    }

    if (!$is_asdt_konsumsitoko && !$is_marketing) {
      $order->customer->options = array_merge((array) $order->customer->options, [
        'last_order_at' => Carbon::now()->toDateTimeString(),
        'last_order_or_notified_at' => Carbon::now()->toDateTimeString(),
      ]);
      $order->customer->save();
    }

    Cache::forget('calendar_orders_' . $store->slug . '_' . $created_at->format('Y-m-d') . '_' . auth()->user()->id);
    Cache::forget('jobs_order_bebas_' . $store->slug . '_' . $created_at->format('Y-m-d'));
    Cache::forget('jobs_order_' . $store->slug . '_' . $created_at->format('Y-m-d') . '_' . auth()->user()->id);
    Cache::forget('jobs_drivers_' . $store->slug . '_' . $created_at->format('Y-m-d'));
    Cache::forget('jobs_drivers_plus_' . $store->slug . '_' . $created_at->format('Y-m-d'));
    Cache::forget('jobs_ttl_month_' . $store->slug . '_' . $created_at->format('Y-m-d'));
    Cache::forget('jobs_ttl_today_' . $store->slug . '_' . $created_at->format('Y-m-d'));

    if ($is_konsumsitoko) {
      // return redirect()->route('operatingcost-add', $query_url)->with('success', 'TAMBAH JOB BERHASIL');
      return response()->json([
        'url_redirect' => route('operatingcost-add', $query_url),
        'status' => 'success',
        'message' => 'TAMBAH JOB BERHASIL',
      ]);
    } else {
      // return redirect()->route('job-list', $query_url)->with('success', 'TAMBAH JOB BERHASIL');
      return response()->json([
        'url_redirect' => route('job-list', $query_url),
        'status' => 'success',
        'message' => 'TAMBAH JOB BERHASIL',
      ]);
    }
  }

  public function markNotifAsSent(Request $request, $order_product_id)
  {
    // $data = $request->all();
    $order_product = OrderProduct::find($order_product_id);
    $order_product->notif_sent_at = now();
    $order_product->updated_at = now();
    $order_product->save();
    return response()->json([
      'status' => 'success',
      'message' => 'Notifikasi berhasil dikirim',
    ]);
  }

  public function reSyncAcc(Request $request)
  {
    $data = $request->all();

    if ($data['date'] <= '2025-05-05') {
      return response()->json([
        'status' => 'error',
        'message' => 'Date cannot be less than 2025-05-06',
      ], 400);
    }

    // return null;

    $orders_unsync = Order::distinct()
      ->whereDate('created_at', $data['date'])
      ->whereNull('deleted_at')
      ->where('store_id', $data['store_id'])
      ->where('status_id', '!=', 5)
      ->where(function ($q) {
        $q
          ->whereHas('accurateInvoice', function ($sq) {
            $sq->whereNull('accurate_id');
          })->OrWhereHas('accurateReceipt', function ($sq) {
            $sq->whereNull('accurate_id');
          })
          ->orWhereDoesntHave('accurateInvoice')
          // ->orWhereDoesntHave('accurateReceipt')
        ;
      })
      ->with([
        'accurateInvoice',
        'accurateReceipt',
      ])
      ->get();

    if ($orders_unsync) {
      $helper = new Helper;
      $success_sync = 0;
      $failed_sync = 0;
      foreach ($orders_unsync as $order) {
        $result_sync = null;
        if (!$order->accurate_invoice || ($order->accurate_invoice && $order->accurate_invoice->accurate_id === null)) {
          $result_sync = $helper->accurateUpsertInvoice($order);
        }
        if ($order->status_id === 6 && (!$order->accurate_receipt || ($order->accurate_receipt && $order->accurate_receipt->accurate_id === null))) {
          $result_sync = $helper->accurateUpsertReceipt($order);
        }
        if ($result_sync) {
          $success_sync++;
        } else {
          $failed_sync++;
        }
      }
      return response()->json([
        'status' => 'success',
        'message' => 'All orders have been successfully re-synced with Accurate.',
        'success_sync_count' => $success_sync,
        'failed_sync_count' => $failed_sync,
      ]);
    }
    return response()->json([
      'status' => 'success',
      'message' => 'No un-sync orders found.',
    ]);
  }
}
