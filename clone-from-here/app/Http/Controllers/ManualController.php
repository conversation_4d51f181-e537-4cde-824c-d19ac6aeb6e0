<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Helper\Helper;
use Illuminate\Http\Request;
// use App\Models\Product;
// use App\Models\Store;

class ManualController extends Controller
{
    public function generatedeposit($customer_id, Request $request)
    {
        $helper = new Helper;
        $data = $request->all();
        return $helper->generateDepositHistory($customer_id);
    }
}
