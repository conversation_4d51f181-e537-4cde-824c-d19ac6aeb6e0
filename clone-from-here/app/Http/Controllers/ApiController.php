<?php

namespace App\Http\Controllers;

use DateTime;
use Carbon\Carbon;
use App\Models\City;
use App\Models\User;
use App\Models\Order;
use App\Models\Store;
use App\Helper\Helper;
use App\Models\Address;
use App\Models\Deposit;
// use Illuminate\Support\Facades\File;
// use Illuminate\Filesystem\Filesystem;
// use App\Models\User;
// use App\Models\Store;
use App\Models\Product;
use App\Models\Category;
use App\Models\Customer;
use App\Jobs\ProcessNotif;
use App\Models\Socialchat;
use App\Models\OrderProduct;
use App\Models\ProductStore;
use Illuminate\Http\Request;
// use App\Models\Bank;
use App\Models\CustomerProduct;
use App\Helper\HelperSocialchat;
use App\Models\PpobResponseData;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\NotificationSchedule;
use Illuminate\Support\Facades\Mail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;

class ApiController extends Controller
{
    public function getCitiesStores()
    {
        $cities = City::with(['stores', 'stores.featurephoto'])->orderBy('sort_order')->get();
        return $cities->map(function ($city) {
            $city->stores = $city->stores->map(function ($store) {
                $store->photo_url = $store->featurephoto ? $store->featurephoto->thumbnail($width = 390, $height = 390, $filters = []) : null;
            });
            return $city;
        });
    }

    public function getCityStore($slugstore)
    {
        $slug_store = $slugstore;
        $city = City::whereHas('stores', function (Builder $query) use ($slug_store) {
            $query->where('slug', $slug_store);
        })->first();
        return $city;
    }

    public function getStores()
    {
        return Store::orderBy('sort_order')->get();
    }

    public function getStoresProducts()
    {
        return Store::orderBy('sort_order')->with(['products'])->get();
    }

    public function getStore($slugstore)
    {
        $slug_store = $slugstore;
        $store = Store::where('slug', $slug_store)->with(['featurephoto', 'city', 'banks'])->orderBy('sort_order')->first();
        $store->featurephoto_url = $store->featurephoto ? $store->featurephoto->thumbnail($width = 390, $height = 390, $filters = []) : null;
        return $store;
    }

    public function getCategories()
    {
        return Category::all();
    }

    public function getStoreCategories($slugstore)
    {
        $slug_store = $slugstore;
        $categories = DB::table('categories')
            ->leftJoin('category_product', 'categories.id', '=', 'category_product.category_id')
            ->leftJoin('products', 'category_product.product_id', '=', 'products.id')
            ->leftJoin('product_store', 'products.id', '=', 'product_store.product_id')
            ->leftJoin('stores', 'product_store.store_id', '=', 'stores.id')
            ->where('stores.slug', $slug_store)
            ->where('product_store.is_available', 1)
            ->select('categories.*')
            ->orderBy('categories.sort_order')
            ->distinct()
            ->get();
        return $categories;
    }

    public function getStoreProducts($slugstore)
    {
        $slug_store = $slugstore;
        $store = Store::where('slug', $slug_store)->first();
        if ($store) {
            $products = $store->products()->wherePivot('is_available', 1)->with(['featurephoto', 'categories'])->orderBy('sort_order')->get();
            foreach ($products as &$product) {
                $product->featurephoto_url = $product->featurephoto ? $product->featurephoto->thumbnail($width = 354, $height = 354, $filters = []) : null;
            }
            return $products;
        } else {
            return response()->json(false);
        }
    }

    public function getProducts()
    {
        return Product::all();
    }

    public function getProduct($slugstore, $slugproduct)
    {
        $slug_store = $slugstore;
        $slug_product = $slugproduct;
        $store = Store::where('slug', $slug_store)->with(['products' => function ($query) use ($slug_product) {
            $query->where('slug', $slug_product)->wherePivot('is_available', 1)->with(['featurephoto', 'photos', 'categories']);
        }])->first();

        $product = response()->json(false);
        if ($store->products->count()) {
            $product = $store['products'][0];

            $product->featurephoto_url = $product->featurephoto ? $product->featurephoto->thumbnail($width = 500, $height = 500, $filters = []) : null;

            foreach ($product->photos as &$photo) {
                if ($photo) {
                    $photo->photo_url = $photo->thumbnail($width = 500, $height = 500, $filters = []);
                }
            }
        }

        return $product;
    }

    public function getCustomerById(Request $request, $id)
    {
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(['error' => 'Key invalid!']);
        }
        $customer = Customer::where('id', $id)->with(['addresses', 'addresses.store'])->first();
        if ($customer) {
            return $customer;
        } else {
            return response()->json(false);
        }
    }

    public function getLastOrder(Request $request, $customer_id)
    {
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(['error' => 'Key invalid!']);
        }
        $order = Order::where('customer_id', $customer_id)->with(['products', 'status', 'customer', 'address', 'store', 'store.banks'])->orderBy('created_at', 'desc')->first();
        if ($order) {
            // ? Get PPOB Response Data
            $order->products = $order->products->map(function ($product) {
                $product->ppobresponsedata = PpobResponseData::find($product->pivot->ppob_response_data_id);
                return $product;
            });
            return $order;
        } else {
            return response()->json(false);
        }
    }

    public function getCustomerByPhone(Request $request, $phone)
    {
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(['error' => 'Key invalid!']);
        }
        $phone_number = $phone;
        $phone_number = str_replace(' ', '', $phone);
        $phone_number = str_replace('-', '', $phone_number);
        $phone_number = str_replace('+', '', $phone_number);
        if (substr($phone_number, 0, 1) == '0') {
            $phone_number = '62' . substr($phone_number, 1);
        }
        $customer = Customer::where('phone', 'like', $phone_number)
            ->with(['addresses', 'addresses.store', 'lastorder'])
            ->get()
            ->sortByDesc(function ($customer) {
                return $customer->lastorder->created_at;
            })
            ->values()
            ->first();
        if ($customer) {
            // if ($customer->phone != $phone_number) {
            //     $customer->phone = $phone_number;
            //     $customer->save();
            // }
            return $customer;
        } else {
            return response()->json('notfound');
        }
    }

    public function updateCustomerNotif(Request $request)
    {
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(['error' => 'Key invalid!']);
        }
        $data = $request->all();

        // Check Data
        // ====================================================
        if (empty($data["id"]) || empty($data["notif_status"])) {
            return response()->json(
                [
                    'status' => "error",
                    'error' => "Customer not found!",
                ],
                500,
            );
        }

        $notif_status = filter_var($data['notif_status'], FILTER_VALIDATE_BOOLEAN);
        if ($notif_status) {
            $notif_status = 'on';
        } else {
            $notif_status = 'off';
        }

        Customer::where('id', $data['id'])
            ->update([
                'notif_status' => $notif_status
            ]);

        return response()->json(
            [
                'status' => "success",
                'message' => "Update customer notif success!",
            ]
        );
    }

    public function upsertCustomer(Request $request)
    {
        $helper = new Helper;
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(['error' => 'Key invalid!']);
        }
        $data = $request->all();

        // Normalize Data
        $data['phone'] = str_replace(' ', '', $data['phone']);
        $data['phone'] = str_replace('-', '', $data['phone']);
        $data['phone'] = str_replace('+', '', $data['phone']);
        if (substr($data['phone'], 0, 1) == '0') {
            $data['phone'] = '62' . substr($data['phone'], 1);
        }

        // Get Store
        if (isset($data['store_id']) && !empty($data['store_id'])) {
            $store = Store::find($data['store_id']);
        } elseif (isset($data['slug_store']) && !empty($data['slug_store'])) {
            $store = Store::where('slug', $data['slug_store'])->first();
        }

        // Get Customer
        $customer = false;
        if (isset($data['id']) && !empty($data['id'])) {
            $customer = Customer::find($data['id']);
        }

        // Get Address
        $address = false;
        if (isset($data['address_id']) && !empty($data['address_id'])) {
            $address = Address::find($data['address_id']);
        } elseif ($customer && $customer->addresses) {
            $address = $customer->addresses->first();
        }

        // Data Customer
        $data_customer = [
            'phone' => $data['phone'],
            'name' => $data['name'],
            'address' => $data['address'],
        ];
        if (isset($data['email']) && !empty($data['email'])) {
            $data_customer['email'] = $data['email'];
        }
        if (isset($data['amount']) && !empty($data['amount'])) {
            $data_customer['amount'] = $data['amount'];
        }
        if (isset($data['payment']) && !empty($data['payment'])) {
            $data_customer['payment'] = $data['payment'];
        }
        if (isset($data['note']) && !empty($data['note'])) {
            $data_customer['note'] = $data['note'];
        }

        // Data Address
        $data_address = [
            'store_id' => $store->id,
            'address' => $data['address'],
        ];
        $address_id = isset($data['address_id']) && !empty($data['address_id']) ? $data['address_id'] : 0;
        if (isset($data['latlng']) && !empty($data['latlng'])) {
            $data_address['latlng'] = $data['latlng'];
        }


        // Rules Customer
        $model_customer = new Customer;
        $rules_customer = $model_customer->rules;
        $rules_customer['address'] = 'required';

        // Filter Rules Customer
        if ($customer && $data_customer['phone'] == $customer->phone) {
            unset($rules_customer['phone']);
        }

        // Validate Customer
        $validation = Validator::make($data_customer, $rules_customer);
        if ($validation->fails()) {
            return response()->json(['errors' => $validation->errors()->toArray()]);
        }
        unset($data_customer['address']);

        // Update or Insert Customer
        if ($customer) {
            $affected = $customer->update($data_customer);
            $customer_id = $customer->id;
        } else {
            $customer_id = Customer::create($data_customer)->id;
        }

        // Update or Insert Address
        if ($address) {
            $address->update($data_address);
            $helper->accurateUpsertCustomer($address);
        } else {
            $data_address['customer_id'] = $customer_id;
            $address_new = Address::create($data_address);
            $helper->accurateUpsertCustomer($address_new, true);
        }

        // Response
        return response()->json(Customer::where('id', $customer_id)->with(['addresses', 'addresses.store'])->first());
    }

    public function createOrder(Request $request)
    {
        $helper = new Helper;
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(['error' => 'Key invalid!']);
        }
        $data = $request->all();
        unset($data['token']);
        $data['order'] = json_decode($data['order']);

        // Get Store ID
        if (isset($data['store_id']) && !empty($data['store_id'])) {
            $store = Store::find($data['store_id']);
        } else {
            $store = Store::where('slug', $data['slug_store'])->first();
        }

        $customer = Customer::find($data['id']);
        if (isset($data['address_id']) && !empty($data['address_id'])) {
            $address = Address::find($data['address_id']);
        } else {
            $address = Address::where('address', $data['address'])->first();
        }

        $order = Order::create([
            'status_id' => 1,
            'store_id' => $store->id,
            'customer_id' => $customer->id,
            'address_id' => $address->id,
            'payment' => $data['order']['payment'] ? $data['order']['payment'] : null,
            'amount_will_pay' => $data['order']['amount'] ? $data['order']['amount'] : null,
            'note' => $data['order']['note'] ? $data['order']['note'] : null,
        ]);

        $data_products = [];
        foreach ($data['cart'] as $cart) {
            $product = json_decode($cart);
            $productstore = ProductStore::where('store_id', $store->id)
                ->where('product_id', $product->id)
                ->first();
            $customerproduct = CustomerProduct::where('customer_id', $customer->id)
                ->where('product_id', $product->id)
                ->first();
            $local_price = $productstore ? ($productstore->local_price ? $productstore->local_price : $productstore->product->price) : 0;
            $special_price = $customerproduct && $customerproduct->local_price ? $customerproduct->local_price : $local_price;
            $data_products[$product->id] = [
                'qty' => $product->qty,
                'price' => $special_price,
            ];
        }
        // return response()->json(['test' => $request->all()]);
        $order->products()->sync($data_products);
        $order->setAdditionalCost();
        $order->countTotal();
        Deposit::create([
            'order_id' => $order->id,
            'customer_id' => $order->customer_id,
            'amount' => ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) ? ($order->amount_deposit_used * -1) : 0,
            'balance' => $order->deposit_balance_after,
            'note' => 'ADD JOB',
        ]);
        if ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) {
            $order->customer->setDeposit($order->deposit_balance_after);
        }
        // $helper->generateDepositHistory($order->customer_id, $order->created_at);

        // Send Notification by Email
        $details = Order::where('id', $order->id)->with(['products', 'customer', 'address', 'store', 'store.banks'])->orderBy('sort_order')->first();
        $admin = User::where('role_id', 4)->where('store_id', $store->id)->first();
        Mail::to($admin->email)->send(new \App\Mail\MailNewOrder($details));
        // return response()->json(
        //     [
        //         'test'=>$details,
        //         // 'notification'=>json_decode($response_notification_admin)
        //     ]
        // );

        // // Send Webpushr
        // $end_point = 'https://api.webpushr.com/v1/notification/send/attribute';
        // $http_header = array(
        //     "Content-Type: Application/Json",
        //     "webpushrKey: " . env('WEBPUSHR_KEY', 'd52766018d2e49e724b9b7da26dab392'),
        //     "webpushrAuthToken: " . env('WEBPUSHR_AUTH_TOKEN', '20775')
        // );
        // $curent_year = date('Y');
        // $curent_month = date('m');
        // $curent_date = date('d');
        // $current_date = $curent_year.$curent_month.$curent_date;
        // $ch = curl_init();
        // curl_setopt($ch, CURLOPT_HTTPHEADER, $http_header);
        // curl_setopt($ch, CURLOPT_URL, $end_point);
        // curl_setopt($ch, CURLOPT_POST, 1);
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // // Send Notification to Admin Toko
        // $admin = User::where('role_id', 4)->where('store_id', $store->id)->first();
        // if ($admin) {
        //     $req_data = array(
        //         'title' 		=> "Order Baru 👤 ".$customer->name, //required
        //         'message' 		=> "Pilih driver sekarang!", //required
        //         'target_url'	=> env('APP_URL', 'http://cs2.gasplus.online').'/cs/list/order?filter_status%5B0%5D=1&filter_date='.$current_date.'..'.$current_date.'&page=1', //required
        //         'attribute'		=> [
        //             'user_id' => $admin->id, // Admin Toko
        //             // 'role_id' => 4, // Admin Toko
        //             // 'store_id' => $store->id
        //         ],
        //         //following parameters are optional
        //         //'name'		=> 'Test campaign',
        //         //'icon'		=> 'https://cdn.webpushr.com/siteassets/wSxoND3TTb.png',
        //         //'image'		=> 'https://cdn.webpushr.com/siteassets/aRB18p3VAZ.jpeg',
        //         //'auto_hide'	=> 1,
        //         //'expire_push'	=> '5m',
        //         //'send_at'		=> '2020-12-28 06:17 +5:30',
        //         //'action_buttons'=> array(
        //             //array('title'=> 'Demo', 'url' => 'https://www.webpushr.com/demo'),
        //             //array('title'=> 'Rates', 'url' => 'https://www.webpushr.com/pricing')
        //         //)
        //     );
        //     curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($req_data));
        //     $response_notification_admin = curl_exec($ch);
        // }

        // // Send Notification to Other Users
        // $users = User::whereNotIn('role_id', [4, 5])->where('notification_is_active', 1)->get(); // except Admin Toko or Driver
        // foreach ($users as $user) {
        //     // sleep(1);
        //     $req_data = array(
        //         'title' 		=> "Order Baru 🛍 ".$store->name, //required
        //         'message' 		=> "Dari 👤 ".$customer->name.". Klik untuk melihat detail.", //required
        //         'target_url'	=> env('APP_URL', 'http://cs2.gasplus.online').'/cs/form/order/'.$order->id, //required
        //         'attribute'		=> [
        //             'user_id' => $user->id,
        //         ],
        //         //following parameters are optional
        //         //'name'		=> 'Test campaign',
        //         //'icon'		=> 'https://cdn.webpushr.com/siteassets/wSxoND3TTb.png',
        //         //'image'		=> 'https://cdn.webpushr.com/siteassets/aRB18p3VAZ.jpeg',
        //         //'auto_hide'	=> 1,
        //         //'expire_push'	=> '5m',
        //         //'send_at'		=> '2020-12-28 06:17 +5:30',
        //         //'action_buttons'=> array(
        //             //array('title'=> 'Demo', 'url' => 'https://www.webpushr.com/demo'),
        //             //array('title'=> 'Rates', 'url' => 'https://www.webpushr.com/pricing')
        //         //)
        //     );
        //     curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($req_data));
        //     $response = curl_exec($ch);
        // }

        return response()->json(
            [
                'order' => $order,
                'notification_is_sent' => true
                // 'notification'=>json_decode($response_notification_admin)
            ]
        );
    }

    public function upsertCustomerAddressOrder(Request $request)
    {
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(['error' => 'Key invalid!']);
        }
        $data = $request->all();
        unset($data['token']);

        // Upsert Customer
        // ====================================================

        // Normalize Data
        $helper = new Helper();
        $data['phone'] = $helper->convertPhone($data['phone']);

        // Get Store
        // if (isset($data['store_id']) && !empty($data['store_id'])) {
        //     $store = Store::find($data['store_id']);
        // } elseif (isset($data['slug_store']) && !empty($data['slug_store'])) {
        $store = Store::where('slug', $data['slug_store'])->first();

        if (!$store) {
            return response()->json(
                [
                    'status' => "error",
                    'error' => 'Toko tidak ditemukan!',
                ],
                500
            );
        } else if ($store && $store->is_comingsoon) {
            return response()->json(
                [
                    'status' => "error",
                    'error' => 'Toko belum beroperasi!',
                ],
                500
            );
        } else if ($store && $store->holiday_start && $store->holiday_end) {
            // Convert to timestamp
            $start_ts = strtotime($store->holiday_start);
            $end_ts = strtotime($store->holiday_end);
            $current_ts = strtotime("now");

            // Check that user date is between start & end
            if (($current_ts >= $start_ts) && ($current_ts <= $end_ts)) {
                return response()->json(
                    [
                        'status' => "error",
                        'error' => 'Toko tutup!',
                    ],
                    500
                );
            }
        }
        // }

        $is_merged = false;

        // Get Customer
        $customer = false;
        if (isset($data['id']) && !empty($data['id'])) {
            $customer = Customer::find($data['id']);
            if ($customer->merger) {
                $is_merged = true;
                $customer = $customer->merger->maincustomer;
            }
        }

        // Get Address
        $address = false;
        if ($is_merged) {
            $address = $customer->addresses->first();
        } elseif (isset($data['address_id']) && !empty($data['address_id'])) {
            $address = Address::find($data['address_id']);
        } elseif ($customer && $customer->addresses) {
            $address = $customer->addresses->first();
        }

        // Data Customer
        $data_customer = [
            'phone' => $data['phone'],
            'name' => $data['name'],
            'address' => $data['address'],
        ];
        if (isset($data['email']) && !empty($data['email'])) {
            $data_customer['email'] = $data['email'];
        }
        if (isset($data['amount']) && !empty($data['amount'])) {
            $data_customer['amount'] = $data['amount'];
        }
        if (isset($data['payment']) && !empty($data['payment'])) {
            $data_customer['payment'] = $data['payment'];
        }
        if (isset($data['note']) && !empty($data['note'])) {
            $data_customer['note'] = $data['note'];
        }

        // Data Address
        $data_address = [
            'store_id' => $store->id,
            'address' => $data['address'],
        ];

        $address_id = isset($data['address_id']) && !empty($data['address_id']) ? $data['address_id'] : 0;
        if (isset($data['latlng']) && !empty($data['latlng'])) {
            $data_address['latlng'] = $data['latlng'];
        }


        // Rules Customer
        $model_customer = new Customer;
        $rules_customer = $model_customer->rules;
        $rules_customer['address'] = 'required';

        // Filter Rules Customer
        if ($customer && $data_customer['phone'] == $customer->phone) {
            unset($rules_customer['phone']);
        }

        // Validate Customer
        if (!$is_merged) {
            $validation = Validator::make($data_customer, $rules_customer);
            if ($validation->fails()) {
                return response()->json(['errors' => $validation->errors()->toArray()]);
            }
        }
        unset($data_customer['address']);

        // Update or Insert Customer
        if ($customer) {
            if (!$is_merged) {
                $affected = $customer->update($data_customer);
            }
            $customer_id = $customer->id;
        } else {
            $customer_id = Customer::create($data_customer)->id;
            $customer = Customer::find($customer_id);
        }

        // Update or Insert Address
        if ($address) {
            if (!$is_merged) {
                $address->update($data_address);
                $helper->accurateUpsertCustomer($address);
            }
        } else {
            $data_address['customer_id'] = $customer_id;
            $address = Address::create($data_address);
            $helper->accurateUpsertCustomer($address, true);
        }

        $customer_updated = Customer::where('id', $customer_id)->with(['addresses', 'addresses.store'])->first();



        // New Order
        // ====================================================

        // var_dump($data['order']);
        $data['order'] = is_array($data['order']) ? $data['order'] : json_decode($data['order']);

        $current_time = new DateTime();
        $close_hour_array = explode(":", $store->close_hour);
        $close_hour = new DateTime();
        $close_hour->setTime((int) $close_hour_array[0], (int) $close_hour_array[1]);
        $is_deliver_tomorrow = $current_time > $close_hour;
        $created_at = $is_deliver_tomorrow ? $current_time->modify('+1 day') : new DateTime();

        $order = Order::create([
            'status_id' => 1,
            'store_id' => $store->id,
            'customer_id' => $customer->id,
            'address_id' => $address->id,
            'payment' => $data['order']['payment'] ? $data['order']['payment'] : null,
            'amount_will_pay' => $data['order']['amount'] ? $data['order']['amount'] : null,
            'note' => $data['order']['note'] ? $data['order']['note'] : null,
            'created_at' => $created_at,
        ]);

        // $data_products = [];
        foreach ($data['cart'] as $cart) {
            $product = is_array($cart) ? $cart : json_decode($cart);
            $productstore = ProductStore::where('store_id', $store->id)
                ->where('product_id', $product['id'])
                ->first();
            $customerproduct = CustomerProduct::where('customer_id', $customer->id)
                ->where('product_id', $product['id'])
                ->first();
            $local_price = $productstore ? ($productstore->local_price ? $productstore->local_price : $productstore->product->price) : 0;
            $special_price = $customerproduct && $customerproduct->local_price ? $customerproduct->local_price : $local_price;
            // $data_products[$product['id']] = [
            //     'qty' => $product['qty'],
            //     'price' => $special_price,
            // ];
            OrderProduct::create([
                'order_id' => $order->id,
                'product_id' => $product['id'],
                'qty' => $product['qty'],
                'price' => $special_price,
            ]);
        }
        // return response()->json(['test' => $request->all()]);
        // $order->products()->sync($data_products);
        $order->setAdditionalCost();
        $order->countTotal();
        Deposit::create([
            'order_id' => $order->id,
            'customer_id' => $order->customer_id,
            'amount' => ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) ? ($order->amount_deposit_used * -1) : 0,
            'balance' => $order->deposit_balance_after,
            'note' => 'ADD JOB',
        ]);
        if ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) {
            $order->customer->setDeposit($order->deposit_balance_after);
        }
        // $helper->generateDepositHistory($order->customer_id, $order->created_at);

        // ? Accurate create Invoice
        $helper->accurateUpsertInvoice($order, true);

        // ? Send Notif via Socialchat
        $isSuccesSendNotifViaSocialchat = false;
        // if ($order->store->is_notif_wa) {
        $close_hour_array = explode(":", $order->store->close_hour);
        $close_hour = new DateTime();
        $close_hour->setTime((int) $close_hour_array[0], (int) $close_hour_array[1]);
        $is_deliver_tomorrow = $created_at > $close_hour;
        $msg_order_new = $order->store->msg_order_new ? $order->store->msg_order_new : 'Mohon menunggu. Pesanan sedang kami proses.';
        $msg_order_info = $helper->createOrderMessage($order);
        $msg_to_customer = $msg_order_info ? $msg_order_new . '<br><br>' . $msg_order_info : $msg_order_new;
        if ($is_deliver_tomorrow) {
            $msg_to_customer = '*Maaf, saat ini toko sudah tutup 🙏. Pesanan akan dikirim BESOK*.<br><br>' . $msg_to_customer;
        }

        // ? Get conversation chat
        $helperSocialchat = new HelperSocialchat();
        $conversationId = null;
        $phone = $order->receiver_phone ?? $order->customer->phone;
        $socialchat = Socialchat::where("store_id", $order->store_id)
            ->where('customer_id', $order->customer_id)
            ->where('phone', $phone)
            ->orderBy('id', 'desc')
            ->first();
        if ($socialchat) {
            $conversationId = $socialchat->conversation_id;
        } else {
            $conversationId = $helperSocialchat->getConversationId($order->store, $phone);
            if ($conversationId) {
                Socialchat::create([
                    'store_id' => $order->store_id,
                    'customer_id' => $order->customer_id,
                    'phone' => $phone,
                    'conversation_id' => $conversationId,
                ]);
            }
        }

        // ? Try send notif via socialchat
        if ($conversationId) {
            $order->customer->socialchat_conversation_id = $conversationId;
            $order->customer->save();
            $msg_to_customer = str_replace(PHP_EOL, '', $msg_to_customer);
            $msg_to_customer = str_replace('<br>', PHP_EOL, $msg_to_customer);
            $isSuccesSendNotifViaSocialchat = $helperSocialchat->sendMessage($conversationId, $msg_to_customer);
            $query_url['alert'] = 'notif-sent';
            $query_url['alertmsg'] = 'Job ' . $order->code . ' berhasil terkirim.';
        }
        // }

        // Send Notification by WhatsApp
        // -------------------------------------
        // if ($order->store->is_notif_wa) {
        //     $messages = [];

        //     // Check Notif Customer
        //     if ($order->customer->notif_status === 'unset') {
        //         $customer->update([
        //             'notif_status' => 'on',
        //         ]);
        //     }

        //     // Customer
        //     if ($order->customer->notif_status === 'on' || $order->customer->notif_status === 'unset') {
        //         $msg_order_new = $order->store->msg_order_new ? $order->store->msg_order_new : 'Mohon menunggu. Pesanan sedang kami proses.';
        //         $msg_order_info = $helper->createOrderMessage($order);
        //         $msg_to_customer = $msg_order_info ? $msg_order_new . '<br><br>' . $msg_order_info : $msg_order_new;
        //         if ($is_deliver_tomorrow) {
        //             $msg_to_customer = '*Maaf, saat ini toko sudah tutup 🙏. Pesanan akan dikirim BESOK*.<br><br>' . $msg_to_customer;
        //         }
        //         array_push($messages, [
        //             'order_id'      => $order->id,
        //             'to'      => $helper->convertPhone($customer->phone),
        //             'message'    => $msg_to_customer,
        //             // 'footer' => 'Tidak ingin menerima pesan ini? Klik link di bawah:',
        //             // 'button_links' => [
        //             //     [
        //             //         'index' => 1,
        //             //         'urlButton' => [
        //             //             'displayText' => '🛑 Berhenti menerima pesan dari kami!',
        //             //             'url' => env('APP_URL').'/info/notif?phone='.$order->customer->phone.'&tkn='.$order->customer->id.'&is=off',
        //             //         ]
        //             //     ]
        //             // ]
        //             // 'type' => 'new_order_to_customer',
        //             // 'footer' => 'Transfer / QRIS klik link di bawah',
        //             // 'store_slug' => $order->store->slug,
        //             // 'total' => $order->total,
        //             // 'order_code' => $order->code,
        //         ]);
        //     }

        //     // Admin Toko & CS
        //     $msg_to_cs = '📥 *New Job* by PWA ' . config('app.url') . '/manager/job-list/' . $order->store->slug . '?date=' . date_format($order->created_at, 'Y-m-d') . '&status=total&search=' . $order->code . ' ' . $order->customer->name;
        //     // Admin Toko
        //     // if ($order->store->whatsapp_2) {
        //     //     array_push($messages, [
        //     //         'order_id'      => $order->id,
        //     //         'to'      => $helper->convertPhone($order->store->whatsapp_2),
        //     //         'message'    => $msg_to_cs,
        //     //     ]);
        //     // }
        //     // CS
        //     if ($is_deliver_tomorrow) {
        //         $msg_to_cs = '📥 *Ada Job untuk BESOK*. Untuk langsung dikirim hari ini, klik link di bawah > BATAL > GANTI TANGGAL. Atau abaikan pesan ini untuk tetap dikirim besok.<br><br>' . $msg_to_cs;
        //     }
        //     array_push($messages, [
        //         'order_id'      => $order->id,
        //         'to'      => $helper->convertPhone($order->store->whatsapp_1),
        //         'message'    => $msg_to_cs,
        //     ]);
        //     $response =  $helper->processSendNotifToWablas($messages);
        // }

        $customer->options = array_merge((array) $customer->options, [
            'last_order_at' => Carbon::now()->toDateTimeString(),
            'last_order_or_notified_at' => Carbon::now()->toDateTimeString(),
        ]);
        $customer->save();

        // TODO: Mark order is from WBCL
        // if (isset($customer->options['last_notification_schedule_id'])) {
        //     $schedule = NotificationSchedule::find($customer->options['last_notification_schedule_id']);
        //     if ($schedule) {
        //         $schedule->update([
        //             'others' => 1,
        //         ]);
        //     }
        // }

        return response()->json(
            [
                'customer' => $customer_updated,
                'order' => $order,
                // 'notification_is_sent' => $order->store->is_notif_wa,
                'notification_is_sent' => $isSuccesSendNotifViaSocialchat,
                // 'notification'=>json_decode($response_notification_admin)
            ]
        );
    }

    public function generateDepositHistory($customer_id)
    {

        $helper = new Helper;

        if ($customer_id > 0) {
            $customers = Customer::where('id', $customer_id)->get();
        } else {
            $customers = Customer::all();
        }

        set_time_limit(300); // Extends to 5 minutes.
        foreach ($customers as $customer) {
            $helper->generateDepositHistory($customer->id);
        }

        return 'success';
    }

    public function customerStore($phone)
    {

        if (!isset($phone) || empty($phone)) {
            return 0;
        }
        $helper = new Helper;
        $customer = Customer::where('phone', $helper->convertPhone($phone))->first();
        if (!$customer) {
            return 0;
        }

        return [
            'customer' => $customer,
            'stores' => $customer->addresses()->with(['store'])->get()->map(function ($address) {
                return $address->store;
            }),
        ];
    }





    public function testView(Request $request)
    {
        // $ch = curl_init();  
        // // $url = env('API_URL').'/api/revalidate?secret='.env('API_TOKEN');
        // // $url = 'https://maps.app.goo.gl/tvKcrpRMEbjT2VbBA';
        // $url = 'https://www.google.com/maps/place/Jambon+Asri+Residen,+Jatimulyo+Indah+No.5,+Kricak,+Kec.+Tegalrejo,+Kota+Yogyakarta,+Daerah+Istimewa+Yogyakarta+55242/data=!4m2!3m1!1s0x2e7a5869f93512cd:0x40e9036c91cb1556?utm_source=mstt_1&entry=gps&g_ep=CAESBzEwLjgyLjEYACD___________8BKgA%3D';
        // curl_setopt($ch,CURLOPT_URL,$url);
        // curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($ch,CURLOPT_BINARYTRANSFER,true);
        // // curl_setopt($ch,CURLOPT_HEADER, false); 
        // curl_setopt($ch,CURLOPT_TIMEOUT, 4);    
        // // curl_setopt($ch,CURLOPT_FRESH_CONNECT, true); 
        // // curl_setopt($ch,CURLOPT_FOLLOWLOCATION, false); 
        // // curl_setopt($ch,CURLOPT_FAILONERROR, true); 

        // $output = curl_exec($ch);

        // curl_close($ch);

        // // dd($output);
        // $html = htmlentities($output);


        // return htmlentities($output);
        // function get_redirect_target($destination){
        // $destination = 'https://goo.gl/maps/acyGGxZ32hWhTwRPA';
        // $headers = get_headers($destination, 1);
        // return $headers['Location'];
        // }
        // $current_time = new DateTime();
        // $data = Order::where('customer_id', 802)
        //     ->whereBetween('created_at', ['2022-03-15 02:54:33', $current_time->modify('+1 week')])
        //     ->get()
        //     ->toJson(JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        // return view('test', compact(
        //     'data',
        // ));

        // return DB::table('orders') 
        //      ->selectRaw('SELECT MAX(created_at) FROM orders')
        //      ->limit(10)
        //      ->get();
        //  $latest_order = DB::table('orders')
        //            ->select('address_id', DB::raw('MAX(created_at) as last_order_created_at'))
        //            ->groupBy('address_id');

        //            return $addresses = DB::table('addresses')
        //            ->joinSub($latest_order, 'latest_order', function ($join) {
        //                $join->on('addresses.id', '=', 'latest_order.address_id');
        //            })->limit(10)->get();
        // $month01 = Carbon::now();
        //         $month02 = Carbon::now();
        //         $month03 = Carbon::now();
        //         $month06 = Carbon::now();
        //         $month01->addMonths(-1);
        //         $month02->addMonths(-2);
        //         $month03->addMonths(-3);
        //         $month06->addMonths(-6);

        // $latest_order = DB::table('orders')
        //             ->whereNull('deleted_at')
        //             ->where('store_id', 1)
        //             ->select('address_id', DB::raw('MAX(created_at) as last_order_created_at'))
        //             ->groupBy('address_id');

        //         return $address = DB::table('addresses')
        //         ->where('store_id', 1)
        //         ->joinSub($latest_order, 'latest_order', function ($join) {
        //             $join->on('addresses.id', '=', 'latest_order.address_id');
        //         })
        //         // ->groupBy('customer_id')
        //         ->selectRaw("COUNT(CASE WHEN last_order_created_at >= '".$month01->toDateString()."' THEN 1 END) as month0")
        //             ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '".$month01->toDateString()."' THEN 1 END) as month1")
        //             ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '".$month02->toDateString()."' THEN 1 END) as month2")
        //             ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '".$month03->toDateString()."' THEN 1 END) as month3")
        //             ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '".$month06->toDateString()."' THEN 1 END) as month6")
        //             ->first();

        // $data = [
        //     'order_id' => 1,
        //     'service_rating' => 1,
        //     'delivery_rating' => 1,
        // ];

        // $delay = \DB::table('jobs')->count()*10;
        // ProcessNotif::dispatch($data)->delay(now()->addSeconds($delay));;

        // $helper = new Helper;
        // $messages = [
        //         'to'      => $helper->convertPhone('081331885989'),
        //         'from'    => 'palagan',
        //         'message'    => 'Messsdfage Dua nih bifefsa gk',
        // ];
        // $res = $helper->processSendNotifToServer($messages);
        // return $res['status'];
        // if ($res['status'] == 'success') {
        //     return 'OK';
        // } else {
        //     return "FAILED";
        // }

        // $helper = new Helper;
        // $messages = [
        //     [
        //         'to'      => $helper->convertPhone('081331885989'),
        //         'from'    => 'palagan',
        //         'message'    => 'Message Satu',
        //     ],
        //     [
        //         'to'      => $helper->convertPhone('081331885989'),
        //         'from'    => 'palagan',
        //         'message'    => 'Message Dua nih bisa gk',
        //     ]
        // ];
        // $helper->processSendNotifToWablas($messages);
        return [
            'status' => 'oke',
        ];
    }

    public function forwardMessage(Request $request)
    {
        $env_is_local = env("APP_ENV") === "local";
        $default_cs_phone = $env_is_local ? "*************" : "62882007121760";
        // Check Token
        // ====================================================
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(
                [
                    'status' => "error",
                    'error' => 'Key invalid!',
                ],
                500
            );
        }

        $data = $request->all();

        // Check Data
        // ====================================================
        if (empty($data["message"]) || empty($data["from_number"])) {
            return response()->json(
                [
                    'status' => "error",
                    'error' => "Message or destination phone number is empty",
                ],
                500,
            );
        }

        // return response()->json(
        //     [
        //         'status'=>"success",
        //         'message'=>$data["message"],
        //     ]
        // );
        // return $data["message"];

        // Forward Message
        // ====================================================
        $helper = new Helper;

        $is_cs = Store::where('whatsapp_1', $helper->convertPhone($data["from_number"]))
            // ->whereNull('deleted_at')
            ->first();

        if ($is_cs) {
            return response()->json(
                [
                    'status' => "success",
                    'message' => "Excluded nomer CS",
                ]
            );
        }

        // $customer = Customer::where('phone', $helper->convertPhone($data["from_number"]))->first();
        // $stores = $customer ? $customer->addresses()->with(['store'])->get()->map(function($address) {
        //     return $address->store;
        // }) : [];
        // if (!$customer) {
        //     return response()->json(
        //         [
        //             'status'=>"error",
        //             'error'=>"Customer not found",
        //         ],
        //         500,
        //     );
        // }

        // $from_name = $customer ? $customer->name : "New Customer";
        // $to_number = $customer && !empty($store[0]->whatsapp_1) ? $store[0]->whatsapp_1 : $default_cs_phone;

        $order = Order::whereHas('customer', function ($q) use ($helper, $data) {
            $q->where('phone', $helper->convertPhone($data["from_number"]))
                ->whereNull('deleted_at');
        })
            ->whereNull('deleted_at')
            ->orderByDesc('created_at')
            ->first();

        $from_name = $order ? $order->customer->name : "New Customer";
        $to_number = $order ? $order->store->whatsapp_1 : $default_cs_phone;

        $messages = [
            [
                'to'      => $helper->convertPhone($to_number),
                'message'    => "*" . $from_name . "* " . $data["from_number"],
            ],
            [
                'to'      => $helper->convertPhone($to_number),
                'forward_message'    => $data["message"],
            ],
        ];
        $helper->processSendNotifToWablas($messages);

        return response()->json(
            [
                'status' => "success",
                'message' => "Forward success",
            ]
        );
    }

    public function testSendNotifQueue(Request $request)
    {
        // $helper = new Helper;
        // $messages = [
        //         'to'      => $helper->convertPhone('081331885989'),
        //         'from'    => 'palagan',
        //         'message'    => 'Messsdfage Dua nih bifefsa gk',
        // ];
        // $res = $helper->processSendNotifToServer($messages);
        // return $res['status'];
        // if ($res['status'] == 'success') {
        //     return 'OK';
        // } else {
        //     return "FAILED";
        // }

        $helper = new Helper;
        $messages = [
            [
                'type' => 'message',
                'to'      => $helper->convertPhone('081331885989'),
                'message'    => '*Maaf, saat ini toko sudah tutup 🙏. Pesanan akan dikirim BESOK*.<br><br>*Selama Puasa last order menjadi jam 16:00*\n\nMohon menunggu, Gasplus Condongcatur sedang memproses pesanannya.<br><br>*ORDER :: Gasplus Condongcatur :: C230608-13I*<br><br>Nama: Niko Okta<br>HP: wa.me/*************<br>Alamat: Kompleks Perumahan Muslim Darussalam 3, Blok Tursina no.\n7, Dusun Gebang, Kelurahan Wedomartani, Kecamatan Ngemplak, Kabupaten Sleman 55584<br><br>*Produk:*<br>(33x) Aqua 220ml <br><br>Total Nota: Rp Rp1.320.000 (33 produk)<br>Ongkos Kirim: FREE (Kurir Gasplus)<br>*Total Tagihan: Rp 1.320.000* 💰<br><br><br>_Bukti transfer kirim ke no *CS ( wa.me/628112716455 )*_<br><br>_Transfer / QRIS klik link di bawah:_<br><br>🏧 *Lihat No.Rekening* 👉 http://cs.ordergasplus.test/info/payment/gasplus-condongcatur/tf?total=1320000&order_code=C230608-13I<br><br>📱 *Lihat QRIS* 👉 http://cs.ordergasplus.test/images/qris/gasplus-condongcatur.jpeg<br><br>🛑 *TIDAK MAU MENERIMA PESAN INI* 👉 http://localhost:3003/toko/gasplus-condongcatur/pengaturan?phone=*************&notif=off',
                'footer' => 'Tidak ingin menerima pesan ini? Klik link di bawah:',
                'button_links' => [
                    [
                        'index' => 1,
                        'urlButton' => [
                            'displayText' => '🛑 Berhenti menerima pesan dari kami!',
                            'url' => 'https://cs.ordergasplus.online'
                        ]
                    ]
                ]
            ],
            [
                'to'      => $helper->convertPhone('081331885989'),
                'message'    => 'Message ' . uniqid(),
                'footer' => 'Tidak ingin menerima pesan ini? Klik link di bawah:',
                'buttons' => [
                    [
                        'buttonId' => 'id1',
                        'buttonText' => [
                            'displayText' => '🛑 Berhenti menerima pesan dari kami!'
                        ],
                        'type' => 1
                    ]
                ]
            ],
            // [
            //     'to'      => $helper->convertPhone('081331885989'),
            //     'message'    => 'Message '.uniqid(),
            // ]
        ];
        // $helper->processSendNotifToServer($messages[0]);
        return $helper->queueSendNotif($messages);
        return [
            'status' => 'oke',
        ];
    }

    public function testSendNotifQueuePrior(Request $request)
    {
        // $helper = new Helper;
        // $messages = [
        //         'to'      => $helper->convertPhone('081331885989'),
        //         'from'    => 'palagan',
        //         'message'    => 'Messsdfage Dua nih bifefsa gk',
        // ];
        // $res = $helper->processSendNotifToServer($messages);
        // return $res['status'];
        // if ($res['status'] == 'success') {
        //     return 'OK';
        // } else {
        //     return "FAILED";
        // }

        // $helper = new Helper;
        // $messages = [
        //     [
        //         'order_id'      => $order->id,
        //         'to'      => $helper->convertPhone('081331885989'),
        //         'message'    => '👑 PRIOR Message ' . uniqid(),
        //     ]
        // ];
        // $helper->processSendNotifToWablas($messages);
        return [
            'status' => 'oke',
        ];
    }

    public function testGet(Request $request)
    {
        $current_time = new DateTime();
        return Order::where('customer_id', 802)
            ->whereBetween('created_at', ['2022-03-15 02:54:33', $current_time->modify('+1 week')])
            ->get();
        $helper = new Helper();
        $order = Order::find(1);
        return $helper->createOrderMessage($order);
        // return dd(ENV('WA_NOTIF_URL'));
        // if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
        //     return response()->json(['error' => 'Key invalid!']);
        // }
        $data = $request->all();
        return response()->json([
            // 'app_key' => config('app.key'),
            // 'api' => "OK",
            'data request' => $data,
        ]);
    }

    public function testPost(Request $request)
    {
        // if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
        //     return response()->json(['error' => 'Key invalid!']);
        // }
        $data = $request->all();
        return response()->json([
            // 'app_key' => config('app.key'),
            // 'api' => "OK",
            'data request' => $data,
        ]);
    }

    public function testSendMail(Request $request)
    {
        // $details = [
        //     'code' => 'CODE01',
        //     'name' => 'Niko Okta',
        //     'phone' => '*************',
        //     'address' => 'Jl. Satu Dua No.3',
        //     'receivephoto_url' => 'https://cs.gasplus.online/public/gasplus/thumbnails/img/products/1/500-500/galon-aqua.jpeg?**********'
        // ];
        $order = \App\Models\Order::where('id', 1)->withTrashed()->with(['products', 'customer', 'address', 'store', 'store.banks'])->orderBy('sort_order')->first();
        // \Mail::to('<EMAIL>')->send(new \App\Mail\MailNewOrder($order));
        $from = "<EMAIL>";
        $to = "<EMAIL>";
        $subject = "Test Order Baru 🎉 " . $order->code;
        $message = 'oiajweoifjaowiejf';
        // $message = view('emails.order', $order);
        $headers = "From:" . $from;
        mail($to, $subject, $message, $headers);

        return dd("Email is Sent.");
    }

    public function testSendNotifWa(Request $request)
    {
        if (empty($request->input('app_key')) || $request->input('app_key') != config('app.key')) {
            return response()->json(['error' => 'Key invalid!']);
        }
        $helper = new Helper();
        $response =  $helper->queueSendNotif([
            [
                'to'      => '*************',
                'message'    => 'Test notif dari Niko',
                'timeout' => 1000,
            ],
            [
                'to'      => '+62 811-298-904',
                'message'    => 'Test notif dari Niko',
                'timeout' => 3000,
            ]
        ]);
        // return $response[0]->status;
        return $response;
    }

    public function test(Request $request)
    {
        // $pwa_all = Helper::revalidateFrontEnd();
        $pwa = Helper::revalidateFrontEnd('gasplus-palagan,gasplus-malioboro');
        $beltor = Helper::revalidateBelanjaKantor();
        return [
            // "pwa_all" => $pwa_all,
            "pwa" => $pwa,
            "beltor" => $beltor,
        ];
    }
}
