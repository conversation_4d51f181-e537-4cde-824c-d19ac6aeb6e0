<?php

namespace App\Http\Controllers;

use DateTime;
use DatePeriod;
use DateInterval;
use Carbon\Carbon;
use App\Models\Area;
use App\Models\User;
use App\Models\Order;
use App\Models\Store;
// use App\Models\Total;
use App\Helper\Helper;
use App\Models\Address;
use App\Models\Deposit;
use App\Models\Invoice;
use App\Models\Category;
use App\Models\Customer;
use App\Models\Marketing;
use App\Models\Socialchat;
use App\Models\OrderProduct;
use App\Models\ProductStore;
use Illuminate\Http\Request;
use App\Models\AdditionalCost;
use App\Models\CustomerProduct;
use App\Helper\HelperSocialchat;
// use App\Models\OrderProduct;
use App\Models\PpobCustomerData;
use App\Models\PpobResponseData;
use App\Models\OperatingCostItem;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Builder;

// use App\Models\Store;

class JobsController extends Controller
{

  public function calendar(Request $request, $store_slug)
  {
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'calendar');
    if (!$validateStoreSlug['status']) {
      if (empty($validateStoreSlug['store_slug'])) {
        return "User: \"" . (auth()->user()->email ?? auth()->user()->name) . "\" tidak aktif (belum di set ke toko tertentu). Silahkan kontak cs/admin.";
      }
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    $datetime_cache = new DateTime;
    $datetime_cache->modify(config('constants.cache.days_cached'));

    // ALL
    if ($store_slug === 'all') {
      $stores = Store::distinct();
      if (in_array(auth()->user()->role_id, [3, 4, 6])) {
        $store_ids = auth()->user()->stores ? auth()->user()->stores->pluck('id')->toArray() : [];
        $stores = $stores->whereIn('id', $store_ids)->orderBy('sort_order')->get();
      } else {
        $stores = $stores->orderBy('sort_order')->get();
      }
      $date_now = date('Y-m-d');
      $date_now = $request->query('date') ? $request->query('date') : $date_now;
      $date_compare = date('Y-m-d', strtotime($date_now . " +1 day"));
      $date_prev = date('Y-m-d', strtotime($date_now . " -1 day"));
      $future_order_count = Order::whereDate('created_at', '>=', $date_compare)->orderByDesc('created_at')->count();
      $future_marketing_count = Marketing::whereDate('created_at', '>=', $date_compare)->orderByDesc('created_at')->count();
      $datetime_current = new DateTime;
      $datetime_current->modify('-1 day');
      $datetime_now = new DateTime($date_now);
      $date_next = $future_order_count > 0 || $future_marketing_count > 0 ? date('Y-m-d', strtotime($date_now . " +1 day")) : ($datetime_current > $datetime_now ? date('Y-m-d', strtotime($date_now . " +1 day")) : null);

      $data = [];
      $sum_total_job = 0;
      $sum_total_bebas = 0;
      $sum_total_diambil = 0;
      $sum_total_terkirim = 0;
      $sum_total_selesai = 0;
      $sum_total_batal = 0;
      $sum_total_overtime = 0;
      $sum_total_bbro = 0;

      $seconds_cache_expire = $datetime_now < $datetime_cache ? config('constants.cache.expire_in_seconds') : config('constants.cache.expire_quick_in_seconds');

      foreach ($stores as $store) {
        if (isPageRefreshed()) {
          Cache::forget('calendar_orders_all_' . $store->slug . '_' . $date_now);
        }

        $orders = DB::table('orders')
          ->whereNull('deleted_at')
          ->where('store_id', $store->id)
          ->whereDate('created_at', $date_now);

        $orders = Cache::remember(
          'calendar_orders_all_' . $store->slug . '_' . $date_now,
          $seconds_cache_expire,
          function () use ($orders) {
            return $orders
              // ->selectRaw('count(*) as total_job')
              ->selectRaw("COUNT(CASE WHEN status_id = 1 THEN 1 END) as total_bebas")
              ->selectRaw("COUNT(CASE WHEN status_id = 2 OR status_id = 3 THEN 1 END) as total_diambil")
              ->selectRaw("COUNT(CASE WHEN status_id = 4 THEN 1 END) as total_terkirim")
              ->selectRaw("COUNT(CASE WHEN status_id = 5 THEN 1 END) as total_batal")
              ->selectRaw("COUNT(CASE WHEN status_id = 6 THEN 1 END) as total_selesai")
              ->selectRaw("COUNT(CASE WHEN status_id = 6 THEN 1 END) as total_selesai")
              ->selectRaw("COUNT(CASE WHEN (status_id = 4 OR status_id = 6 OR status_id = 7) AND (duration > 5400 OR duration < -5400) THEN 1 END) AS total_overtime")
              ->first();
          }
        );

        $total_bebas = $orders->total_bebas;
        $total_diambil = $orders->total_diambil;
        $total_terkirim = $orders->total_terkirim;
        $total_batal = $orders->total_batal;
        $total_selesai = $orders->total_selesai;
        $total_overtime = $orders->total_overtime;
        $total_bbro = 999;

        // $total_diambil_a = $totals[$key_diambil_a]['total'] ?? 0;
        // $total_diambil_b = $totals[$key_diambil_b]['total'] ?? 0;
        // $total_diambil = $total_diambil_a + $total_diambil_b;
        // $total_terkirim = $totals[$key_terkirim]['total'] ?? 0;
        // $total_batal = $totals[$key_batal]['total'] ?? 0;
        // $total_selesai = $totals[$key_selesai]['total'] ?? 0;
        $total_job = $total_bebas + $total_diambil + $total_terkirim + $total_selesai; // kecuali batal

        array_push($data, [
          // 'order' => $orders_selesai->whereIn('status_id', [4])->count(),
          'url' => route('calendar', [
            'store_slug' => $store->slug,
            'month' => date('Y-m', strtotime($date_now)),
          ]),
          'store' => $store->name,
          'total' => $total_job,
          'bebas' => $total_bebas,
          'diambil' => $total_diambil,
          'terkirim' => $total_terkirim,
          'batal' => $total_batal,
          'selesai' => $total_selesai,
          'overtime' => $total_overtime,
          'bbro' => $total_bbro,
        ]);

        $sum_total_job += $total_job;
        $sum_total_bebas += $total_bebas;
        $sum_total_diambil += $total_diambil;
        $sum_total_terkirim += $total_terkirim;
        $sum_total_selesai += $total_selesai;
        $sum_total_batal += $total_batal;
        $sum_total_overtime += $total_overtime;
        $sum_total_bbro += $total_bbro;
      }

      return view(
        'manager.calendar',
        compact(
          'store_slug',
          'date_now',
          'date_prev',
          'date_next',
          'data',
          'stores',
          'sum_total_job',
          'sum_total_bebas',
          'sum_total_diambil',
          'sum_total_terkirim',
          'sum_total_selesai',
          'sum_total_batal',
          'sum_total_overtime',
          'sum_total_bbro',
        )
      );

      // SINGLE STORE
    } else {
      $store = Store::where('slug', $store_slug)->first();
      $month_now = date('Y-m');

      $month_now = $request->query('month') ? $request->query('month') : $month_now;
      $month_prev = date('Y-m', strtotime($month_now . " -1 month"));
      $month_next = date('Y-m') == $month_now ? null : date('Y-m', strtotime($month_now . " +1 month"));
      $future_order = Order::where('store_id', $store->id)
        ->whereDate('created_at', '>=', date('Y-m-d'))
        ->orderByDesc('created_at')
        ->first();
      $date_current = date('Y-m') == $month_now ? ($future_order ? $future_order->created_at : date('Y-m-d')) : date("Y-m-t", strtotime($month_now));

      $begin = new DateTime($month_now);
      $begin->modify('first day of this month');
      $end = new DateTime($date_current);
      if (date('Y-m') != $month_now || !$future_order) {
        $end = $end->modify('+1 day');
      }

      $interval = new DateInterval('P1D');
      $daterange = new DatePeriod($begin, $interval, $end);

      $hari = array(1 => "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu", "Minggu");
      $data = [];
      $sum_total_job = 0;
      $sum_total_bebas = 0;
      $sum_total_diambil = 0;
      $sum_total_terkirim = 0;
      $sum_total_selesai = 0;
      $sum_total_batal = 0;
      $sum_total_overtime = 0;
      $sum_total_bbro = 0;

      foreach ($daterange as $date) {

        // $total = Total::where('totalable_type', 'App\Models\Status')
        //     ->where('store_id', $store->id)
        //     ->whereDate('date', $date->format("Y-m-d"));

        // if (in_array(auth()->user()->role_id, [5, 6])) {
        //     $total->where('user_id', auth()->user()->id);
        // }

        // if (!$total->first() || $request->query('refresh')) {

        //     $orders_bebas = Order::where('store_id', $store->id)
        //         ->whereDate('created_at', $date->format("Y-m-d"));

        //     $orders_diambil_a = Order::where('store_id', $store->id)
        //         ->whereDate('created_at', $date->format("Y-m-d"));

        //     $orders_diambil_b = Order::where('store_id', $store->id)
        //         ->whereDate('created_at', $date->format("Y-m-d"));

        //     $orders_terkirim = Order::where('store_id', $store->id)
        //         ->whereDate('created_at', $date->format("Y-m-d"));

        //     $orders_batal = Order::where('store_id', $store->id)
        //         ->whereDate('created_at', $date->format("Y-m-d"));

        //     $orders_selesai = Order::where('store_id', $store->id)
        //         ->whereDate('created_at', $date->format("Y-m-d"));

        //     $data_default = [
        //         'date' => $date->format('Y-m-d'),
        //         'store_id' => $store->id,
        //         'totalable_type' => 'App\Models\Status',
        //     ];
        //     if (in_array(auth()->user()->role_id, [5, 6])) {
        //         // $orders->where('driver_id', auth()->user()->id);
        //         $orders_bebas->where('driver_id', auth()->user()->id);
        //         $orders_diambil_a->where('driver_id', auth()->user()->id);
        //         $orders_diambil_b->where('driver_id', auth()->user()->id);
        //         $orders_terkirim->where('driver_id', auth()->user()->id);
        //         $orders_batal->where('driver_id', auth()->user()->id);
        //         $orders_selesai->where('driver_id', auth()->user()->id);

        //         $data_default['user_id'] = auth()->user()->id;
        //     }

        //     $data_bebas = array_merge($data_default, ['totalable_id' => 1]);
        //     $data_diambil_a = array_merge($data_default, ['totalable_id' => 2]);
        //     $data_diambil_b = array_merge($data_default, ['totalable_id' => 3]);
        //     $data_terkirim = array_merge($data_default, ['totalable_id' => 4]);
        //     $data_batal = array_merge($data_default, ['totalable_id' => 5]);
        //     $data_selesai = array_merge($data_default, ['totalable_id' => 6]);

        //     Total::updateOrCreate(
        //         $data_bebas,
        //         ['total' => $orders_bebas->whereIn('status_id', [1])->count()],
        //     );
        //     Total::updateOrCreate(
        //         $data_diambil_a,
        //         ['total' => $orders_diambil_a->whereIn('status_id', [2])->count()],
        //     );
        //     Total::updateOrCreate(
        //         $data_diambil_b,
        //         ['total' => $orders_diambil_b->whereIn('status_id', [3])->count()],
        //     );
        //     Total::updateOrCreate(
        //         $data_terkirim,
        //         ['total' => $orders_terkirim->whereIn('status_id', [4])->count()],
        //     );
        //     Total::updateOrCreate(
        //         $data_batal,
        //         ['total' => $orders_batal->whereIn('status_id', [5])->count()],
        //     );
        //     Total::updateOrCreate(
        //         $data_selesai,
        //         ['total' => $orders_selesai->whereIn('status_id', [6])->count()],
        //     );
        // }

        // $totals = Total::where('totalable_type', 'App\Models\Status')
        //     ->where('store_id', $store->id)
        //     ->whereDate('date', $date->format("Y-m-d"));

        // if (in_array(auth()->user()->role_id, [5, 6])) {
        //     $totals->where('user_id', auth()->user()->id);
        // } else {
        //     $totals->whereNull('user_id');
        // }

        // $totals = $totals->get()->toArray();

        // $key_bebas = array_search(1, array_column($totals, 'totalable_id'));
        // $key_diambil_a = array_search(2, array_column($totals, 'totalable_id'));
        // $key_diambil_b = array_search(3, array_column($totals, 'totalable_id'));
        // $key_terkirim = array_search(4, array_column($totals, 'totalable_id'));
        // $key_batal = array_search(5, array_column($totals, 'totalable_id'));
        // $key_selesai = array_search(6, array_column($totals, 'totalable_id'));

        // $total_bebas = $totals[$key_bebas]['total'] ?? 0;
        // $total_diambil_a = $totals[$key_diambil_a]['total'] ?? 0;
        // $total_diambil_b = $totals[$key_diambil_b]['total'] ?? 0;
        // $total_diambil = $total_diambil_a + $total_diambil_b;
        // $total_terkirim = $totals[$key_terkirim]['total'] ?? 0;
        // $total_batal = $totals[$key_batal]['total'] ?? 0;
        // $total_selesai = $totals[$key_selesai]['total'] ?? 0;
        // $total_job = $total_bebas + $total_diambil + $total_terkirim + $total_selesai; // kecuali batal

        $user = auth()->user();
        $user_id = auth()->user()->id;

        if (isPageRefreshed()) {
          Cache::forget('calendar_marketings_' . $store->slug . '_' . $date->format('Y-m-d') . '_' . $user_id);
          Cache::forget('calendar_orders_' . $store->slug . '_' . $date->format('Y-m-d') . '_' . $user_id);
        }
        $seconds_cache_expire = $date < $datetime_cache ? config('constants.cache.expire_in_seconds') : config('constants.cache.expire_quick_in_seconds');

        $orders = DB::table('orders')
          ->whereNull('deleted_at')
          ->where('store_id', $store->id)
          ->whereDate('created_at', $date->format("Y-m-d"));

        $total_bbro = 0;

        if (in_array(auth()->user()->role_id, [5])) {
          $orders->where('driver_id', auth()->user()->id);

          $marketings = Cache::remember(
            'calendar_marketings_' . $store->slug . '_' . $date->format('Y-m-d') . '_' . $user_id,
            $seconds_cache_expire,
            function () use ($store, $date, $user_id) {
              return DB::table('marketings')
                ->whereNull('deleted_at')
                ->where('store_id', $store->id)
                ->whereDate('created_at', $date->format('Y-m-d'))
                ->where('driver_id', $user_id)
                ->selectRaw(
                  'COUNT(CASE WHEN status_id = 6 THEN 1 END) as total_selesai'
                )
                ->first();
            }
          );
          $total_bbro = $marketings->total_selesai;
        }

        $orders = Cache::remember(
          'calendar_orders_' . $store->slug . '_' . $date->format('Y-m-d') . '_' . $user_id,
          $seconds_cache_expire,
          function () use ($orders) {
            return $orders
              // ->selectRaw('count(*) as total_job')
              ->selectRaw(
                'COUNT(CASE WHEN status_id = 1 THEN 1 END) as total_bebas'
              )
              ->selectRaw(
                'COUNT(CASE WHEN status_id = 2 OR status_id = 3 THEN 1 END) as total_diambil'
              )
              ->selectRaw(
                'COUNT(CASE WHEN status_id = 4 THEN 1 END) as total_terkirim'
              )
              ->selectRaw(
                'COUNT(CASE WHEN status_id = 5 THEN 1 END) as total_batal'
              )
              ->selectRaw(
                'COUNT(CASE WHEN status_id = 6 THEN 1 END) as total_selesai'
              )
              ->selectRaw(
                'COUNT(CASE WHEN (status_id = 4 OR status_id = 6 OR status_id = 7) AND (duration > 5400 OR duration < -5400) THEN 1 END) as total_overtime'
              )
              ->first();
          }
        );

        $total_bebas = $orders->total_bebas;
        $total_diambil = $orders->total_diambil;
        $total_terkirim = $orders->total_terkirim;
        $total_batal = $orders->total_batal;
        $total_selesai = $orders->total_selesai;
        $total_overtime = $orders->total_overtime;

        // $total_diambil_a = $totals[$key_diambil_a]['total'] ?? 0;
        // $total_diambil_b = $totals[$key_diambil_b]['total'] ?? 0;
        // $total_diambil = $total_diambil_a + $total_diambil_b;
        // $total_terkirim = $totals[$key_terkirim]['total'] ?? 0;
        // $total_batal = $totals[$key_batal]['total'] ?? 0;
        // $total_selesai = $totals[$key_selesai]['total'] ?? 0;
        $total_job = $total_bebas + $total_diambil + $total_terkirim + $total_selesai; // kecuali batal

        array_push($data, [
          // 'order' => $orders_selesai->whereIn('status_id', [4])->count(),
          'url' => route('jobs', [
            'store_slug' => $store_slug,
            'date' => $date->format("Y-m-d"),
          ]),
          'date' => $date->format("j"),
          'day' => $hari[$date->format("N")],
          'total' => $total_job,
          'bebas' => $total_bebas,
          'diambil' => $total_diambil,
          'terkirim' => $total_terkirim,
          'batal' => $total_batal,
          'selesai' => $total_selesai,
          'overtime' => $total_overtime,
          'bbro' => $total_bbro,
        ]);

        $sum_total_job += $total_job;
        $sum_total_bebas += $total_bebas;
        $sum_total_diambil += $total_diambil;
        $sum_total_terkirim += $total_terkirim;
        $sum_total_selesai += $total_selesai;
        $sum_total_batal += $total_batal;
        $sum_total_overtime += $total_overtime;
        $sum_total_bbro += $total_bbro;
      }

      $data = array_reverse($data);

      if ((int)auth()->user()->role_id <= 2) {
        $stores = Store::all();
      } else {
        $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
      }

      return view(
        'manager.calendar',
        compact(
          'store_slug',
          'month_now',
          'month_prev',
          'month_next',
          'data',
          'stores',
          'store',
          'sum_total_job',
          'sum_total_bebas',
          'sum_total_diambil',
          'sum_total_terkirim',
          'sum_total_selesai',
          'sum_total_batal',
          'sum_total_overtime',
          'sum_total_bbro',
        )
      );
    }
  }

  public function jobs(Request $request, $store_slug)
  {
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'jobs');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('jobs', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'date' => $request->query('date'),
      ]);
    }
    $store = Store::where('slug', $store_slug)->first();
    $date_now = date('Y-m-d');

    $date_now = $request->query('date') ? $request->query('date') : $date_now;
    $date_compare = date('Y-m-d', strtotime($date_now . " +1 day"));
    $date_prev = date('Y-m-d', strtotime($date_now . " -1 day"));
    $future_order_count = Order::where('store_id', $store->id)->whereDate('created_at', '>=', $date_compare)->orderByDesc('created_at')->count();
    $future_marketing_count = Marketing::where('store_id', $store->id)->whereDate('created_at', '>=', $date_compare)->orderByDesc('created_at')->count();
    $datetime_current = new DateTime;
    $datetime_current->modify('-1 day');
    $datetime_now = new DateTime($date_now);
    $datetime_cache = new DateTime;
    $datetime_cache->modify(config('constants.cache.days_cached'));
    $date_next = $future_order_count > 0 || $future_marketing_count > 0 ? date('Y-m-d', strtotime($date_now . " +1 day")) : ($datetime_current > $datetime_now ? date('Y-m-d', strtotime($date_now . " +1 day")) : null);

    $seconds_cache_expire = $datetime_now < $datetime_cache ? config('constants.cache.expire_in_seconds') : config('constants.cache.expire_quick_in_seconds');

    $hari = array(1 => "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu", "Minggu");
    $day = $hari[date('N', strtotime($date_now))];

    // $orders = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // // $orders_total = Order::where('store_id', $store->id)
    // //         ->whereDate('created_at', $date_now);

    // $orders_bebas = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_diambil = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_terkirim = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_terkirim_cash = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_terkirim_non_cash = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_batal = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_selesai = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_selesai_cash = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_selesai_non_cash = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    // $orders_selesai_split_cash = Order::where('store_id', $store->id)
    //         ->whereDate('created_at', $date_now);

    if (isPageRefreshed()) {
      Cache::forget('jobs_invoice_' . $store->slug . '_' . $date_now);
      Cache::forget('jobs_invoice_confirmed_' . $store->slug . '_' . $date_now);
      Cache::forget('jobs_current_marketing_' . $store->slug . '_' . $date_now . '_' . auth()->user()->id);
      Cache::forget('jobs_order_' . $store->slug . '_' . $date_now . '_' . auth()->user()->id);
      Cache::forget('jobs_marketing_' . $store->slug . '_' . $date_now . '_' . auth()->user()->id);
      Cache::forget('jobs_marketing_plus_' . $store->slug . '_' . $date_now . '_' . auth()->user()->id);
      Cache::forget('jobs_order_bebas_' . $store->slug . '_' . $date_now);
      Cache::forget('jobs_banks_' . $store->slug . '_' . $date_now);
      Cache::forget('jobs_drivers_' . $store->slug . '_' . $date_now);
      Cache::forget('jobs_drivers_plus_' . $store->slug . '_' . $date_now);
      Cache::forget('jobs_orders_unfinish_' . $store->slug . '_' . $date_now);
      Cache::forget('jobs_ttl_month_' . $store->slug . '_' . $date_now);
      Cache::forget('jobs_ttl_today_' . $store->slug . '_' . $date_now);
    }

    $order = DB::table('orders')
      ->whereNull('deleted_at')
      ->where('store_id', $store->id)
      ->whereDate('created_at', $date_now);

    $invoice = Cache::remember(
      'jobs_invoice_' . $store->slug . '_' . $date_now,
      $seconds_cache_expire,
      function () use ($store) {
        return DB::table('invoices')
          ->whereNull('deleted_at')
          ->where('store_id', $store->id)
          // ->selectRaw('count(*) as invoice_total_count')
          ->selectRaw("COUNT(CASE WHEN confirmed_at IS NULL THEN 1 END) AS invoice_total_count")
          ->first();
      }
    );

    $invoice_confirmed = Cache::remember(
      'jobs_invoice_confirmed_' . $store->slug . '_' . $date_now,
      $seconds_cache_expire,
      function () use ($store, $date_now) {
        return DB::table('invoices')
          ->whereNull('deleted_at')
          ->where('store_id', $store->id)
          ->whereNotNull('confirmed_at')
          ->whereDate('confirmed_at', $date_now)
          // ->selectRaw('count(*) as invoice_confirmed_non_cash_count')
          // ->selectRaw('count(*) as invoice_total_count')
          ->selectRaw("COUNT(CASE WHEN payment_method_confirmed != 'cash' THEN 1 END) AS invoice_confirmed_non_cash_count")
          ->selectRaw("SUM(IF(payment_method_confirmed != 'cash',amount_pay,0)) AS invoice_confirmed_non_cash_total")
          ->first();
      }
    );

    $operatingcost_total = Cache::remember(
      'jobs_operatingcost_total_' . $store->slug . '_' . $date_now,
      $seconds_cache_expire,
      function () use ($store, $date_now) {
        return OperatingCostItem::
          // ->whereNull('deleted_at')
          whereHas('operatingcost', function ($q) use ($store, $date_now) {
            $q->where('store_id', $store->id)
              ->whereDate('submit_at', $date_now);
          })
          ->whereNotIn('cost_category_id', [1, 2, 3, 4])
          ->selectRaw("SUM(price) AS total_cost")
          ->first();
      }
    );

    $marketing = DB::table('marketings')
      ->whereNull('deleted_at')
      ->where('store_id', $store->id)
      ->whereDate('created_at', $date_now);

    $marketing_plus = Marketing::whereNull('deleted_at')
      ->where('store_id', $store->id)
      ->whereDate('created_at', $date_now)
      ->whereHas('driver', function ($q) {
        $q->whereNotIn('role_id', [5]);
      });

    $current_marketing = Cache::remember(
      'jobs_current_marketing_' . $store->slug . '_' . $date_now . '_' . auth()->user()->id,
      $seconds_cache_expire,
      function () use ($store) {
        return Marketing::where('driver_id', auth()->user()->id)
          ->whereNull('area_id')
          ->where('code', '')
          ->whereNull('sequence_per_day')
          ->where('status_id', 3)
          ->where('store_id', $store->id)
          ->with([
            'photos',
          ])
          ->first();
      }
    );

    if (!$current_marketing) {
      $current_marketing = Marketing::create([
        'store_id' => $store->id,
        'driver_id' => auth()->user()->id,
        'status_id' => 3,
      ]);
    }

    $current_marketing->photos->map(function ($photo) {
      $photo->photo_url = $photo->thumbnail($width = 1000, $height = 1000, $filters = []);
      return $photo;
    });

    if (in_array(auth()->user()->role_id, [5])) {
      $order->where('driver_id', auth()->user()->id);
      $marketing->where('driver_id', auth()->user()->id);
      // // $order_bebas->where('driver_id', auth()->user()->id);
      // $order_diambil->where('driver_id', auth()->user()->id);
      // $order_terkirim->where('driver_id', auth()->user()->id);
      // $order_batal->where('driver_id', auth()->user()->id);
      // $order_selesai->where('driver_id', auth()->user()->id);
    }

    $order = Cache::remember(
      'jobs_order_' . $store->slug . '_' . $date_now . '_' . auth()->user()->id,
      $seconds_cache_expire,
      function () use ($order) {
        return $order
          ->selectRaw("COUNT(CASE WHEN status_id != 5 THEN 1 END) AS order_total_count")
          ->selectRaw("COUNT(CASE WHEN status_id = 1 THEN 1 END) AS order_bebas_count")
          ->selectRaw("COUNT(CASE WHEN status_id = 2 OR status_id = 3 THEN 1 END) AS order_diambil_count")
          ->selectRaw("COUNT(CASE WHEN status_id = 4 THEN 1 END) AS order_terkirim_count")
          ->selectRaw("COUNT(CASE WHEN status_id = 5 THEN 1 END) AS order_batal_count")
          ->selectRaw("COUNT(CASE WHEN status_id = 6 THEN 1 END) AS order_selesai_count")
          ->selectRaw("COUNT(CASE WHEN (status_id = 4 OR status_id = 6) AND payment_method_ask = 'cash' THEN 1 END) AS order_terkirim_cash_count")
          ->selectRaw("SUM(IF((status_id = 4 OR status_id = 6) AND payment_method_ask = 'cash',total,0)) AS order_terkirim_cash_total")
          ->selectRaw("COUNT(CASE WHEN (status_id = 4 OR status_id = 6) AND payment_method_ask = 'non-cash' THEN 1 END) AS order_terkirim_non_cash_count")
          ->selectRaw("SUM(IF((status_id = 4 OR status_id = 6) AND payment_method_ask = 'non-cash',total,0)) AS order_terkirim_non_cash_total")
          ->selectRaw("COUNT(CASE WHEN status_id = 6 AND payment_method_ask = 'cash' THEN 1 END) AS order_selesai_cash_count")
          ->selectRaw("SUM(IF(status_id = 6 AND payment_method_ask = 'cash',total,0)) AS order_selesai_cash_total")
          ->selectRaw("COUNT(CASE WHEN status_id = 6 AND payment_method_confirmed = 'invoice' THEN 1 END) AS order_selesai_invoice_count")
          ->selectRaw("SUM(IF(status_id = 6 AND payment_method_confirmed = 'invoice',total,0)) AS order_selesai_invoice_total")
          ->selectRaw("COUNT(CASE WHEN status_id = 6 AND payment_method_ask = 'non-cash' AND payment_method_confirmed != 'invoice' THEN 1 END) AS order_selesai_non_cash_count")
          ->selectRaw("SUM(IF(status_id = 6 AND payment_method_ask = 'non-cash' AND payment_method_confirmed != 'invoice',total,0)) AS order_selesai_non_cash_total")
          ->selectRaw("SUM(IF(status_id = 6 AND payment_method_confirmed != 'cash' AND amount_split_to_cash IS NOT NULL,amount_split_to_cash,0)) AS order_selesai_split_cash_total")
          ->first();
      }
    );

    $marketing = Cache::remember(
      'jobs_marketing_' . $store->slug . '_' . $date_now . '_' . auth()->user()->id,
      $seconds_cache_expire,
      function () use ($marketing) {
        return $marketing
          ->selectRaw("COUNT(CASE WHEN status_id != 5 THEN 1 END) AS marketing_total_count")
          ->selectRaw("COUNT(CASE WHEN (status_id = 2 OR status_id = 3) AND area_id IS NOT NULL THEN 1 END) AS marketing_diambil_count")
          ->selectRaw("COUNT(CASE WHEN status_id = 5 THEN 1 END) AS marketing_batal_count")
          ->selectRaw("COUNT(CASE WHEN status_id = 6 AND sequence_per_day IS NOT NULL THEN 1 END) AS marketing_selesai_count")
          ->first();
      }
    );

    $marketing_plus = Cache::remember(
      'jobs_marketing_plus_' . $store->slug . '_' . $date_now . '_' . auth()->user()->id,
      $seconds_cache_expire,
      function () use ($marketing_plus) {
        return $marketing_plus
          ->selectRaw("COUNT(CASE WHEN status_id = 6 AND sequence_per_day IS NOT NULL THEN 1 END) AS marketing_selesai_count")
          ->first();
      }
    );

    $order_bebas = Cache::remember(
      'jobs_order_bebas_' . $store->slug . '_' . $date_now,
      $seconds_cache_expire,
      function () use ($store, $date_now) {
        return DB::table('orders')
          ->whereNull('deleted_at')
          ->where('store_id', $store->id)
          ->whereDate('created_at', $date_now)
          ->selectRaw("COUNT(CASE WHEN status_id = 1 THEN 1 END) AS order_bebas_count")
          ->first();
      }
    );

    // return dd($order);

    // $order_total_count = $orders->whereNotIn('status_id', [5])->count();
    // $order_bebas_count = $orders_bebas->whereIn('status_id', [1])->count();
    // $order_diambil_count = $orders_diambil->whereIn('status_id', [2, 3])->count();
    // $order_terkirim_count = $orders_terkirim->whereIn('status_id', [4])->count();
    // $order_terkirim_cash_count = $orders_terkirim_cash->where('payment_method_ask', 'cash')->whereIn('status_id', [4, 6, 7])->count();
    // $order_terkirim_cash_total = $orders_terkirim_cash->where('payment_method_ask', 'cash')->whereIn('status_id', [4, 6, 7])->sum('total');
    // $order_terkirim_non_cash_count = $orders_terkirim_non_cash->where('payment_method_ask', 'non-cash')->whereIn('status_id', [4, 6, 7])->count();
    // $order_terkirim_non_cash_total = $orders_terkirim_non_cash->where('payment_method_ask', 'non-cash')->whereIn('status_id', [4, 6, 7])->sum('total');
    // $order_batal_count = $orders_batal->whereIn('status_id', [5])->count();
    // $order_selesai_count = $orders_selesai->whereIn('status_id', [6])->count();
    // $order_selesai_cash_count = $orders_selesai_cash->whereIn('status_id', [6])->where('payment_method_ask', 'cash')->count();
    // $order_selesai_cash_total = $orders_selesai_cash->whereIn('status_id', [6])->where('payment_method_ask', 'cash')->sum('total');
    // $order_selesai_non_cash_count = $orders_selesai_non_cash->whereIn('status_id', [6])->where('payment_method_ask', 'non-cash')->count();
    // $order_selesai_non_cash_total = $orders_selesai_non_cash->whereIn('status_id', [6])->where('payment_method_ask', 'non-cash')->sum('total');
    // $order_selesai_split_cash_total = $orders_selesai_split_cash->whereIn('status_id', [6])->where('payment_method_confirmed', '!=', 'cash')->whereNotNull('amount_split_to_cash')->sum('amount_split_to_cash');
    // // $order_total = $orders_total->whereIn('status_id', [6])->whereNotNull('payment_method_confirmed')->sum('total');

    $invoice_total_count = $invoice->invoice_total_count;
    $invoice_confirmed_non_cash_count = $invoice_confirmed->invoice_confirmed_non_cash_count;
    $invoice_confirmed_non_cash_total = $invoice_confirmed->invoice_confirmed_non_cash_total;

    $order_total_count = $order->order_total_count;
    $order_bebas_count = $order_bebas->order_bebas_count;
    $order_diambil_count = $order->order_diambil_count;
    $order_terkirim_count = $order->order_terkirim_count;
    $order_terkirim_cash_count = $order->order_terkirim_cash_count;
    $order_terkirim_cash_total = $order->order_terkirim_cash_total;
    $order_terkirim_non_cash_count = $order->order_terkirim_non_cash_count;
    $order_terkirim_non_cash_total = $order->order_terkirim_non_cash_total;
    $order_batal_count = $order->order_batal_count;
    $order_selesai_count = $order->order_selesai_count + $invoice_confirmed_non_cash_count;
    $order_selesai_cash_count = $order->order_selesai_cash_count;
    $order_selesai_cash_total = $order->order_selesai_cash_total;
    $order_selesai_invoice_count = $order->order_selesai_invoice_count;
    $order_selesai_invoice_total = $order->order_selesai_invoice_total;
    $order_selesai_non_cash_count = $order->order_selesai_non_cash_count;
    $order_selesai_non_cash_total = $order->order_selesai_non_cash_total;
    $order_selesai_split_cash_total = $order->order_selesai_split_cash_total;

    $marketing_total_count = $marketing->marketing_total_count;
    $marketing_diambil_count = $marketing->marketing_diambil_count;
    $marketing_selesai_count = $marketing->marketing_selesai_count;
    $marketing_plus_selesai_count = $marketing_plus->marketing_selesai_count;
    $marketing_batal_count = $marketing->marketing_batal_count;

    $banks = Cache::remember(
      'jobs_banks_' . $store->slug . '_' . $date_now,
      $seconds_cache_expire,
      function () use ($store, $date_now) {
        return $store->banks()->withCount([
          'orders AS orders_count' => function ($query) use ($date_now) {
            $query->whereDate('orders.created_at', $date_now);
          },
          'orders AS order_total' => function ($query) use ($date_now) {
            $query->whereDate('orders.created_at', $date_now)->select(DB::raw("SUM(amount_pay) as ordertotal"));
          },
          'invoices AS invoice_total' => function ($query) use ($date_now) {
            $query->whereDate('invoices.confirmed_at', $date_now)->select(DB::raw("SUM(amount_pay) as invoicetotal"));
          },
        ])->get();
      }
    );

    $drivers = Cache::remember(
      'jobs_drivers_' . $store->slug . '_' . $date_now,
      $seconds_cache_expire,
      function () use ($store, $date_now) {
        return User::whereIn('role_id', [5, 6])
          // ->where(function ($subquery) use ($store) {
          //   $subquery->where('store_id', $store->id)
          //     ->orWhereHas('stores', function ($subsubquery) use ($store) {
          //       $subsubquery->where('id', $store->id);
          //     });
          // })
          ->whereHas('orders', function ($q) use ($store, $date_now) {
            $q->where('store_id', $store->id)
              ->whereDate('created_at', $date_now);
          })
          ->withCount([
            'orders as orders_diambil' => function ($query) use ($store, $date_now) {
              $query->where('store_id', $store->id)
                ->whereDate('created_at', $date_now)
                ->whereIn('status_id', [2, 3]);
            },
            'orders as orders_terkirim' => function ($query) use ($store, $date_now) {
              $query->where('store_id', $store->id)
                ->whereDate('created_at', $date_now)
                ->whereIn('status_id', [4]);
            },
            'orders as orders_selesai' => function ($query) use ($store, $date_now) {
              $query->where('store_id', $store->id)
                ->whereDate('created_at', $date_now)
                ->whereIn('status_id', [6]);
            },
            'orders as orders_batal' => function ($query) use ($store, $date_now) {
              $query->where('store_id', $store->id)
                ->whereDate('created_at', $date_now)
                ->whereIn('status_id', [5]);
            },
            'marketings as marketings_diambil' => function ($query) use ($store, $date_now) {
              $query->where('store_id', $store->id)
                ->whereDate('created_at', $date_now)
                ->whereIn('status_id', [2, 3]);
            },
            'marketings as marketings_selesai' => function ($query) use ($store, $date_now) {
              $query->where('store_id', $store->id)
                ->whereNotNull('sequence_per_day')
                ->whereDate('created_at', $date_now)
                ->whereIn('status_id', [6]);
            },
            'marketings as marketings_batal' => function ($query) use ($store, $date_now) {
              $query->where('store_id', $store->id)
                ->whereDate('created_at', $date_now)
                ->whereIn('status_id', [5]);
            },
          ])
          ->distinct()
          ->get();
      }
    );

    $drivers_plus = Cache::remember(
      'jobs_drivers_plus_' . $store->slug . '_' . $date_now,
      $seconds_cache_expire,
      function () use ($store, $date_now) {
        return User::whereNotIn('role_id', [5, 6])
          // ->where('store_id', $store->id)
          ->whereHas('marketings', function ($query) use ($store, $date_now) {
            $query->where('store_id', $store->id)
              ->whereDate('created_at', $date_now);
          })
          ->withCount([
            'marketings as marketings_selesai' => function ($query) use ($store, $date_now) {
              $query->where('store_id', $store->id)
                ->whereNotNull('sequence_per_day')
                ->whereDate('created_at', $date_now)
                ->whereIn('status_id', [6]);
            },
          ])
          ->distinct()
          ->get();
      }
    );

    if ((int)auth()->user()->role_id <= 2) {
      $stores = Store::all();
    } else {
      $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
    }

    $order_total = $order_selesai_cash_total
      + $order_selesai_split_cash_total
      + $order_selesai_invoice_total
      // + $invoice_confirmed_non_cash_total
    ;

    foreach ($banks as $bank) {
      $order_total += $bank->order_total;
    }

    // $begin = new DateTime();
    // $begin->modify('-7 days');
    // $begin = $begin->format("Y-m-d");
    $begin = date('Y-m-d', strtotime("2024-05-01"));

    $orders_unfinish = Cache::remember(
      'jobs_orders_unfinish_' . $store->slug . '_' . $date_now,
      $seconds_cache_expire,
      function () use ($store, $date_now, $begin) {
        return Order::where('store_id', $store->id)
          ->where('status_id', '<=', 4)
          ->whereBetween('created_at', [$begin, $date_now])
          ->select(
            'created_at',
            // DB::raw("(sum(total)) as total"),
            // DB::raw("(DATE_FORMAT(created_at, '%d-%m-%Y')) as date")
          )
          ->orderBy('created_at')
          ->groupBy('created_at')
          // ->groupBy(DB::raw("DATE_FORMAT(created_at, '%Y-%m-%d')"))
          ->get()
          ->groupBy(function ($item) {
            return $item->created_at->format('Y-m-d');
          })
          ->map(function ($item, $key) {
            return [
              'date' => $key,
              'count' => $item->count(),
            ];
          });
      }
    );

    $total_job_this_month = 0;
    $total_job_this_month_overtime = 0;
    $total_job_today = 0;
    $total_job_today_overtime = 0;
    $total_operatingcost_today = $operatingcost_total->total_cost;
    if (auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) {
      $begin = new DateTime($date_now);
      $end = new DateTime($date_now);
      $begin->modify('first day of this month');
      $end->modify('last day of this month');

      $ttl_month = Cache::remember(
        'jobs_ttl_month_' . $store->slug . '_' . $date_now,
        $seconds_cache_expire,
        function () use ($store, $begin, $end) {
          return DB::table('orders')
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereIn('status_id', [4, 6, 7])
            // ->whereRaw('MONTH(created_at) = ?',['09'])
            // ->whereMonth('created_at', '09')
            // ->whereMonth('created_at', date('m', strtotime($date_now)))
            ->whereBetween('created_at', [$begin, $end])
            ->selectRaw('count(*) as count_month')
            ->selectRaw('COUNT(CASE WHEN duration > 5400 OR duration < -5400 THEN 1 END) AS count_month_overtime')
            // ->selectRaw("count(case when created_at > 5400 or duration < -5400 then 1 end) as count_delivered_overtime")
            // ->selectRaw('COUNT(CASE WHEN created_at = CAST("'.date('Y-m-d', strtotime($date_now)).'" AS datetime) THEN 1 END) AS count_today')
            // ->selectRaw('COUNT(CASE WHEN duration > 5400 OR duration < -5400 THEN 1 END) AS count_today_overtime')
            ->first();
        }
      );
      $ttl_today = Cache::remember(
        'jobs_ttl_today_' . $store->slug . '_' . $date_now,
        $seconds_cache_expire,
        function () use ($store, $date_now) {
          return DB::table('orders')
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereIn('status_id', [4, 6, 7])
            ->whereDate('created_at', $date_now)
            ->selectRaw('count(*) as count_today')
            // ->selectRaw("count(case when created_at > 5400 or duration < -5400 then 1 end) as count_delivered_overtime")
            // ->selectRaw('COUNT(CASE WHEN created_at = CAST("'.date('Y-m-d', strtotime($date_now)).'" AS datetime) THEN 1 END) AS count_today')
            ->selectRaw('COUNT(CASE WHEN duration > 5400 OR duration < -5400 THEN 1 END) AS count_today_overtime')
            ->first();
        }
      );
      // $total_job_today = date('Y-m-d', strtotime($date_now));
      // $total_job_this_month = date('m', strtotime($date_now));
      $total_job_this_month = $ttl_month->count_month;
      $total_job_this_month_overtime = $ttl_month->count_month_overtime;
      $total_job_today = $ttl_today->count_today;
      $total_job_today_overtime = $ttl_today->count_today_overtime;
    }

    return view(
      'manager.jobs',
      compact(
        'total_job_this_month',
        'total_job_this_month_overtime',
        'total_job_today',
        'total_job_today_overtime',
        'total_operatingcost_today',

        'store_slug',
        'date_now',
        'date_prev',
        'date_next',
        'day',

        'invoice_total_count',
        'invoice_confirmed_non_cash_count',
        'invoice_confirmed_non_cash_total',

        'orders_unfinish',
        'order_total',
        'order_total_count',
        'order_bebas_count',
        'order_diambil_count',
        'order_terkirim_count',
        'order_terkirim_cash_count',
        'order_terkirim_cash_total',
        'order_terkirim_non_cash_count',
        'order_terkirim_non_cash_total',
        'order_selesai_count',
        'order_selesai_cash_count',
        'order_selesai_cash_total',
        'order_selesai_invoice_count',
        'order_selesai_invoice_total',
        'order_selesai_non_cash_count',
        'order_selesai_non_cash_total',
        'order_selesai_split_cash_total',
        'order_batal_count',

        'marketing_total_count',
        'marketing_diambil_count',
        'marketing_selesai_count',
        'marketing_plus_selesai_count',
        'marketing_batal_count',

        'current_marketing',

        'drivers',
        'drivers_plus',
        'banks',
        'stores',
        'store'
      )
    );
  }

  public function joblist(Request $request, $store_slug)
  {
    $helperSocialchat = new HelperSocialchat();
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'joblist');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('joblist', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'date' => $request->query('date'),
        'status' => $request->query('status'),
      ]);
    }
    $date_now = $request->query('date') ? $request->query('date') : date('Y-m-d');

    $store = Store::where('slug', $store_slug)->first();

    $orders = Order::distinct()
      ->where('store_id', $store->id)
      ->whereDate('created_at', $date_now);

    $marketings = Marketing::distinct()
      ->where('store_id', $store->id)
      ->whereNotNull('sequence_per_day')
      ->whereDate('created_at', $date_now);

    $invoices = Invoice::distinct()
      ->where('store_id', $store->id)
      ->where(function ($query) use ($date_now) {
        $query->whereNull('payment_method_confirmed')
          ->orWhereDate('confirmed_at', $date_now);
      });

    $status = $request->query('status');
    $job_type = $request->query('type');
    $filter_ltt = $request->query('ltt');
    if ($filter_ltt === 'std_cpr') {
      $orders
        ->whereHas('orderproducts', function ($q) {
          $q->where('product_id', 102);
        })
        ->where('status_id', '!=', 5)
        ->with('orderproducts')
        ->has('orderproducts', '=', 1)
      ;
    } else if ($filter_ltt === 'spd_deposit') {
      $orders
        ->where('status_id', '!=', 5)
        ->where('deposit_job', '>', 0)
      ;
    }
    $color = 'bg-black';
    $current_date = new DateTime();
    $edge_date = new DateTime('2022-05-11');
    if ($job_type == 'invoice') {
      $color = 'bg-blue-200 text-blue-900';
    }
    if ($status == 'bebas') {
      if ($current_date > $edge_date) {
        $orders->whereIn('status_id', [1])
          ->orderBy('sequence_per_day', 'asc');
        $marketings->whereIn('status_id', [1])
          ->orderBy('sequence_per_day', 'asc');
      } else {
        $orders->whereIn('status_id', [1])
          ->orderBy('id', 'asc');
        $marketings->whereIn('status_id', [1])
          ->orderBy('id', 'asc');
      }
      $color = 'bg-yellow-500';
    } elseif ($status == 'overtime') {
      if ($current_date > $edge_date) {
        $orders->whereIn('status_id', [4, 6, 7])
          ->where(function ($q) {
            $q->where('duration', '>', 5400)
              ->orWhere('duration', '<', -5400);
          })
          ->orderBy('sequence_per_day', 'asc');
        // $marketings->whereIn('status_id', [2, 3])
        //     ->orderBy('sequence_per_day', 'asc');
      } else {
        $orders->whereIn('status_id', [4, 6, 7])
          ->where(function ($q) {
            $q->where('duration', '>', 5400)
              ->orWhere('duration', '<', -5400);
          })
          ->orderBy('id', 'asc');
        // $marketings->whereIn('status_id', [2, 3])
        //     ->orderBy('id', 'asc');
      }
      $color = 'bg-red-500';
    } elseif ($status == 'diambil') {
      if ($current_date > $edge_date) {
        $orders->whereIn('status_id', [2, 3])
          ->orderBy('sequence_per_day', 'asc');
        $marketings->whereIn('status_id', [2, 3])
          ->orderBy('sequence_per_day', 'asc');
      } else {
        $orders->whereIn('status_id', [2, 3])
          ->orderBy('id', 'asc');
        $marketings->whereIn('status_id', [2, 3])
          ->orderBy('id', 'asc');
      }
      $color = 'bg-blue-500';
    } elseif ($status == 'terkirim') {
      $orders->whereIn('status_id', [4])
        ->orderBy('updated_at', 'desc');
      $marketings->whereIn('status_id', [4])
        ->orderBy('updated_at', 'desc');
      $color = 'bg-blue-700';
    } elseif ($status == 'selesai') {
      if ($job_type != 'marketing') {
        $orders->whereIn('status_id', [6])
          ->orderBy('updated_at', 'desc');
      }
      $marketings->whereIn('status_id', [6])
        ->orderBy('updated_at', 'desc');
      $color = $job_type == 'marketing' ? 'bg-pink-500' : 'bg-green-500';
    } elseif ($status == 'batal') {
      $orders->whereIn('status_id', [5])
        ->orderBy('updated_at', 'desc');
      $marketings->whereIn('status_id', [5])
        ->orderBy('updated_at', 'desc');
      $color = 'bg-red-500';
    } else {
      if ($current_date > $edge_date) {
        $orders->orderBy('sequence_per_day', 'desc');
        $marketings->orderBy('sequence_per_day', 'desc');
        $invoices->orderBy('sequence_per_day', 'desc');
      } else {
        $orders->orderBy('id', 'desc');
        $marketings->orderBy('id', 'desc');
        $invoices->orderBy('id', 'desc');
      }
    }

    if (in_array(auth()->user()->role_id, [5]) && $status != 'bebas') {
      $orders->where('driver_id', auth()->user()->id);
      $marketings->where('driver_id', auth()->user()->id);
    }

    if (in_array(auth()->user()->role_id, [5]) && $store->hide_freejob_fordelman) {
      $orders->whereNotIn('status_id', [1]);
    }

    $orders = $orders->with([
      'armada',
      'store',
      'store.banks',
      'customer',
      'customer.merger',
      'customer.merger.members',
      'customer.delmanexcludes',
      'address',
      'address.photo',
      'products',
      'additionalcosts',
      'driver',
      'status',
      'cancelby',
      'author',
      'bank',
      'confirmedby',
      'receivephoto',
      'accurateInvoice',
    ])->get();
    $orders = $orders->map(function ($order) use ($helper, $helperSocialchat) {
      // ? Get PPOB Response Data & Customer Data
      $order->products = $order->products->map(function ($product) use ($order) {
        $product->ppobcustomerdata = $product->code !== 'EMONEY' ? null : PpobCustomerData::where('customer_id', $order->customer_id)
          ->where('key', $product->pivot->ppob_key)
          ->where('product_id', $product->id)
          ->where('ppob_product_code', $product->pivot->ppob_product_code)
          ->first();
        $product->ppobresponsedata = PpobResponseData::find($product->pivot->ppob_response_data_id);
        $order_product = OrderProduct::find($product->pivot->id);
        $helper = new Helper();
        $msg_ppob = $helper->createPpobMessage($order_product);
        $product->msg_ppob = $msg_ppob;
        return $product;
      });
      // ? Get Receiver Conversation ID
      $receiver_conversation_id = null;
      if ($order->receiver_phone) {
        $receiver_phone = $helper->convertPhone($order->receiver_phone);
        $socialchat = Socialchat::where("store_id", $order->store_id)
          ->where('customer_id', $order->customer_id)
          ->where('phone', $receiver_phone)
          ->orderBy('id', 'desc')
          ->first();
        if ($socialchat) {
          $receiver_conversation_id = $socialchat->conversation_id;
        } else {
          // $receiver_conversation_id = $helperSocialchat->getConversationId($order->store, $receiver_phone);
          // if ($receiver_conversation_id) {
          //   Socialchat::create([
          //     'store_id' => $order->store_id,
          //     'customer_id' => $order->customer_id,
          //     'phone' => $receiver_phone,
          //     'conversation_id' => $receiver_conversation_id,
          //   ]);
          // }
        }
      }
      $order->receiver_conversation_id = $receiver_conversation_id;

      // ? Get Customer Conversation ID
      $customer_conversation_id = null;
      if ($order->customer->phone) {
        $socialchat = Socialchat::where("store_id", $order->store_id)
          ->where('customer_id', $order->customer_id)
          ->where('phone', $order->customer->phone)
          ->orderBy('id', 'desc')
          ->first();
        if ($socialchat) {
          $customer_conversation_id = $socialchat->conversation_id;
        } else {
          // $customer_conversation_id = $helperSocialchat->getConversationId($order->store, $order->customer->phone);
          // if ($customer_conversation_id) {
          //   Socialchat::create([
          //     'store_id' => $order->store_id,
          //     'customer_id' => $order->customer_id,
          //     'phone' => $order->customer->phone,
          //     'conversation_id' => $customer_conversation_id,
          //   ]);
          // }
        }
      }
      $order->customer_conversation_id = $customer_conversation_id;

      $order->address_photo_url = $order->address->photo ? $order->address->photo->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
      $order->receive_photo_url = $order->receivephoto ? $order->receivephoto->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
      $order->payment_proof_url = $order->paymentproof ? $order->paymentproof->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
      $order->type = 'order';
      // Notif
      $close_hour_array = explode(":", $order->store->close_hour);
      $close_hour = new DateTime();
      $created_at = new DateTime($order->created_at);
      $close_hour->setTime((int) $close_hour_array[0], (int) $close_hour_array[1]);
      $is_deliver_tomorrow = $created_at > $close_hour;
      $msg_order_new = $order->store->msg_order_new ? $order->store->msg_order_new : 'Mohon menunggu. Pesanan sedang kami proses.';
      $is_asdt = str_contains($order->customer->name, 'ASDT');
      $msg_order_info = $helper->createOrderMessage($order, true, $is_asdt);
      $msg_to_customer = $msg_order_info ? $msg_order_new . '<br><br>' . $msg_order_info : $msg_order_new;
      if ($is_deliver_tomorrow) {
        $msg_to_customer = '*Maaf, saat ini toko sudah tutup 🙏. Pesanan akan dikirim BESOK*.<br><br>' . $msg_to_customer;
      }
      $order->notif_msg = $msg_to_customer;
      $order->notif_to = $helper->convertPhone($order->receiver_phone ? $order->receiver_phone : $order->customer->phone);
      return $order;
    });

    if ($job_type != 'marketing') {
      $marketings->where('code', 'dontincludethis');
    }

    $marketings = $marketings->with([
      'store',
      'area',
      'driver',
      'status',
      'cancelby',
      'author',
      'photos',
    ])->get();
    $marketings = $marketings->map(function ($marketing) {
      $marketing->photos = $marketing->photos ? $marketing->photos->map(
        function ($photo) {
          $photo->url = $photo->thumbnail($width = 1000, $height = 1000, $filters = []);
          $photo->created = $photo->created_at->format('d-j-y H:i:s');
          return $photo;
        }
      ) : null;
      $marketing->type = 'marketing';
      return $marketing;
    });

    $invoices = $invoices->with([
      'orders',
      'orders.products',
      'store',
      'store.banks',
      'bank',
      'customer',
      'confirmedby',
      'paymentproof',
    ])->get();
    $invoices = $invoices->map(function ($invoice) {
      $invoice->payment_proof_url = $invoice->paymentproof ? $invoice->paymentproof->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
      $invoice->type = 'invoice';
      return $invoice;
    });

    // $orders->toArray();
    // $marketings->toArray();

    // $orders = array_merge($orders, $marketings);

    $drivers = User::whereIn('role_id', [5, 6])
      ->where(function (Builder $q) use ($store_slug) {
        $q->whereHas('store', function (Builder $query) use ($store_slug) {
          $query->where('slug', $store_slug);
        })
          ->orWhereHas('stores', function (Builder $query) use ($store_slug) {
            $query->where('slug', $store_slug);
          });
      })
      ->get();

    $search_query = $request->query('search') ? $request->query('search') : '';

    return view(
      'manager.job-list',
      compact(
        'store_slug',
        'store',
        'date_now',
        'search_query',
        'orders',
        'marketings',
        'invoices',
        'status',
        'job_type',
        'color',
        'drivers'
      )
    );
  }

  public function addjob(Request $request, $store_slug)
  {
    // $type = $request->get('type');
    // $is_asdt = $type === 'asdt';
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'order-add');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('order-add', [
        'store_slug' => $validateStoreSlug['store_slug'],
      ]);
    }
    $stores = Store::all();

    $store = Store::where('slug', $store_slug)->first();
    $drivers = User::whereIn('role_id', [5, 6])
      ->whereHas('store', function ($q) use ($store_slug) {
        $q->where('slug', $store_slug);
      })
      ->get();

    return view(
      'manager.order-add',
      compact(
        'store_slug',
        'stores',
        // 'type',
        // 'is_asdt',
        'store',
        'drivers',
      )
    );
  }

  public function postjob(Request $request, $store_slug)
  {
    $data = $request->all();
    global $store_id;
    $is_with_product = false;
    $is_asdt_konsumsitoko = $data['oldnew'] === 'asdt' || $data['oldnew'] === 'konsumsitoko';
    $is_konsumsitoko = $data['oldnew'] === 'konsumsitoko';
    $is_asdt = $data['oldnew'] === 'asdt';
    $is_marketing = $data['oldnew'] === 'marketing';
    if ($data['oldnew'] === 'old') {
      if (isset($data['is_add_another_address']) && $data['is_add_another_address']) {
        $store_id = $data['store_id_another_address'];
      } else {
        $store_id = $data['store_id_old'];
      }
    } else if ($data['oldnew'] == 'new') {
      $store_id = $data['store_id_new'];
    } else if ($data['oldnew'] == 'marketing') {
      $store_id = $data['store_id_marketing'];
    } else if ($is_asdt_konsumsitoko) {
      $store_id = $data['store_id'];
    }
    $store = Store::find($store_id);
    $helper = new Helper();
    $created_at = new DateTime($data['created_at']);
    if (isset($data['receiver_phone_asdt']) && !empty($data['receiver_phone_asdt'])) {
      $data['receiver_phone'] = $data['receiver_phone_asdt'];
      unset($data['receiver_phone_asdt']);
    }

    // JOB MERKETING / BAGI BROSUR
    if ($data['oldnew'] === 'marketing') {
      $area_id = $data['area_id'];
      if (!is_numeric($area_id)) {
        $area_id = Area::create([
          'store_id' => $store->id,
          'name' => $data['area_id'],
          'latlng' => $data['latlng'],
        ])->id;
      } else {
        Area::where('id', $data['area_id'])
          ->update([
            'latlng' => $data['latlng'],
          ]);
      }

      $order = Marketing::create([
        'store_id' => $store->id,
        'area_id' => $area_id,
        'driver_id' => $data['driver_id'],
        'status_id' => 3,
        'note_for_driver' => isset($data['note_for_driver']) && !empty($data['note_for_driver']) ? $data['note_for_driver'] : null,
        'created_at' => $created_at,
      ]);


      // ASDT / KONSUMSI TOKO
    } else if ($is_asdt_konsumsitoko) {
      $keyword = $data['oldnew'] === 'konsumsitoko' ? 'KONSUMSI TOKO' : 'ASDT';
      $address = Address::where('store_id', $store->id)
        ->whereHas('customer', function ($q) use ($keyword) {
          $q->where('name', 'like', '%' . $keyword . '%')
            ->whereNull('deleted_at');
        })
        ->first();
      if (!$address) return 'Data pelanggan ' . $data['oldnew'] . ' tidak ditemukan!';
      $order = Order::create([
        'store_id' => $store->id,
        'customer_id' => $address->customer->id,
        'address_id' => $address->id,
        'status_id' => 4,
        'payment' => isset($data['payment']) && !empty($data['payment']) ? $data['payment'] : null,
        'payment_method_ask' => isset($data['payment']) && !empty($data['payment']) ? ($data['payment'] === 'cash' ? 'cash' : 'non-cash') : null,
        'receiver_phone' => isset($data['receiver_phone']) && !empty($data['receiver_phone']) ? $helper->convertPhone($data['receiver_phone']) : null,
        'note' => isset($data['note']) && !empty($data['note']) ? $data['note'] : null,
        'note_for_driver' => isset($data['note_for_driver']) && !empty($data['note_for_driver']) ? $data['note_for_driver'] : null,
        'created_at' => $created_at,
        'received_by' => $address->customer->name,
        'received_at' => $created_at,
      ]);
      $data_products = [];
      foreach ($data["product_id"] as $key => $product_id) {
        if ($product_id) {
          $productstore = ProductStore::where('store_id', $store->id)
            ->where('product_id', $product_id)
            ->first();
          $customerproduct = CustomerProduct::where('customer_id', $address->customer->id)
            ->where('product_id', $product_id)
            ->first();
          $local_price = $productstore ? ($productstore->local_price ? $productstore->local_price : $productstore->product->price) : 0;
          $special_price = $customerproduct && $customerproduct->local_price ? $customerproduct->local_price : $local_price;
          $data_products[$product_id] = [
            'qty' => $data['qty'][$key],
            'price' => $special_price,
          ];
        }
      }
      $is_with_product = count($data_products) > 0;
      if ($is_with_product) {
        // $order->products()->sync($data_products);
        foreach ($data_products as $id => $product) {
          OrderProduct::create([
            'order_id' => $order->id,
            'product_id' => $id,
            'qty' => $product['qty'],
            'price' => $product['price'],
          ]);
        }
        // $order->setAdditionalCost();
        $order->countTotal();
        Deposit::create([
          'order_id' => $order->id,
          'customer_id' => $order->customer_id,
          'amount' => ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) ? ($order->amount_deposit_used * -1) : 0,
          'balance' => $order->deposit_balance_after,
          'note' => 'ADD JOB',
        ]);
        if ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) {
          $order->customer->setDeposit($order->deposit_balance_after);
        }
      }

      // JOB ORDER
    } else {

      $is_secondfloor = isset($data['is_secondfloor']) ? $data['is_secondfloor'] : 0;
      if ($data['oldnew'] == 'new') {
        $customer = Customer::where('phone', $helper->convertPhone($data['customer_phone']))->first();
        if ($customer) {
          return back()->withErrors(['customer_phone' => 'No. WhatsApp sudah terdaftar di ' . $customer->name . ' (' . $customer->phone . ')']);
        } else {
          $customer_id = Customer::create([
            'name' => $data['customer_name'],
            'phone' => $helper->convertPhone($data['customer_phone']),
            'payment' => isset($data['payment']) && !empty($data['payment']) ? $data['payment'] : null,
            'note' => isset($data['note']) && !empty($data['note']) ? $data['note'] : null,
          ])->id;
          $address_id = Address::create([
            'store_id' => $store->id,
            'customer_id' => $customer_id,
            'address' => $data['customer_address'],
            'is_secondfloor' => $is_secondfloor,
            'latlng' => isset($data['customer_latlng']) && !empty($data['customer_latlng']) ? $data['customer_latlng'] : null,
          ])->id;
        }
      } else {
        if (isset($data['is_add_another_address']) && $data['is_add_another_address']) {
          $address = Address::find($data['address_id']);
          $customer_id = $address->customer_id;
          $address_id = Address::create([
            'store_id' => $store->id,
            'customer_id' => $customer_id,
            'address' => $data['another_address'],
            'is_secondfloor' => isset($data['another_address_is_secondfloor']) ? $data['another_address_is_secondfloor'] : 0,
            'latlng' => isset($data['another_address_latlng']) && !empty($data['another_address_latlng']) ? $data['another_address_latlng'] : null,
          ])->id;
        } else {
          $address = Address::find($data['address_id']);
          $address->update([
            'is_secondfloor' => $is_secondfloor,
          ]);
          $customer_id = $address->customer_id;
          $address_id = $address->id;
        }
      }
      // if (date('H') < 8 && date('H') > 6) {
      //     $created_at->setTime(8, 0, 0);
      // }
      $options = null;
      if (isset($data['is_hide_deposit']) && !empty($data['is_hide_deposit'])) {
        $options['is_hide_deposit'] = true;
      }
      $order = Order::create([
        'store_id' => $store->id,
        'customer_id' => $customer_id,
        'address_id' => $address_id,
        'status_id' => 1,
        'payment' => isset($data['payment']) && !empty($data['payment']) ? $data['payment'] : null,
        'note' => isset($data['note']) && !empty($data['note']) ? $data['note'] : null,
        'note_for_driver' => isset($data['note_for_driver']) && !empty($data['note_for_driver']) ? $data['note_for_driver'] : null,
        'receiver_phone' => isset($data['receiver_phone']) && !empty($data['receiver_phone']) ? $helper->convertPhone($data['receiver_phone']) : null,
        'created_at' => $created_at,
        'is_urgent' => isset($data['is_urgent']) && !empty($data['is_urgent']) ? $data['is_urgent'] : 0,
        'options' => $options,
      ]);
      $data_products = [];
      foreach ($data["product_id"] as $key => $product_id) {
        if ($product_id) {
          $productstore = ProductStore::where('store_id', $store->id)
            ->where('product_id', $product_id)
            ->first();
          $customerproduct = CustomerProduct::where('customer_id', $customer_id)
            ->where('product_id', $product_id)
            ->first();
          $local_price = $productstore ? ($productstore->local_price ? $productstore->local_price : $productstore->product->price) : 0;
          $special_price = $customerproduct && $customerproduct->local_price ? $customerproduct->local_price : $local_price;
          $data_products[$product_id] = [
            'qty' => $data['qty'][$key],
            'price' => $special_price,
          ];
        }
      }

      $customer = Customer::find($customer_id);

      // Job Product
      $is_with_product = count($data_products) > 0;
      if ($is_with_product) {
        // $order->products()->sync($data_products);
        foreach ($data_products as $id => $product) {
          OrderProduct::create([
            'order_id' => $order->id,
            'product_id' => $id,
            'qty' => $product['qty'],
            'price' => $product['price'],
          ]);
        }
        $order->setAdditionalCost();
        $order->countTotal();
        Deposit::create([
          'order_id' => $order->id,
          'customer_id' => $order->customer_id,
          'amount' => ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) ? ($order->amount_deposit_used * -1) : 0,
          'balance' => $order->deposit_balance_after,
          'note' => 'ADD JOB',
        ]);
        if ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) {
          $order->customer->setDeposit($order->deposit_balance_after);
        }

        // $helper->generateDepositHistory($order->customer_id, $order->created_at);

        // Send Notification by WhatsApp
        // -------------------------------------
        // if ($order->store->is_notif_wa) {
        //   $close_hour_array = explode(":", $order->store->close_hour);
        //   $close_hour = new DateTime();
        //   $close_hour->setTime((int) $close_hour_array[0], (int) $close_hour_array[1]);
        //   $is_deliver_tomorrow = $created_at > $close_hour;

        //   $messages = [];
        //   $msg_order_new = $order->store->msg_order_new ? $order->store->msg_order_new : 'Mohon menunggu. Pesanan sedang kami proses.';
        //   // Admin Toko
        //   // if ($order->store->whatsapp_2) {
        //   //   array_push($messages, [
        //   //     'order_id'      => $order->id,
        //   //     'to' => $helper->convertPhone($order->store->whatsapp_2),
        //   //     'message' => '📥 *New Job* by CS ' . config('app.url') . '/manager/job-list/' . $order->store->slug . '?date=' . date_format($order->created_at, 'Y-m-d') . '&status=total&search=' . $order->code . ' ' . $order->customer->name,
        //   //   ]);
        //   // }
        //   // Customer
        //   if ($order->customer->notif_status === 'on') {
        //     $msg_order_info = $helper->createOrderMessage($order);
        //     $msg_to_customer = $msg_order_info ? $msg_order_new . '<br><br>' . $msg_order_info : $msg_order_new;
        //     if ($is_deliver_tomorrow) {
        //       $msg_to_customer = '*Maaf, saat ini toko sudah tutup 🙏. Pesanan akan dikirim BESOK*.<br><br>' . $msg_to_customer;
        //     }
        //     array_push($messages, [
        //       'order_id'      => $order->id,
        //       'to' => $helper->convertPhone($order->receiver_phone ? $order->receiver_phone : $customer->phone),
        //       'message' => $msg_to_customer,
        //       // 'footer' => 'Tidak ingin menerima pesan ini? Klik link di bawah:',
        //       // 'button_links' => [
        //       //   [
        //       //       'index' => 1,
        //       //       'urlButton' => [
        //       //           'displayText' => '🛑 Berhenti menerima pesan dari kami!',
        //       //           'url' => 'https://cs.ordergasplus.online'
        //       //       ]
        //       //   ]
        //       // ]
        //     ]);
        //   }
        //   $helper->queueSendNotif($messages);
        // }
      }

      // Job Deposit
      if (isset($data['deposit']) && !empty($data['deposit'])) {
        $order->update([
          'deposit_job' => str_replace(".", "", $data['deposit']),
          'status_id' => $is_with_product ? 1 : 4,
        ]);
      }
    }

    // ? Accurate create Invoice
    if (!$is_marketing) {
      $helper->accurateUpsertInvoice($order, true);
    }

    $query_url = [
      'store_slug' => $store->slug,
      // 'status' => 'total',
    ];

    if ($data['oldnew'] === 'konsumsitoko') {
      $query_url['totalkonsumsitoko'] = $order->total;
    } else if ($data['oldnew'] === 'marketing') {
      // $query_url['status'] = 'diambil';
      $query_url['type'] = 'marketing';
    } else {
      $query_url['status'] = 'total';
    }

    $is_send_notif = true;
    if ($is_konsumsitoko || $data['oldnew'] === 'marketing') {
      $is_send_notif = false;
    }
    if ($is_asdt && !$order->receiver_phone) {
      $is_send_notif = false;
    }
    // if (!$data["is_send_notif"]) {
    //   $is_send_notif = false;
    // }
    if ($is_send_notif) {
      $close_hour_array = explode(":", $order->store->close_hour);
      $close_hour = new DateTime();
      $close_hour->setTime((int) $close_hour_array[0], (int) $close_hour_array[1]);
      $is_deliver_tomorrow = $created_at > $close_hour;
      $msg_order_new = $order->store->msg_order_new ? $order->store->msg_order_new : 'Mohon menunggu. Pesanan sedang kami proses.';
      $msg_to_customer = $helper->createOrderMessage($order, true, $is_asdt);
      if (!$is_asdt) {
        $msg_to_customer = $msg_order_new . '<br><br>' . $msg_to_customer;
      }
      if ($is_deliver_tomorrow) {
        $msg_to_customer = '*Maaf, saat ini toko sudah tutup 🙏. Pesanan akan dikirim BESOK*.<br><br>' . $msg_to_customer;
      }


      // ? Get conversation chat
      $helperSocialchat = new HelperSocialchat();
      $conversationId = null;
      $phone = $order->receiver_phone ?? $order->customer->phone;
      $socialchat = Socialchat::where("store_id", $order->store_id)
        ->where('customer_id', $order->customer_id)
        ->where('phone', $phone)
        ->orderBy('id', 'desc')
        ->first();
      if ($socialchat) {
        $conversationId = $socialchat->conversation_id;
      } else {
        $conversationId = $helperSocialchat->getConversationId($order->store, $phone);
        if ($conversationId) {
          Socialchat::create([
            'store_id' => $order->store_id,
            'customer_id' => $order->customer_id,
            'phone' => $phone,
            'conversation_id' => $conversationId,
          ]);
        }
      }

      // return [
      //   'data' => $data,
      //   'order' => $order,
      //   'conversationId' => $conversationId,
      //   'phone' => $phone,
      //   'socialchat' => $socialchat,
      // ];

      // ? Try send notif via socialchat
      $isSuccesSendNotifViaSocialchat = false;
      if ($conversationId) {
        $order->customer->socialchat_conversation_id = $conversationId;
        $order->customer->save();
        $msg_to_customer = str_replace(PHP_EOL, '', $msg_to_customer);
        $msg_to_customer = str_replace('<br>', PHP_EOL, $msg_to_customer);
        $isSuccesSendNotifViaSocialchat = $helperSocialchat->sendMessage($conversationId, $msg_to_customer);
        $query_url['alert'] = 'notif-sent';
        $query_url['alertmsg'] = 'Job ' . $order->code . ' berhasil terkirim.';
      }

      // ? Send notif manual via wa.me
      if (!$conversationId || !$isSuccesSendNotifViaSocialchat) {
        $query_url['alert'] = 'notif-confirm';
        $query_url['msg'] = urlencode($msg_to_customer);
        $query_url['to'] = $helper->convertPhone($order->receiver_phone ? $order->receiver_phone : $customer->phone);
      }
    }

    if (!$is_asdt_konsumsitoko && $data['oldnew'] !== 'marketing') {
      $customer->options = array_merge((array) $customer->options, [
        'last_order_at' => Carbon::now()->toDateTimeString(),
        'last_order_or_notified_at' => Carbon::now()->toDateTimeString(),
      ]);
      $customer->save();
    }

    Cache::forget('calendar_orders_' . $store->slug . '_' . $created_at->format('Y-m-d') . '_' . auth()->user()->id);
    Cache::forget('jobs_order_bebas_' . $store->slug . '_' . $created_at->format('Y-m-d'));
    Cache::forget('jobs_order_' . $store->slug . '_' . $created_at->format('Y-m-d') . '_' . auth()->user()->id);
    Cache::forget('jobs_drivers_' . $store->slug . '_' . $created_at->format('Y-m-d'));
    Cache::forget('jobs_drivers_plus_' . $store->slug . '_' . $created_at->format('Y-m-d'));
    Cache::forget('jobs_ttl_month_' . $store->slug . '_' . $created_at->format('Y-m-d'));
    Cache::forget('jobs_ttl_today_' . $store->slug . '_' . $created_at->format('Y-m-d'));

    if ($data['oldnew'] == 'konsumsitoko') {
      return redirect()->route('operatingcost-add', $query_url)->with('success', 'TAMBAH JOB BERHASIL');
    } else {
      return redirect()->route('job-list', $query_url)->with('success', 'TAMBAH JOB BERHASIL');
    }
  }

  public function report(Request $request, $store_slug)
  {
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    if ((int)auth()->user()->role_id <= 2) {
      $stores = Store::all();
    } else {
      $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
    }

    return view(
      'manager.report',
      compact(
        'store_slug',
        'stores'
      )
    );
  }

  public function reportdriver(Request $request, $store_slug)
  {
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    $store = Store::where('slug', $store_slug)->first();

    $month_now = date('Y-m');

    $month_now = $request->query('month') ? $request->query('month') : $month_now;
    $month_prev = date('Y-m', strtotime($month_now . " -1 month"));
    $month_next = date('Y-m') == $month_now ? null : date('Y-m', strtotime($month_now . " +1 month"));
    $future_order = Order::where('store_id', $store->id)
      ->whereDate('created_at', '>=', date('Y-m-d'))
      ->orderByDesc('created_at')
      ->first();
    $date_current = date('Y-m') == $month_now ? ($future_order ? $future_order->created_at : date('Y-m-d')) : date("Y-m-t", strtotime($month_now));

    $begin = new DateTime($month_now);
    $begin->modify('first day of this month');
    $end = new DateTime($date_current);
    if (date('Y-m') != $month_now || !$future_order) {
      $end = $end->modify('+1 day');
    }

    $interval = new DateInterval('P1D');
    $daterange = new DatePeriod($begin, $interval, $end);

    $hari = array(1 => "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu", "Minggu");
    $data = [];
    $sum = [];
    $sum_overtime = [];
    $drivers = User::withTrashed()
      ->whereIn('role_id', [5, 6])
      ->where(function ($q) use ($store, $begin, $end) {
        $q->where('store_id', $store->id)
          ->orWhereHas(
            'orders',
            function ($sub) use ($store, $begin, $end) {
              $sub->where('store_id', $store->id)
                ->whereNull('deleted_at')
                ->whereIn('status_id', [4, 6, 7])
                ->whereBetween('created_at', [$begin, $end]);
            }
          );
      })
      ->get();

    // $totals = Total::where('totalable_type', 'App\Models\User')
    //     ->where('store_id', $store->id)
    //     ->whereBetween('date', [$begin->format("Y-m-d"), $end->format("Y-m-d")])
    //     ->get();

    foreach ($daterange as $date) {

      $driver_total_delivery = [];
      $day_total_delivery = 0;
      $day_total_delivery_overtime = 0;
      foreach ($drivers as $key => $driver) {
        // $total = $totals->where('date', $date->format("Y-m-d"))
        //     ->where('totalable_id', $driver->id)
        //     ->whereNull('type')
        //     ->first();

        // if (!$total) {
        // $total_delivery = Order::where('store_id', $store->id)
        //     ->where('driver_id', $driver->id)
        //     ->whereIn('status_id', [4, 6, 7])
        //     ->whereDate('created_at', $date->format("Y-m-d"))
        //     ->count();

        // $total_delivery = DB::table('orders')
        //     ->where('store_id', $store->id)
        //     ->where('driver_id', $driver->id)
        //     ->whereIn('status_id', [4, 6, 7])
        //     ->whereDate('created_at', $date->format("Y-m-d"))
        //     ->selectRaw("count(*) as count")
        //     ->first();

        // $total = Total::create([
        //     'totalable_type' => 'App\Models\User',
        //     'totalable_id' => $driver->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        //     'total' => $total_delivery,
        // ]);

        // $total = Total::updateOrCreate([
        //     'totalable_type' => 'App\Models\User',
        //     'totalable_id' => $driver->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        // ], [
        //     'total' => $total_delivery,
        // ]);

        // }

        // $total_overtime = $totals->where('date', $date->format("Y-m-d"))
        //     ->where('totalable_id', $driver->id)
        //     ->where('type', 'overtime')
        //     ->first();

        // if (!$total_overtime) {
        // $total_delivery_overtime = Order::where('store_id', $store->id)
        //     ->where(function (Builder $query) {
        //         $query->where('duration', '>', 5400)
        //         ->orWhere('duration', '<', -5400);
        //     })
        //     ->where('driver_id', $driver->id)
        //     ->whereIn('status_id', [4, 6, 7])
        //     ->whereDate('created_at', $date->format("Y-m-d"))
        //     ->count();

        // $total_delivery_overtime = DB::table('orders')
        //     ->where('store_id', $store->id)
        //     ->where(function ($query) {
        //         $query->where('duration', '>', 5400)
        //             ->orWhere('duration', '<', -5400);
        //     })
        //     ->where('driver_id', $driver->id)
        //     ->whereIn('status_id', [4, 6, 7])
        //     ->whereDate('created_at', $date->format("Y-m-d"))
        //     ->selectRaw('count(*) as count')
        //     ->first();

        // $total_overtime = Total::create([
        //     'totalable_type' => 'App\Models\User',
        //     'totalable_id' => $driver->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        //     'type' => 'overtime',
        //     'total' => $total_delivery_overtime,
        // ]);

        // $total_overtime = Total::updateOrCreate([
        //     'totalable_type' => 'App\Models\User',
        //     'totalable_id' => $driver->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        //     'type' => 'overtime',
        // ], [
        //     'total' => $total_delivery_overtime,
        // ]);
        // }

        $orders = DB::table('orders')
          ->whereNull('deleted_at')
          ->where('store_id', $store->id)
          ->where('driver_id', $driver->id)
          ->whereIn('status_id', [4, 6, 7])
          ->whereDate('created_at', $date->format("Y-m-d"))
          ->selectRaw('count(*) as count_delivered')
          ->selectRaw("count(case when duration > 5400 or duration < -5400 then 1 end) as count_delivered_overtime")
          ->first();

        // $total = $total ? $total->total : 0;
        // $total_overtime = $total_overtime ? $total_overtime->total : 0;

        $total = $orders->count_delivered ? $orders->count_delivered : 0;
        $total_overtime = $orders->count_delivered_overtime ? $orders->count_delivered_overtime : 0;

        $driver_total_delivery[$driver->id] = [
          'total' => $total,
          'total_overtime' => $total_overtime,
        ];
        $day_total_delivery += $total;
        $day_total_delivery_overtime += $total_overtime;

        $sum[$driver->id] = isset($sum[$driver->id]) ? $sum[$driver->id] + $total : $total;
        $sum_overtime[$driver->id] = isset($sum_overtime[$driver->id]) ? $sum_overtime[$driver->id] + $total_overtime : $total_overtime;
      }

      array_push($data, [
        'datetime' => $date->format("Y-m-d"),
        'date' => $date->format("j"),
        'day' => $hari[$date->format("N")],
        'driver_total_delivery' => $driver_total_delivery,
        'day_total_delivery' => $day_total_delivery,
        'day_total_delivery_overtime' => $day_total_delivery_overtime,
      ]);
    }

    $data = array_reverse($data);

    $drivers->map(function ($driver) use ($sum, $sum_overtime) {
      $driver->total = isset($sum[$driver->id]) ? $sum[$driver->id] : 0;
      $driver->total_overtime = isset($sum_overtime[$driver->id]) ? $sum_overtime[$driver->id] : 0;
      return $driver;
    });

    $sum_total_all = 0;
    $sum_total_all_overtime = 0;
    foreach ($drivers as $driver) {
      $sum_total_all += $driver->total;
      $sum_total_all_overtime += $driver->total_overtime;
    }

    if ((int)auth()->user()->role_id <= 2) {
      $stores = Store::all();
    } else {
      $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
    }

    // $sum_total_all = Total::where('totalable_type', 'App\Models\User')
    //     ->whereIn('totalable_id', $drivers->pluck('id')->toArray())
    //     // ->where('store_id', $store->id)
    //     ->whereBetween('date', [$begin->format("Y-m-d"), $end->format("Y-m-d")])
    //     ->sum('total');

    return view(
      'manager.report-driver',
      compact(
        'store_slug',
        'stores',
        'drivers',
        'month_now',
        'month_prev',
        'month_next',
        'data',
        'sum_total_all',
        'sum_total_all_overtime',
      )
    );
  }

  public function reportlt2list(Request $request, $store_slug)
  {
    //? Import
    $helper = new Helper();

    //? Validate Store Slug
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    $drivers = User::whereIn('role_id', [5, 6])
      ->with(['store'])
      // ->orWhereHas(
      //   'orders',
      //   function ($q) {
      //     $q->whereNull('deleted_at')
      //       ->whereIn('status_id', [4, 6, 7])
      //       ->whereBetween('created_at', [$begin, $end]);
      //   }
      // )
      ->orderBy('store_id', 'desc')
      ->get()
      // ->groupBy('store_id');
      // ->map(function($store) {
      //   $store->test = 'aoijwef';
      //   return $store;
      // });
      ->groupBy(function ($item) {
        return $item->store ? $item->store->name : 'Kosong';
      });

    return view(
      'manager.report-lt2-list',
      compact(
        'store_slug',
        'drivers',
      )
    );
  }

  public function reportlt2insentive(Request $request, $store_slug, $ids)
  {

    //? Import
    $helper = new Helper();

    //? Validate Store Slug
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    //? Get Store
    $store = Store::where('slug', $store_slug)->first();

    //? Process Date
    $month_now = date('Y-m');

    $month_now = $request->query('month') ? $request->query('month') : $month_now;
    $month_prev = date('Y-m', strtotime($month_now . " -1 month"));
    $month_next = date('Y-m') == $month_now ? null : date('Y-m', strtotime($month_now . " +1 month"));
    $future_order = Order::where('store_id', $store->id)
      ->whereDate('created_at', '>=', date('Y-m-d'))
      ->orderByDesc('created_at')
      ->first();
    $date_current = date('Y-m') == $month_now ? ($future_order ? $future_order->created_at : date('Y-m-d')) : date("Y-m-t", strtotime($month_now));

    $begin = new DateTime($month_now);
    $begin->modify('first day of this month');
    $end = new DateTime($date_current);
    $date_min = $begin->format("Y-m-d");
    $date_max = $end->format("Y-m-d");

    if ($request->query('datestart')) {
      $begin = new DateTime($request->query('datestart'));
    }
    if ($request->query('dateend')) {
      $end = new DateTime($request->query('dateend'));
    }
    // if (date('Y-m') != $month_now || !$future_order) {
    //   $end = $end->modify('+1 day');
    // }
    $date_start = $begin->format("Y-m-d");
    $date_end = $end->format("Y-m-d");
    $end->modify('+1 day');

    //? Get Categories
    $categories = Category::where('insentive', '>', 0)->get();

    //? Vars
    $data = [];
    $sum_all_delivered = 0;
    $sum_all_insentive = 0;
    $sum_all_delivered_overtime = 0;
    $sum_additionalcost_qty = [];
    $sum_additionalcost_insentive = [];
    $driver_ids = explode('-', $ids);

    //? Get Stores List
    $stores = Store::all();

    //? Loop Store
    foreach ($stores as $store) {
      // $store = Store::find($store_id);
      $store_insentives = $store->categorystores;
      $sum_store_delivered = 0;
      $sum_store_insentive = 0;
      $driver_list = [];
      $drivers = User::withTrashed()
        ->whereIn('role_id', [5, 6])
        ->whereIn('id', $driver_ids)
        ->whereHas(
          'orders',
          function ($q) use ($store, $begin, $end) {
            $q
              ->where('store_id', $store->id)
              ->whereNull('deleted_at')
              ->whereIn('status_id', [4, 6, 7])
              ->whereBetween('created_at', [$begin, $end])
              ->where(function ($qsub) {
                $qsub->whereHas(
                  'orderproducts',
                  function ($sub) {
                    $sub
                      ->where('product_id', '154');
                  }
                )
                  ->orWhereHas(
                    'additionalcosts',
                    function ($sub) {
                      $sub
                        ->where('name', 'Biaya antar lantai atas');
                    }
                  );
              });
          }
        )
        ->orderBy('store_id')
        ->get();

      //? Loop Drivers
      foreach ($drivers as $driver) {
        $sum_driver_qty = 0;
        $sum_driver_insentive = 0;
        $driver_additionalcost = [];

        //? Loop Categories inside Driver
        // foreach ($categories as $category) {
        $sum_qty = Order::whereNull('deleted_at')
          ->where('store_id', $store->id)
          ->where('driver_id', $driver->id)
          ->whereIn('status_id', [4, 6, 7])
          ->whereBetween('created_at', [$begin, $end])
          ->where(function ($qsub) {
            $qsub->whereHas(
              'orderproducts',
              function ($sub) {
                $sub
                  ->where('product_id', '154');
              }
            )
              ->orWhereHas(
                'additionalcosts',
                function ($sub) {
                  $sub
                    ->where('name', 'Biaya antar lantai atas');
                }
              );
          })
          ->count();

        $insentive = AdditionalCost::where('name', 'Biaya antar lantai atas')
          ->whereHas('order', function ($q) use ($driver, $store, $begin, $end) {
            $q
              ->whereNull('deleted_at')
              ->where('store_id', $store->id)
              ->where('driver_id', $driver->id)
              ->whereIn('status_id', [4, 6, 7])
              ->whereBetween('created_at', [$begin, $end]);
          })
          ->sum('total_cost');

        $qty_by_productlt2 = OrderProduct::whereHas('order', function ($q) use ($driver, $store, $begin, $end) {
          $q
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->where('driver_id', $driver->id)
            ->whereIn('status_id', [4, 6, 7])
            ->whereBetween('created_at', [$begin, $end])
            ->whereHas(
              'orderproducts',
              function ($sub) {
                $sub
                  ->where('product_id', '154');
              }
            );
        })
          // ->whereHas(
          //   'product',
          //   function ($sub) {
          //     $sub->whereHas(
          //       'categories',
          //       function ($subsub) {
          //         $subsub->whereIn('slug', ['lpg', 'galon']);
          //       });
          //   })
          ->sum('qty');

        $insentive_by_productlt2 = (ceil($qty_by_productlt2 / 2) * 3000);

        $insentive = $insentive + $insentive_by_productlt2;

        // $store_insentive = $store_insentives->firstWhere('category_id', $category->id);
        // $insentive = $store_insentive && $store_insentive->insentive ? $store_insentive->insentive : $category->insentive;
        // $insentive_result = round(($sum_qty * $insentive)/1000);

        array_push($driver_additionalcost, [
          'name' => 'Lantai 2',
          'qty' => $sum_qty,
          'insentive' => $insentive,
        ]);

        if (!array_key_exists(0, $sum_additionalcost_qty)) {
          $sum_additionalcost_qty[0] = 0;
        }
        $sum_additionalcost_qty[0] += $sum_qty;
        if (!array_key_exists(0, $sum_additionalcost_insentive)) {
          $sum_additionalcost_insentive[0] = 0;
        }
        $sum_additionalcost_insentive[0] += $insentive;

        $sum_driver_qty += $sum_qty;
        $sum_driver_insentive += $insentive;
        // }
        //? END - Loop Categories inside Driver

        //? Push Driver Categories
        $driver->sum_qty = $sum_driver_qty;
        $driver->sum_insentive = $sum_driver_insentive;
        $driver->additionalcosts = $driver_additionalcost;

        //? Sum Driver Job & Overtime
        $orders = DB::table('orders')
          ->distinct()
          ->whereNull('deleted_at')
          ->where('store_id', $store->id)
          ->where('driver_id', $driver->id)
          ->whereIn('status_id', [4, 6, 7])
          ->whereBetween('created_at', [$begin, $end])
          // ->where(function ($qsub) {
          //   $qsub->whereHas(
          //     'orderproducts',
          //     function ($sub) {
          //       $sub
          //         ->where('product_id', '154');
          //     }
          //   )
          //     ->orWhereHas(
          //       'additionalcosts',
          //       function ($sub) {
          //         $sub
          //           ->where('name', 'Biaya antar lantai atas');
          //       }
          //     );
          // })
          ->selectRaw('count(*) as count_delivered')
          ->selectRaw("count(case when duration > 5400 or duration < -5400 then 1 end) as count_delivered_overtime")
          ->first();
        $driver->count_delivered = $orders->count_delivered;
        $driver->count_delivered_overtime = $orders->count_delivered_overtime;

        $sum_store_delivered += $orders->count_delivered;
        $sum_store_insentive += $sum_driver_insentive;

        $sum_all_delivered += $orders->count_delivered;
        $sum_all_delivered_overtime += $orders->count_delivered_overtime;
        $sum_all_insentive += $sum_driver_insentive;

        array_push($driver_list, $driver);
      }
      //? END - Loop Drivers

      array_push($data, [
        'store' => $store,
        'sum_store_delivered' => $sum_store_delivered,
        'sum_store_insentive' => $sum_store_insentive,
        'driver_list' => $driver_list,
      ]);
    }

    return view(
      'manager.report-lt2-insentive',
      compact(
        'store_slug',
        'month_now',
        'month_prev',
        'month_next',
        'date_min',
        'date_max',
        'date_start',
        'date_end',
        // 'categories',
        'data',
        'sum_additionalcost_qty',
        'sum_additionalcost_insentive',
        'sum_all_delivered',
        'sum_all_delivered_overtime',
        'sum_all_insentive',
        'ids'
      )
    );
  }

  public function reportdriverinsentivelist(Request $request, $store_slug)
  {
    //? Import
    $helper = new Helper();

    //? Validate Store Slug
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    $drivers = User::whereIn('role_id', [5, 6])
      ->with(['store'])
      ->orderBy('store_id', 'desc')
      ->get()
      // ->groupBy('store_id');
      // ->map(function($store) {
      //   $store->test = 'aoijwef';
      //   return $store;
      // });
      ->groupBy(function ($item) {
        return $item->store ? $item->store->name : 'Kosong';
      });

    return view(
      'manager.report-driver-insentive-list',
      compact(
        'store_slug',
        'drivers',
      )
    );
  }

  public function reportdriverinsentive(Request $request, $store_slug, $ids)
  {

    //? Import
    $helper = new Helper();

    //? Validate Store Slug
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    //? Get Store
    $store = Store::where('slug', $store_slug)->first();

    //? Process Date
    $month_now = date('Y-m');

    $month_now = $request->query('month') ? $request->query('month') : $month_now;
    $month_prev = date('Y-m', strtotime($month_now . " -1 month"));
    $month_next = date('Y-m') == $month_now ? null : date('Y-m', strtotime($month_now . " +1 month"));
    $future_order = Order::where('store_id', $store->id)
      ->whereDate('created_at', '>=', date('Y-m-d'))
      ->orderByDesc('created_at')
      ->first();
    $date_current = date('Y-m') == $month_now ? ($future_order ? $future_order->created_at : date('Y-m-d')) : date("Y-m-t", strtotime($month_now));

    $begin = new DateTime($month_now);
    $begin->modify('first day of this month');
    $end = new DateTime($date_current);
    $date_min = $begin->format("Y-m-d");
    $date_max = $end->format("Y-m-d");

    if ($request->query('datestart')) {
      $begin = new DateTime($request->query('datestart'));
    }
    if ($request->query('dateend')) {
      $end = new DateTime($request->query('dateend'));
    }
    // if (date('Y-m') != $month_now || !$future_order) {
    //   $end = $end->modify('+1 day');
    // }
    $date_start = $begin->format("Y-m-d");
    $date_end = $end->format("Y-m-d");
    $end->modify('+1 day');

    //? Get Categories
    $categories = Category::where('insentive', '>', 0)->get();

    //? Vars
    $data = [];
    $sum_all_delivered = 0;
    $sum_all_insentive = 0;
    $sum_all_delivered_overtime = 0;
    $sum_category_qty = [];
    $sum_category_insentive = [];
    $driver_ids = explode('-', $ids);

    //? Get Stores List
    $stores = Store::all();

    //? Loop Store
    foreach ($stores as $store) {
      // $store = Store::find($store_id);
      $store_insentives = $store->categorystores;
      $sum_store_delivered = 0;
      $sum_store_insentive = 0;
      $driver_list = [];
      $drivers = User::withTrashed()
        ->whereIn('role_id', [5, 6])
        ->whereIn('id', $driver_ids)
        ->whereHas(
          'orders',
          function ($q) use ($store, $begin, $end) {
            $q
              ->where('store_id', $store->id)
              ->whereNull('deleted_at')
              ->whereIn('status_id', [4, 6, 7])
              ->whereBetween('created_at', [$begin, $end]);
          }
        )
        ->orderBy('store_id')
        ->get();

      //? Loop Drivers
      foreach ($drivers as $driver) {
        $sum_driver_qty = 0;
        $sum_driver_insentive = 0;
        $sum_driver_insentive_gpsoff = 0;
        $driver_categories = [];

        //? Loop Categories inside Driver
        foreach ($categories as $category) {
          $sum_qty = OrderProduct::whereHas('order', function ($q) use ($driver, $store, $begin, $end, $store_insentives, $sum_category_qty, $sum_category_insentive) {
            $q
              ->whereNull('deleted_at')
              ->where('store_id', $store->id)
              ->where('driver_id', $driver->id)
              ->whereIn('status_id', [4, 6, 7])
              // ->whereNotNull('received_latlng')
              ->whereBetween('created_at', [$begin, $end]);
          })
            ->whereHas('product', function ($q) use ($category) {
              $q
                ->whereHas('categories', function ($sq) use ($category) {
                  $sq
                    ->where('id', $category->id);
                });
            })
            ->sum('qty');

          $sum_qty_gpsoff = OrderProduct::whereHas('order', function ($q) use ($driver, $store, $begin, $end, $store_insentives, $sum_category_qty, $sum_category_insentive) {
            $q
              ->whereNull('deleted_at')
              ->where('store_id', $store->id)
              ->where('driver_id', $driver->id)
              ->whereIn('status_id', [4, 6, 7])
              ->whereNull('received_latlng')
              ->whereBetween('created_at', [$begin, $end]);
          })
            ->whereHas('product', function ($q) use ($category) {
              $q
                ->whereHas('categories', function ($sq) use ($category) {
                  $sq
                    ->where('id', $category->id);
                });
            })
            ->sum('qty');

          $store_insentive = $store_insentives->firstWhere('category_id', $category->id);
          $insentive = $store_insentive && $store_insentive->insentive ? $store_insentive->insentive : $category->insentive;
          $insentive_result = $sum_qty > 0 && $insentive > 0 ? round(($sum_qty * $insentive) / 1000) : 0;
          $insentive_result_gpsoff = $sum_qty_gpsoff > 0 && $insentive > 0 ? round(($sum_qty_gpsoff * $insentive) / 1000) : 0;

          array_push($driver_categories, [
            'category_id' => $category->id,
            'qty' => $sum_qty,
            'qty_gpsoff' => $sum_qty_gpsoff,
            'insentive' => $insentive_result,
            'insentive_gpsoff' => $insentive_result_gpsoff,
          ]);

          if (!array_key_exists($category->id, $sum_category_qty)) {
            $sum_category_qty[$category->id] = 0;
          }
          $sum_category_qty[$category->id] += $sum_qty + $sum_qty_gpsoff;
          if (!array_key_exists($category->id, $sum_category_insentive)) {
            $sum_category_insentive[$category->id] = 0;
          }
          $sum_category_insentive[$category->id] += $insentive_result + $insentive_result_gpsoff;

          $sum_driver_qty += $sum_qty + $sum_qty_gpsoff;
          $sum_driver_insentive += $insentive_result;
          $sum_driver_insentive_gpsoff += $insentive_result_gpsoff;
        }
        //? END - Loop Categories inside Driver

        //? Push Driver Categories
        $driver->sum_qty = $sum_driver_qty;
        $driver->sum_insentive = $sum_driver_insentive;
        $driver->sum_insentive_gpsoff = $sum_driver_insentive_gpsoff;
        $driver->categories = $driver_categories;

        //? Sum Driver Job & Overtime
        $orders = DB::table('orders')
          ->distinct()
          ->whereNull('deleted_at')
          ->where('store_id', $store->id)
          ->where('driver_id', $driver->id)
          ->whereIn('status_id', [4, 6, 7])
          ->whereBetween('created_at', [$begin, $end])
          ->selectRaw('count(*) as count_delivered')
          ->selectRaw('count(case when received_latlng is null then 1 end) as count_delivered_gpsoff')
          ->selectRaw("count(case when duration > 5400 or duration < -5400 then 1 end) as count_delivered_overtime")
          ->selectRaw("count(case when (duration > 5400 or duration < -5400) and received_latlng is null then 1 end) as count_delivered_overtime_gpsoff")
          ->first();
        $driver->count_delivered = $orders->count_delivered;
        $driver->count_delivered_gpsoff = $orders->count_delivered_gpsoff;
        $driver->count_delivered_overtime = $orders->count_delivered_overtime;
        $driver->count_delivered_overtime_gpsoff = $orders->count_delivered_overtime_gpsoff;

        $sum_store_delivered += $orders->count_delivered;
        $sum_store_insentive += $sum_driver_insentive;

        $sum_all_delivered += $orders->count_delivered;
        $sum_all_delivered_overtime += $orders->count_delivered_overtime;
        $sum_all_insentive += $sum_driver_insentive;

        array_push($driver_list, $driver);
      }
      //? END - Loop Drivers

      array_push($data, [
        'store' => $store,
        'sum_store_delivered' => $sum_store_delivered,
        'sum_store_insentive' => $sum_store_insentive,
        'driver_list' => $driver_list,
      ]);
    }

    return view(
      'manager.report-driver-insentive',
      compact(
        'store_slug',
        'month_now',
        'month_prev',
        'month_next',
        'date_min',
        'date_max',
        'date_start',
        'date_end',
        'categories',
        'data',
        'sum_category_qty',
        'sum_category_insentive',
        'sum_all_delivered',
        'sum_all_delivered_overtime',
        'sum_all_insentive',
        'ids'
      )
    );
  }

  public function reportbbro(Request $request, $store_slug)
  {
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    $store = Store::where('slug', $store_slug)->first();

    $month_now = date('Y-m');

    $month_now = $request->query('month') ? $request->query('month') : $month_now;
    $month_prev = date('Y-m', strtotime($month_now . " -1 month"));
    $month_next = date('Y-m') == $month_now ? null : date('Y-m', strtotime($month_now . " +1 month"));
    $future_order = Order::where('store_id', $store->id)
      ->whereDate('created_at', '>=', date('Y-m-d'))
      ->orderByDesc('created_at')
      ->first();
    $date_current = date('Y-m') == $month_now ? ($future_order ? $future_order->created_at : date('Y-m-d')) : date("Y-m-t", strtotime($month_now));

    $begin = new DateTime($month_now);
    $begin->modify('first day of this month');
    $end = new DateTime($date_current);
    if (date('Y-m') != $month_now || !$future_order) {
      $end = $end->modify('+1 day');
    }

    $interval = new DateInterval('P1D');
    $daterange = new DatePeriod($begin, $interval, $end);

    $hari = array(1 => "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu", "Minggu");
    $data = [];
    $sum = [];
    $sum_overtime = [];
    $drivers = User::withTrashed()
      ->whereIn('role_id', [4, 5, 6])
      ->where(function ($q) use ($store, $begin, $end) {
        $q->where('store_id', $store->id)
          ->orWhereHas(
            'marketings',
            function ($sub) use ($store, $begin, $end) {
              $sub->where('store_id', $store->id)
                ->whereNull('deleted_at')
                ->whereNotNull('sequence_per_day')
                ->whereIn('status_id', [6])
                ->whereBetween('created_at', [$begin, $end]);
            }
          );
      })
      ->get();

    // $totals = Total::where('totalable_type', 'App\Models\User')
    //     ->where('store_id', $store->id)
    //     ->whereBetween('date', [$begin->format("Y-m-d"), $end->format("Y-m-d")])
    //     ->get();

    foreach ($daterange as $date) {

      $driver_total_delivery = [];
      $day_total_delivery = 0;
      $day_total_delivery_overtime = 0;
      foreach ($drivers as $key => $driver) {
        // $total = $totals->where('date', $date->format("Y-m-d"))
        //     ->where('totalable_id', $driver->id)
        //     ->whereNull('type')
        //     ->first();

        // if (!$total) {
        // $total_delivery = Order::where('store_id', $store->id)
        //     ->where('driver_id', $driver->id)
        //     ->whereIn('status_id', [4, 6, 7])
        //     ->whereDate('created_at', $date->format("Y-m-d"))
        //     ->count();

        // $total_delivery = DB::table('orders')
        //     ->where('store_id', $store->id)
        //     ->where('driver_id', $driver->id)
        //     ->whereIn('status_id', [4, 6, 7])
        //     ->whereDate('created_at', $date->format("Y-m-d"))
        //     ->selectRaw("count(*) as count")
        //     ->first();

        // $total = Total::create([
        //     'totalable_type' => 'App\Models\User',
        //     'totalable_id' => $driver->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        //     'total' => $total_delivery,
        // ]);

        // $total = Total::updateOrCreate([
        //     'totalable_type' => 'App\Models\User',
        //     'totalable_id' => $driver->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        // ], [
        //     'total' => $total_delivery,
        // ]);

        // }

        // $total_overtime = $totals->where('date', $date->format("Y-m-d"))
        //     ->where('totalable_id', $driver->id)
        //     ->where('type', 'overtime')
        //     ->first();

        // if (!$total_overtime) {
        // $total_delivery_overtime = Order::where('store_id', $store->id)
        //     ->where(function (Builder $query) {
        //         $query->where('duration', '>', 5400)
        //         ->orWhere('duration', '<', -5400);
        //     })
        //     ->where('driver_id', $driver->id)
        //     ->whereIn('status_id', [4, 6, 7])
        //     ->whereDate('created_at', $date->format("Y-m-d"))
        //     ->count();

        // $total_delivery_overtime = DB::table('orders')
        //     ->where('store_id', $store->id)
        //     ->where(function ($query) {
        //         $query->where('duration', '>', 5400)
        //             ->orWhere('duration', '<', -5400);
        //     })
        //     ->where('driver_id', $driver->id)
        //     ->whereIn('status_id', [4, 6, 7])
        //     ->whereDate('created_at', $date->format("Y-m-d"))
        //     ->selectRaw('count(*) as count')
        //     ->first();

        // $total_overtime = Total::create([
        //     'totalable_type' => 'App\Models\User',
        //     'totalable_id' => $driver->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        //     'type' => 'overtime',
        //     'total' => $total_delivery_overtime,
        // ]);

        // $total_overtime = Total::updateOrCreate([
        //     'totalable_type' => 'App\Models\User',
        //     'totalable_id' => $driver->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        //     'type' => 'overtime',
        // ], [
        //     'total' => $total_delivery_overtime,
        // ]);
        // }

        $marketings = DB::table('marketings')
          ->whereNull('deleted_at')
          ->whereNotNull('sequence_per_day')
          ->where('store_id', $store->id)
          ->where('driver_id', $driver->id)
          ->whereIn('status_id', [6])
          ->whereDate('created_at', $date->format("Y-m-d"))
          ->selectRaw('count(*) as count_delivered')
          // ->selectRaw("count(case when duration > 5400 or duration < -5400 then 1 end) as count_delivered_overtime")
          ->first();

        // $total = $total ? $total->total : 0;
        // $total_overtime = $total_overtime ? $total_overtime->total : 0;

        $total = $marketings->count_delivered ? $marketings->count_delivered : 0;
        // $total_overtime = $marketings->count_delivered_overtime ? $marketings->count_delivered_overtime : 0;

        $driver_total_delivery[$driver->id] = [
          'total' => $total,
          // 'total_overtime' => $total_overtime,
        ];
        $day_total_delivery += $total;
        // $day_total_delivery_overtime += $total_overtime;

        $sum[$driver->id] = isset($sum[$driver->id]) ? $sum[$driver->id] + $total : $total;
        // $sum_overtime[$driver->id] = isset($sum_overtime[$driver->id]) ? $sum_overtime[$driver->id] + $total_overtime : $total_overtime;
      }

      array_push($data, [
        'datetime' => $date->format("Y-m-d"),
        'date' => $date->format("j"),
        'day' => $hari[$date->format("N")],
        'driver_total_delivery' => $driver_total_delivery,
        'day_total_delivery' => $day_total_delivery,
        // 'day_total_delivery_overtime' => $day_total_delivery_overtime,
      ]);
    }

    $data = array_reverse($data);

    $drivers->map(function ($driver) use ($sum, $sum_overtime) {
      $driver->total = isset($sum[$driver->id]) ? $sum[$driver->id] : 0;
      $driver->total_overtime = isset($sum_overtime[$driver->id]) ? $sum_overtime[$driver->id] : 0;
      return $driver;
    });

    $sum_total_all = 0;
    $sum_total_all_overtime = 0;
    foreach ($drivers as $driver) {
      $sum_total_all += $driver->total;
      $sum_total_all_overtime += $driver->total_overtime;
    }

    if ((int)auth()->user()->role_id <= 2) {
      $stores = Store::all();
    } else {
      $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
    }

    // $sum_total_all = Total::where('totalable_type', 'App\Models\User')
    //     ->whereIn('totalable_id', $drivers->pluck('id')->toArray())
    //     // ->where('store_id', $store->id)
    //     ->whereBetween('date', [$begin->format("Y-m-d"), $end->format("Y-m-d")])
    //     ->sum('total');

    return view(
      'manager.report-bbro',
      compact(
        'store_slug',
        'stores',
        'drivers',
        'month_now',
        'month_prev',
        'month_next',
        'data',
        'sum_total_all',
        'sum_total_all_overtime',
      )
    );
  }

  public function reportproductselect(Request $request, $store_slug)
  {
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    $store = Store::where('slug', $store_slug)->first();
    $products = $store->products;

    if ((int)auth()->user()->role_id <= 2) {
      $stores = Store::all();
    } else {
      $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
    }

    return view(
      'manager.report-product-select',
      compact(
        'store_slug',
        'stores',
        'products',
      )
    );
  }

  public function reportproduct(Request $request, $store_slug, $ids)
  {
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('calendar', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'month' => $request->query('month'),
      ]);
    }

    $store = Store::where('slug', $store_slug)->first();

    $month_now = date('Y-m');

    $month_now = $request->query('month') ? $request->query('month') : $month_now;
    $month_prev = date('Y-m', strtotime($month_now . " -1 month"));
    $month_next = date('Y-m') == $month_now ? null : date('Y-m', strtotime($month_now . " +1 month"));
    $future_order = Order::where('store_id', $store->id)
      ->whereDate('created_at', '>=', date('Y-m-d'))
      ->orderByDesc('created_at')
      ->first();
    $date_current = date('Y-m') == $month_now ? ($future_order ? $future_order->created_at : date('Y-m-d')) : date("Y-m-t", strtotime($month_now));

    $begin = new DateTime($month_now);
    $begin->modify('first day of this month');
    $end = new DateTime($date_current);
    if (date('Y-m') != $month_now || !$future_order) {
      $end = $end->modify('+1 day');
    }

    $interval = new DateInterval('P1D');
    $daterange = new DatePeriod($begin, $interval, $end);

    $hari = array(1 => "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu", "Minggu");
    $data = [];
    $sum = [];
    $product_ids = explode('-', $ids);
    $products = $store->products()->whereIn('product_id', $product_ids)->get();

    // $totals = Total::where('totalable_type', 'App\Models\Product')
    //     ->where('store_id', $store->id)
    //     ->whereBetween('date', [$begin->format("Y-m-d"), $end->format("Y-m-d")])
    //     ->get();

    foreach ($daterange as $date) {

      $product_total_delivery = [];
      $day_total_delivery = 0;
      foreach ($products as $key => $product) {
        // $total = $totals->where('date', $date->format("Y-m-d"))
        //     ->where('totalable_id', $product->id)
        //     ->first();

        // if (!$total) {
        $total_delivery = DB::table('order_product')
          ->leftJoin('orders', 'orders.id', '=', 'order_product.order_id')
          ->where('order_product.product_id', $product->id)
          // ->whereHas('order', function ($query) use ($store, $date) {
          ->where('orders.store_id', $store->id)
          ->whereIn('orders.status_id', [4, 6, 7])
          ->whereDate('orders.created_at', $date->format("Y-m-d"))
          // })
          ->selectRaw("sum(order_product.qty) as total")
          ->first();
        // ->sum('qty');

        // $total = Total::create([
        //     'totalable_type' => 'App\Models\Product',
        //     'totalable_id' => $product->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        //     'total' => $total_delivery,
        // ]);
        // $total = Total::updateOrCreate([
        //     'totalable_type' => 'App\Models\Product',
        //     'totalable_id' => $product->id,
        //     'store_id' => $store->id,
        //     'date' => $date->format("Y-m-d"),
        // ], [
        //     'total' => $total_delivery->total,
        // ]);
        // }

        // $total = $total ? $total->total : 0;
        $total = $total_delivery->total ? $total_delivery->total : 0;

        $product_total_delivery[$product->id] = $total;
        $day_total_delivery += $total;

        $sum[$product->id] = isset($sum[$product->id]) ? $sum[$product->id] + $total : $total;
      }

      array_push($data, [
        'date' => $date->format("j"),
        'day' => $hari[$date->format("N")],
        'product_total_delivery' => $product_total_delivery,
        'day_total_delivery' => $day_total_delivery,
      ]);
    }

    $data = array_reverse($data);

    $products->map(function ($product) use ($sum) {
      $product->total = isset($sum[$product->id]) ? $sum[$product->id] : 0;
      return $product;
    });

    if ((int)auth()->user()->role_id <= 2) {
      $stores = Store::all();
    } else {
      $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
    }

    // $sum_total_all = DB::table('order_product')
    //     ->leftJoin('orders', 'orders.id', '=', 'order_product.order_id')
    //     ->whereIn('order_product.product_id', $product_ids)
    //     ->where('orders.store_id', $store->id)
    //     ->whereBetween('orders.created_at', [$begin->format("Y-m-d"), $end->format("Y-m-d")])
    //     ->selectRaw("sum(order_product.qty) as total")
    //     ->first();

    // $sum_total_all = $sum_total_all->total ? $sum_total_all->total : 0;

    return view(
      'manager.report-product',
      compact(
        'store_slug',
        'stores',
        'products',
        'month_now',
        'month_prev',
        'month_next',
        'data',
        // 'sum_total_all',
        'ids',
      )
    );
  }

  public function reportdeposit(Request $request, $store_slug)
  {
    $helper = new Helper();
    $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'report');
    if (!$validateStoreSlug['status']) {
      return redirect()->route('jobs', [
        'store_slug' => $validateStoreSlug['store_slug'],
        'date' => $request->query('date'),
      ]);
    }
    $store = Store::where('slug', $store_slug)->first();
    $store_id = $store->id;
    $date_now = date('Y-m-d');

    $date_now = $request->query('date') ? $request->query('date') : $date_now;
    $date_compare = date('Y-m-d', strtotime($date_now . " +1 day"));
    $date_prev = date('Y-m-d', strtotime($date_now . " -1 day"));
    // $future_order_count = Order::where('store_id', $store->id)->whereDate('created_at', '>=', $date_compare)->orderByDesc('created_at')->count();
    $datetime_current = new DateTime;
    $datetime_current->modify('-1 day');
    $datetime_now = new DateTime($date_now);
    $date_next = $datetime_current > $datetime_now ? date('Y-m-d', strtotime($date_now . " +1 day")) : null;

    $hari = array(1 => "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu", "Minggu");
    $day = $hari[date('N', strtotime($date_now))];

    // $customers = Customer::distinct()
    //     ->whereHas('orders', function (Builder $query) use ($store, $date_now) {
    //         $query->where('store_id', $store->id)
    //             ->whereDate('created_at', $date_now);
    //     })
    //     ->with(['orders'])
    //     ->get();

    // foreach ($customers as $customer) {
    //     $helper->generateDepositHistory($customer->id, $date_now);
    // }

    $deposits = Deposit::whereHas('order', function (Builder $query) use ($store, $date_now) {
      $query->where('store_id', $store->id)
        ->whereDate('created_at', $date_now);
    })
      ->where(function (Builder $query) {
        $query->where('balance', '!=', 0)
          ->orWhere('amount', '!=', 0);
      })
      ->get();

    $customers_plus = Customer::whereHas('addresses', function (Builder $query) use ($store) {
      $query->where('store_id', $store->id);
    })
      ->where(function (Builder $query) {
        $query->where('deposit_amount', '>', 0);
      })
      ->get();

    $customers_minus = Customer::whereHas('addresses', function (Builder $query) use ($store) {
      $query->where('store_id', $store->id);
    })
      ->where(function (Builder $query) {
        $query->where('deposit_amount', '<', 0);
      })
      ->get();

    if ((int)auth()->user()->role_id <= 2) {
      $stores = Store::all();
    } else {
      $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
    }

    return view(
      'manager.report-deposit',
      compact(
        'store_slug',
        'store_id',
        'stores',
        'date_now',
        'date_prev',
        'date_next',
        'day',
        'deposits',
        'customers_plus',
        'customers_minus',
      )
    );
  }
}
