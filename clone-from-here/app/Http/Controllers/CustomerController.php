<?php

namespace App\Http\Controllers;

// use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\PpobCustomerData;
use App\Http\Controllers\Controller;

class CustomerController extends Controller
{
    public function getCustomerPpobKey($customer_id, $product_id)
    {
        if (!$customer_id || !$product_id) {
            return response()->json([
                'message' => 'Customer ID and Product ID are required'
            ], 400);
        }
        $ppob_keys = PpobCustomerData::where('customer_id', $customer_id)
            ->where('product_id', $product_id)
            ->get();
        return response()->json($ppob_keys);
    }

    public function removeDuplicateCustomers()
    {
        $customers = Customer::with('lastorder')
            ->get()
            ->groupBy('phone');

        foreach ($customers as $phone => $customerGroup) {
            if ($customerGroup->count() > 1) {
                $latestCustomer = $customerGroup->sortByDesc(function ($customer) {
                    return $customer->lastorder ? $customer->lastorder->created_at : null;
                })->first();
                foreach ($customerGroup as $customer) {
                    if ($customer->id !== $latestCustomer->id) {
                        $customer->delete();
                    }
                }
            }
        }

        return response()->json([
            'message' => 'Duplicate customers removed successfully',
            // 'last_customer_id' => $customers->last()->last()->id ?? null,
            // 'next_url' => route('remove-duplicate-customers', ['page' => $page + 1])
        ]);
    }
}
