<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Store;

class InfosController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function payment(Request $request, $store_slug)
    {
        $store = Store::where('slug', $store_slug)->firstOrFail();
        $total = $request->query('total');
        $order_code = $request->query('order_code');
        // $data_payment = '';
        $payments = [];
        foreach ($store->banks as $bank) {
            if (strtolower($bank->bank_name) !== 'qris' && strtolower($bank->bank_name) !== 'deposit' && strtolower($bank->bank_name) !== 'potongan harga') {
                array_push($payments, $bank);
                // $data_payment .= '<div>';
                // $data_payment .= '<strong>'.$bank->bank_name.'</strong> 🏧<br>';
                // $data_payment .= '<strong style="color: blue;">'.$bank->account_number.'</strong><br>';
                // $data_payment .= 'a.n. '.$bank->holder_name.'<br>';
                // $data_payment .= '</div>';
            }
        }
        return view('info.payment', compact('payments', 'total', 'order_code'));
        // return $data_payment;
    }
}