<?php

namespace App\Http\Controllers;

use App\Models\Store;
use App\Helper\Helper;
use App\Models\Product;
use App\Models\Purchase;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;
use App\Models\AccurateTemporaryList;
use Illuminate\Support\Facades\Validator;

class PurchaseController extends Controller
{
    public function addpurchase(Request $request, $store_slug)
    {
        $helper = new Helper();
        $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'invoice-add');
        if (!$validateStoreSlug['status']) {
            return redirect()->route('invoice-add', [
                'store_slug' => $validateStoreSlug['store_slug']
            ]);
        }
        if ((int)auth()->user()->role_id <= 2) {
            $stores = Store::all();
        } else {
            $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
        }

        $store = Store::where('slug', $store_slug)->first();

        $vendors = AccurateTemporaryList::where('alias', '/accurate/api/vendor/list.do')->first() ?? new AccurateTemporaryList;

        $products = Product::distinct()
            ->with([
                'featurephoto',
            ])
            ->leftJoin('product_store', 'product_store.product_id', '=', 'products.id')
            ->where('product_store.store_id', $store->id)
            // ->where('product_store.is_available', 1)
            // ->where('product_store.stock', '>', 0)
            ->select(
                'products.*',
                'product_store.local_price',
                'product_store.stock',
                // 'product_store.is_available',
            )
            ->whereHas('categories', function ($q) {
                $q->where('slug', '!=', 'ppob');
            })
            ->orderBy('products.sort_order')
            ->get();

        foreach ($products as &$product) {
            $product->featurephoto_url = $product->featurephoto ? $product->featurephoto->thumbnail($width = 354, $height = 354, $filters = []) : null;
            $product->is_ppob = $product->is_ppob;
        }

        return view('manager.purchase-add', compact(
            'store_slug',
            'stores',
            'store',
            'vendors',
            'products',
        ));
    }

    public function postpurchase(Request $request, $store_id)
    {
        $data = $request->all();
        if (isset($data['inputs'])) {
            $data = $data['inputs'] = json_decode($data['inputs'], true);
        }
        $rules = [
            'store_id' => 'required',
            'transaction_date' => 'required',
            'payment' => 'required',
            'vendor_no' => 'required',
            'no_faktur' => ['required', Rule::unique('purchases', 'bill_number')->where(function ($query) use ($data) {
                return $query->where('vendor_acc_no', $data['vendor_no']);
            })],
            'products.*.product_id' => 'required',
            'products.*.qty' => 'required',
            'products.*.price' => 'required',
            // 'photoreceipts.*.photo' => 'required',
        ];
        $messages = [
            'store_id.required' => 'The store ID is required.',
            'transaction_date.required' => 'The transaction date is required.',
            'payment.required' => 'The payment method is required.',
            'vendor_no.required' => 'The vendor ID is required.',
            'no_faktur.required' => 'The invoice number (no faktur) is required.',
            'products.*.product_id.required' => 'Each product must have a product ID.',
            'products.*.qty.required' => 'Each product must have a quantity.',
            'products.*.price.required' => 'Each product must have a price.',
            // 'photoreceipts.*.photo.required' => 'Each receipt photo is required.',
        ];
        $validation = Validator::make($data, $rules, $messages);
        if ($validation->fails()) {
            return response()->json(['errors' => $validation->errors()->toArray()], 422);
        }

        $helper = new Helper();
        $store = Store::find($data['store_id']);

        // ? Create Purchase on Accurate
        $params = [];
        $body = [
            'vendorNo' => $data['vendor_no'],
            'billNumber' => $data['no_faktur'],
            'branchId' => $store->accurate_branch_id,
            // 'currencyCode' => 'IDR',
            'description' => $data['note'],
            'transDate' => date('d/m/Y', strtotime($data['transaction_date'])),
        ];
        $iteration = 0;
        foreach ($data['products'] as $product) {
            $the_product = Product::find($product['product_id']);
            $body['detailItem[' . $iteration . '].itemNo'] = $the_product->code;
            $body['detailItem[' . $iteration . '].unitPrice'] = $product['price'];
            // $body['detailItem['.$iteration.'].itemUnitName'] = 'Galon'; // * (NOT REQUIRED)
            $body['detailItem[' . $iteration . '].quantity'] = $product['qty'];
            $body['detailItem[' . $iteration . '].warehouseName'] = $store->accurate_warehouse_name; // * (NOT REQUIRED)
            // $body['detailItem['.$iteration.']._status'] = 'delete'; // * Only to delete item (NOT REQUIRED)
            $iteration++;
        }
        $result_create_purchase = $helper->fetchApiAccurate('/accurate/api/purchase-invoice/save.do', 'POST', $params, $body, false, false, true);
        if (!$result_create_purchase) {
            return response()->json(['message' => 'Error fetchApiAccurate'], 422);
        }
        if (isset($result_create_purchase->s) && !$result_create_purchase->s) {
            if (isset($result_create_purchase->d) && is_string($result_create_purchase->d)) {
                if (strpos($result_create_purchase->d, 'No Faktur') !== false) {
                    return response()->json(['errors' => [
                        'no_faktur' => [$result_create_purchase->d],
                    ]], 422);
                }
                return response()->json(['message' => $result_create_purchase], 422);
            }
            return response()->json(['message' => 'Error fetchApiAccurate'], 422);
        }

        // ? Create Purchase on Gasplus CS Database
        $purchase = Purchase::create([
            'store_id' => $data['store_id'],
            'transaction_date' => $data['transaction_date'],
            'payment' => $data['payment'],
            'vendor_acc_no' => $data['vendor_no'],
            'vendor_acc_name' => $data['vendor_name'],
            'bill_number' => $data['no_faktur'],
            'note' => $data['note'],
        ]);

        $productData = [];
        foreach ($data['products'] as $product) {
            $productData[$product['product_id']] = [
                'qty' => $product['qty'],
                'price' => $product['price'],
                'warehouse_acc_name' => $store->accurate_warehouse_name,
            ];
        }
        $purchase->products()->attach($productData);

        // ? Upload Photo to Gasplus CS
        for ($i = 1; $i <= (int)$request->input('photoscount'); $i++) {
            if ($request->hasFile('photoreceipts_' . $i)) {
                $photo = $request->file('photoreceipts_' . $i);
                $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'photo-purchase';
                if (!File::isDirectory($image_path)) {
                    File::makeDirectory($image_path, 0777, true, true);
                }
                $data_photo_purchase = $helper->saveImage(
                    'photo-purchase',
                    $purchase,
                    $photo,
                    1000,
                    'photoreceipts',
                    'App\Models\Purchase',
                    $i,
                );
                $purchase->photoreceipts()->create($data_photo_purchase);

                // ? Attach to Accurate
                if (is_object($result_create_purchase) && isset($result_create_purchase->s) && $result_create_purchase->s) {
                    $params = [];
                    $imageData = file_get_contents($photo->path());
                    $base64Image = base64_encode($imageData);
                    $photo_name = 'photo-purchase' . '-' . $purchase->id . '-' . microtime(true) . '-' . $i . '.' . 'jpeg';
                    $body = [
                        'description' => 'Nota ' . $i,
                        'contentBase64' => $base64Image,
                        'name' => $photo_name,
                        'transactionType' => 'PI',
                        'transactionId' => $result_create_purchase->r->id,
                    ];
                    $result_upload_attachment = $helper->fetchApiAccurate('/accurate/api/attachment/attach.do', 'POST', $params, $body, false, false, true);
                }
            }
        }

        // ? Store Accurate Purchase Data Morph
        $dataAccuratePurchase = [];
        $dataAccuratePurchase['accuratable_key'] = 'purchase-invoice';
        if (is_object($result_create_purchase) && isset($result_create_purchase->s) && $result_create_purchase->s) {
            $accurate_data = $result_create_purchase->r;
            $dataAccuratePurchase['error_message'] = null;
            $dataAccuratePurchase['synced_at'] = date('Y-m-d H:i:s');
            $dataAccuratePurchase['accurate_id'] = $accurate_data->id;
            $dataAccuratePurchase['accurate_no'] = $accurate_data->number;

            // // ? Upload Photo to Accurate
            // foreach ($purchase->photoreceipts as $index => $photo) {
            //     $params = [];
            //     $imageUrl = public_path('public/gasplus/' . $photo->file_name);
            //     $imageData = file_get_contents($imageUrl);
            //     $base64Image = base64_encode($imageData);
            //     $body = [
            //         'description' => 'Nota ' . ($index + 1),
            //         'contentBase64' => $base64Image,
            //         'name' => pathinfo($photo->file_name, PATHINFO_FILENAME) . '.' . pathinfo($photo->file_name, PATHINFO_EXTENSION),
            //         'transactionType' => 'PI',
            //         'transactionId' => $accurate_data->id,
            //     ];
            //     $result_upload_attachment = $helper->fetchApiAccurate('/accurate/api/attachment/attach.do', 'POST', $params, $body, false, false, true);
            // }

            //TODO: If update need to store id on ProductPurchase
        } else {
            $dataAccuratePurchase['error_message'] = 'Error fetchApiAccurate';
            if (is_string($result_create_purchase)) {
                $arrayError = explode('ERROR: ', $result_create_purchase);
                if (count($arrayError) > 1) {
                    $dataAccuratePurchase['error_message'] = $arrayError[1];
                }
            }
        }

        $purchase->accuratePurchase()->create($dataAccuratePurchase);

        // ? Return
        $query_url = [
            'store_slug' => $store->slug,
            'date' => $purchase->transaction_date,
            // 'status' => 'total',
        ];

        return response()->json([
            'url_redirect' => route('jobs', $query_url),
            // 'url_redirect' => route('report.stockopname.detail', $query_url),
            'status' => 'success',
            'message' => 'TAMBAH PURCHASE BERHASIL',
        ]);
    }
}
