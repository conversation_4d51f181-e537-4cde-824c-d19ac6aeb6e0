<?php

namespace App\Sharp;

use App\Models\Role;
use App\Models\Permission;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\SharpForm;

class RoleSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this->transform(
            Role::with(['permissions'])->findOrFail($id)
        );
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $user = $id ? Role::findOrFail($id) : new Role;
        $this->save($user, $data);
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Role::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this->addField(
            SharpFormTextField::make('title')
                ->setLabel('Nama *')
        )->addField(
            SharpFormSelectField::make(
                "permissions",
                Permission::orderBy("title")->get()->pluck("title", "id")->all()
            )
                ->setLabel("Permissions *")
                ->setMultiple()
                ->setDisplayAsList()
                // ->setInline()
                // ->setCreatable(true)
                // ->setCreateAttribute("name")
                // ->setMaxTagCount(4)
        );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column->withSingleField('title')
                ->withSingleField("permissions");
        });
    }
}
