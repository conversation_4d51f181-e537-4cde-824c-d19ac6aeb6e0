<?php

namespace App\Sharp;

use App\Models\Role;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;

class RoleSharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('title')
                ->setLabel('Nama')
                ->setSortable()
                // ->setHtml()
        )->addDataContainer(
            EntityListDataContainer::make("permissions")
                // ->setSortable()
                ->setLabel("Permissions")
                // ->setHtml()
        );
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("title", 3, 12)
            ->addColumn("permissions", 9, 12);
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('id', 'asc')
            ->setPaginated();
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $roles = Role::distinct();

        if ($params->sortedBy()) {
            $roles->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $roles->where('title', 'like', $word);
            }
        }

        return $this
            // ->setCustomTransformer("permissions", function ($value, $role) {
            //     return $role->permissions->title;
            // })
            ->setCustomTransformer("permissions", function ($permissions, $role) {
                return $role->permissions->map(function ($permission) {
                    // return $permission->title;
                    return (new LinkToEntity($permission->title, "permission"))
                        ->setTooltip("See related permission")
                        // ->setSearch($permission->title)
                        ->toFormOfInstance($permission)
                        ->render();
                })->implode(" | ");
                // });
            })
            ->transform($roles->with(["permissions"])->paginate(30));
    }
}
