<?php

namespace App\Sharp;

use App\Models\User;
use App\Models\Store;
use App\Models\Deposit;
use App\Models\Product;
use App\Models\Customer;
// use App\Models\Area;
use App\Helper\HelperIak;
use Code16\Sharp\Form\SharpForm;
use Code16\Sharp\Http\WithSharpContext;
use Code16\Sharp\Form\Layout\FormLayoutTab;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Code16\Sharp\Form\Fields\SharpFormNumberField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormGeolocationField;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\Fields\SharpFormAutocompleteField;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
// use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;
// use Code16\Sharp\Form\Eloquent\Uploads\Transformers\SharpUploadModelFormAttributeTransformer;

class CustomerSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        $model = Customer::with([
            'addresses',
            'addresses.area',
            'addresses.photo',
            'ppobcustomerdatas',
            'ppobcustomerdatas.product',
            'customerproducts',
            'delmanexcludes'
        ])->findOrFail($id);
        $model->ignore_notif_reminder = isset($model->options['ignore_notif_reminder']) ? $model->options['ignore_notif_reminder'] : false;
        return $this->setCustomTransformer("userstamp", function ($value, $user) {
            return [
                "created_by" => $user->creator ? $user->creator->name : 'System',
                "created_at" => $user->created_at ? $user->created_at->format('jMy(G:i)') : '-',
                "updated_by" => $user->editor ? $user->editor->name : 'System',
                "updated_at" => $user->updated_at ? $user->updated_at->format('jMy(G:i)') : '-',
            ];
        })
            // ->setCustomTransformer(
            //     "addresses.photo",
            //     new FormUploadModelTransformer()
            // )
            ->transform($model);
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $customer = $id ? Customer::findOrFail($id) : new Customer;
        $old_deposit_amount = $customer->deposit_amount;

        if (isset($data['phone'])) {
            $phone = $data['phone'];
            $phone = str_replace(' ', '', $phone);
            $phone = str_replace('-', '', $phone);
            $phone = str_replace('+', '', $phone);
            $phone = str_replace('(', '', $phone);
            $phone = str_replace(')', '', $phone);
            if (substr($phone, 0, 1) == '0') {
                $phone = '62' . substr($phone, 1);
            }
            $data['phone'] = $phone;
        }
        // if (isset($data['area'])) {
        //     $data['area_id'] = $data['area']['id'];
        // }
        if (isset($data['ppobcustomerdatas'])) {
            foreach ($data['ppobcustomerdatas'] as &$ppobcustomerdata) {
                $helper_iak = new HelperIak();
                $product = Product::findOrFail($ppobcustomerdata['product_id']);
                if ($product && $product->code === "PLN_TKN") {
                    $path = '/api/inquiry-pln';
                    $additional_sign = $ppobcustomerdata['key'];
                    $method = 'POST';
                    $params = [];
                    $body = [
                        'customer_id' => $ppobcustomerdata['key'],
                    ];
                    $result = $helper_iak->fetchApiIakPrepaid($path, $method, $params, $body, $additional_sign);
                    if ($result) {
                        $ppobcustomerdata['inquiry_data'] = $result;
                    }
                }
            }
            unset($ppobcustomerdata); // Unset reference to avoid potential issues
        }

        $data['options'] = [
            ...($customer->options ?? []),
            'ignore_notif_reminder' => $data['ignore_notif_reminder'] ? 1 : 0,
        ];

        $this->ignore([
            'userstamp',
            'product_list',
            'ignore_notif_reminder',
        ])->save($customer, $data);

        if ($this->context()->isUpdate()) {
            // $diff_deposit_balance = $data['deposit_amount'] - $old_deposit_amount;
            // if ($diff_deposit_balance != 0) {
            //     Deposit::create([
            //         'customer_id' => $customer->id,
            //         'amount' => $diff_deposit_balance,
            //         'balance' => $data['deposit_amount'],
            //         'note' => 'EDIT DEPOSIT (manual)',   
            //     ]);
            // }
        } else {
            if ($data['deposit_amount']) {
                Deposit::create([
                    'customer_id' => $customer->id,
                    'amount' => $data['deposit_amount'],
                    'balance' => $data['deposit_amount'],
                    'note' => 'INITIAL DEPOSIT (manual)',
                ]);
            }
        }
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Customer::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this->addField(
            SharpFormTextField::make('name')
                ->setLabel('Nama *')
        )->addField(
            SharpFormTextCustomField::make('phone')
                ->setLabel('No. WhatsApp *')
                ->setInputType('phone')
        )->addField(
            SharpFormCheckField::make("ignore_notif_reminder", "Ya, jangan kirim notifikasi reminder!")
                ->setLabel("❌ 📢 Tidak menerima notifikasi reminder?")
        )->addField(
            SharpFormCheckField::make("is_company", "Ya")
                ->setLabel("Sebagai Perusahaan?")
        )->addField(
            SharpFormTextField::make('cp_name')
                ->setLabel('Nama (CP)')
                ->addConditionalDisplay('is_company', true)
        )->addField(
            SharpFormTextCustomField::make('cp_phone')
                ->setLabel('No. WhatsApp (CP)')
                ->setInputType('phone')
                ->addConditionalDisplay('is_company', true)
        )->addField(
            SharpFormTextCustomField::make('email')
                ->setLabel('Email')
                ->setInputType('email')
        )->addField(
            SharpFormTextareaField::make('note_special')
                ->setLabel('Catatan Khusus')
        )->addField(
            SharpFormTextCustomField::make('deposit_amount')
                ->setLabel('Deposit')
                ->setInputType('money')
                ->setReadOnly($this->context()->isUpdate())
            // )->addField(
            //     SharpFormTextField::make('accurate_customer_code')
            //         ->setLabel('ID Accurate')
        )->addField(
            SharpFormSelectField::make(
                "notif_status",
                [
                    'unset' => 'Unset',
                    'on' => '✅ ON',
                    'off' => '🛑 OFF',
                ]
            )
                ->setLabel("Kirim notif ke WhatsApp?")
                ->setDisplayAsDropdown()
        )->addField(
            SharpFormListField::make("ppobcustomerdatas")
                ->setLabel("PPOB")
                ->setAddText("Tambah Data PPOB")
                ->setAddable()
                ->setRemovable()
                ->setItemIdAttribute("id")
                ->addItemField(
                    SharpFormAutocompleteField::make("product_id", "local")
                        ->setLabel("Product")
                        // ->setLabel("Kode | Nama Produk - Harga Master")
                        ->setLocalSearchKeys(["code", "name"])
                        ->setItemIdAttribute('id')
                        ->setListItemTemplatePath("/sharp/templates/product_list.vue")
                        ->setResultItemTemplatePath("/sharp/templates/product_result.vue")
                        ->setLocalValues(
                            Product::orderBy("sort_order")
                                ->whereHas('categories', function ($query) {
                                    $query->where('slug', 'ppob');
                                })
                                ->withTrashed()
                                ->get()
                        )
                )
                ->addItemField(
                    SharpFormTextField::make('key')
                        ->setLabel('Nomor ID / HP / Etc. *')
                )
        )->addField(
            SharpFormListField::make("addresses")
                ->setLabel("Alamat")
                ->setAddText("Tambah Alamat")
                ->setAddable()
                ->setRemovable()
                ->setItemIdAttribute("id")
                ->addItemField(
                    SharpFormTextField::make('label')
                        ->setLabel('Label *')
                        ->setHelpMessage('Contoh: Alamat Utama')
                )
                ->addItemField(
                    SharpFormTextareaField::make('address')
                        ->setLabel('Alamat Lengkap *')
                )
                ->addItemField(
                    SharpFormTextField::make('accurate_customer_code')
                        ->setLabel('ID Accurate')
                        ->setReadOnly(auth()->user()->role_id >= 3)
                )
                ->addItemField(
                    SharpFormGeolocationField::make("latlng")
                        ->setDisplayUnitDecimalDegrees()
                        ->setGeocoding()
                        ->setInitialPosition(-7.7921967, 110.3699972)
                        ->setZoomLevel(14)
                        ->setMapsProvider('osm')
                        ->setGeocodingProvider('osm')
                        // ->setApiKey(env("GMAPS_KEY", "AIzaSyB7Yvda9jQtLu_UmFUh6bBouhxquJfNwtc"))
                        ->setLabel("Koordinat")
                )
                ->addItemField(
                    SharpFormCheckField::make("is_secondfloor", "Ya (Tambah Biaya setiap ORDER)")
                        ->setLabel("Lantai atas?")
                )
                ->addItemField(
                    SharpFormTextCustomField::make("additionalcost_secondfloor")
                        ->setLabel("Custom biaya jasa antar lantai atas")
                        ->setHelpMessage("Jika kosong akan mengikuti harga dasar toko")
                        ->setInputType('money')
                        ->addConditionalDisplay('is_secondfloor', true)
                )
                ->addItemField(
                    SharpFormSelectField::make(
                        "store_id",
                        $this->storeOptions()
                    )
                        ->setLabel("Pelanggan Toko *")
                        ->setDisplayAsDropdown()
                )
                ->addItemField(
                    SharpFormAutocompleteField::make("area", "remote")
                        // ->setDataWrapper("data")
                        ->setItemIdAttribute('id')
                        ->setLabel('Perumahan')
                        ->setPlaceholder('Cari "nama" atau tekan "spasi" untuk melihat semua perumahan')
                        // ->setLocalSearchKeys(["address"])
                        // ->setListItemInlineTemplate("{{address}}")
                        // ->setResultItemInlineTemplate("{{address}}")
                        ->setListItemTemplatePath("/sharp/templates/area_list.vue")
                        ->setResultItemTemplatePath("/sharp/templates/area_result.vue")
                        ->setDynamicRemoteEndpoint("/area/store/{{store_id}}")
                        ->setSearchMinChars(1)
                    // ->setPlaceholder('Tekan "spasi" untuk refresh')
                    // ->setRemoteMethodPOST()
                )
                // ->addItemField(
                //     SharpFormTextField::make('area_id')
                //         ->setLabel('Perumahan')
                // )
                ->addItemField(
                    SharpFormCheckField::make("is_need_marketing", "Ya")
                        ->setLabel("Sekalian bagi brosur?")
                )
                ->addItemField(
                    SharpFormUploadField::make("photo")
                        ->setLabel("Foto Rumah")
                        ->setFileFilterImages()
                        ->shouldOptimizeImage()
                        // ->setCompactThumbnail()
                        // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                        ->setStorageDisk("public")
                        ->setStorageBasePath("img/address")
                    // ->setReadOnly(auth()->user()->role_id >= 3)
                )
        )->addField(
            SharpFormSelectField::make(
                "delmanexcludes",
                User::whereIn('role_id', [5])->pluck("name", "id")->toArray()
            )
                ->setLabel("Eksklude Delman *")
                ->setMultiple()
            // ->setDisplayAsList()
            // ->setInline()
            // ->setCreatable(true)
            // ->setCreateAttribute("name")
            // ->setMaxTagCount(4)
        )->addField(
            SharpFormHtmlField::make('userstamp')
                ->setTemplatePath("/sharp/templates/userstamp.vue")
        );

        // on Create
        if ($this->context()->isCreation()) {
            $this->addField(
                SharpFormSelectField::make(
                    "product_list",
                    Product::orderBy("sort_order")->get()->map(function ($product) {
                        return [
                            "id" => $product->id,
                            "label" => $product->code
                                . " - " . $product->name
                        ];
                    })->all()
                )
                    ->setMultiple()
                    ->setDisplayAsList()
                    ->allowSelectAll()
            );

            // on Update
        } else {
            $this->addField(
                SharpFormListField::make("customerproducts")
                    // ->setLabel("Produk")
                    // ->setAddText("Tambah Produk")
                    // ->setAddable()
                    // ->setRemovable()
                    ->setItemIdAttribute("id")
                    ->addItemField(
                        SharpFormAutocompleteField::make("product_id", "local")
                            ->setLabel("_")
                            // ->setLabel("Kode | Nama Produk - Harga Master")
                            ->setLocalSearchKeys(["code", "name"])
                            ->setItemIdAttribute('id')
                            ->setReadOnly()
                            ->setListItemTemplatePath("/sharp/templates/product_list.vue")
                            ->setResultItemTemplatePath("/sharp/templates/product_result.vue")
                            ->setLocalValues(
                                Product::orderBy("sort_order")->get()
                            )
                    )
                    // ->addItemField(
                    //     SharpFormHtmlField::make("product_info")
                    //         // ->setLabel("")
                    //         ->setInlineTemplate(
                    //             "<strong>{{ id }}</strong> Nama Produk <em>- Rp123.000</em>"
                    //         )
                    // )
                    ->addItemField(
                        SharpFormNumberField::make("stock")
                            ->setLabel("Stok Barang *")
                            ->setMin(1)
                            // ->setPlaceholder('999')
                            ->setShowControls(false)
                    )
                    ->addItemField(
                        SharpFormTextCustomField::make("local_price")
                            ->setLabel("Harga Toko")
                            // ->setPrepend("Rp")
                            ->setInputType('money')
                    )
                    ->addItemField(
                        SharpFormCheckField::make("is_available", "Ya")
                            ->setLabel("Tersedia?")
                    )
            );
        }
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addTab("Info Pelanggan", function (FormLayoutTab $tab) {
            $tab->addColumn(12, function (FormLayoutColumn $column) {
                $column->withSingleField('name')
                    ->withFields("phone|6", "email|6")
                    ->withSingleField("is_company")
                    ->withFields("cp_phone|6", "cp_name|6")
                    ->withFields("note_special")
                    ->withFields("deposit_amount")
                    ->withFields("notif_status")
                    ->withSingleField("addresses", function (FormLayoutColumn $listItem) {
                        $listItem->withFields("label|6", "store_id|6")
                            ->withFields("address|12")
                            ->withFields("is_secondfloor|6", "additionalcost_secondfloor|6")
                            // ->withFields("address|6", "photo|6")
                            ->withFields("latlng|6")
                            ->withSingleField("area")
                            ->withSingleField("is_need_marketing")
                            ->withSingleField("accurate_customer_code");
                    })
                    ->withFields("ignore_notif_reminder")
                ;
            })->addColumn(12, function (FormLayoutColumn $column) {
                $column->withSingleField('userstamp');
            });
        });

        // on Create
        if ($this->context()->isCreation()) {
            // $this->addTab("Produk", function (FormLayoutTab $tab) {
            //     $tab->addColumn(12, function (FormLayoutColumn $column) {
            //         $column->withSingleField("product_list");
            //     });
            // });

            // on Update
        } else {
            $this->addTab("Harga Khusus", function (FormLayoutTab $tab) {
                $tab->addColumn(12, function (FormLayoutColumn $column) {
                    $column->withSingleField("customerproducts", function (FormLayoutColumn $listItem) {
                        $listItem->withFields("product_id|5", "stock|2", "local_price|3", "is_available|2");
                    });
                });
            })
                ->addTab("Exclude Delman", function (FormLayoutTab $tab) {
                    $tab->addColumn(12, function (FormLayoutColumn $column) {
                        $column->withSingleField("delmanexcludes");
                    });
                })
                ->addTab("PPOB", function (FormLayoutTab $tab) {
                    $tab->addColumn(12, function (FormLayoutColumn $column) {
                        $column->withSingleField("ppobcustomerdatas", function (FormLayoutColumn $listItem) {
                            $listItem->withFields("product_id|6", "key|6");
                        });
                    });
                });
        }
    }

    protected function storeOptions()
    {
        $current_user = auth()->user();
        if ($current_user->role_id == 3) { // filter for Admin Toko and Driver
            return $current_user->stores->pluck("name", "id")->all();
        } else {
            return Store::pluck("name", "id")->all();
        }
    }
}
