<?php

namespace App\Sharp;

use App\Models\User;

class SettingPolicy
{
	/**
	 * @param User $user
	 * @return bool
	 */
	public function entity(User $user)
	{
		// return true;
		return $user->permissions->contains('title', 'setting_access');
	}

	/**
	 * @param User $user
	 * @param $model
	 * @return bool
	 */
	public function view(User $user, $model)
	{
		return $user->permissions->contains('title', 'setting_show');
	}

	/**
	 * @param User $user
	 * @param $model
	 * @return bool
	 */
	public function update(User $user, $model)
	{
		return $user->permissions->contains('title', 'setting_edit');
	}

	/**
	 * @param User $user
	 * @return bool
	 */
	public function create(User $user)
	{
		return $user->permissions->contains('title', 'setting_create');
	}

	/**
	 * @param User $user
	 * @param $model
	 * @return bool
	 */
	public function delete(User $user, $model)
	{
		return $user->permissions->contains('title', 'setting_delete');
	}
}