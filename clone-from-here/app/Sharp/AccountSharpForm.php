<?php

namespace App\Sharp;

use App\Models\User;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\SharpSingleForm;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;

class AccountSharpForm extends SharpSingleForm
{
    use WithSharpFormEloquentUpdater;

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormTextField::make('name')
                ->setLabel('Name *')
        )->addField(
            SharpFormTextCustomField::make('phone')
                ->setLabel('No. WhatsApp *')
                ->setInputType('phone')
        )->addField(
            SharpFormTextCustomField::make('email')
                ->setLabel('Email *')
                ->setInputType('email')
        )->addField(
            SharpFormTextField::make('password')
                ->setLabel('Password Baru')
                ->setInputTypePassword()
        )->addField(
            SharpFormCheckField::make("notification_is_active", "Aktif")
                ->setLabel("Notifikasi?")
        );
    }

    public function buildFormLayout(): void
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            return $column->withSingleField("name")
                ->withSingleField("phone")
                ->withSingleField("email")
                ->withSingleField("password")
                ->withSingleField("notification_is_active");
        });
    }

    protected function findSingle()
    {
        return $this->transform(User::findOrFail(auth()->id()));
    }

    protected function updateSingle(array $data)
    {
        if (isset($data['phone'])) {
            $phone = $data['phone'];
            $phone = str_replace(' ', '', $phone);
            $phone = str_replace('-', '', $phone);
            $phone = str_replace('+', '', $phone);
            $phone = str_replace('(', '', $phone);
            $phone = str_replace(')', '', $phone);
            if (substr($phone, 0, 1) == '0') {
                $phone = '62' . substr($phone, 1);
            }
            $data['phone'] = $phone;
        }
        $this->save(User::findOrFail(auth()->id()), $data);
    }
}