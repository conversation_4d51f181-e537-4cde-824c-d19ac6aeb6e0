<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;
use Illuminate\Validation\Rule;

class ArmadaSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 'employee_id'    => 'required',
            'licence_number'    => 'required',
            // 'stnk_valid_until'    => 'required',
            // 'stnkphoto'    => 'required',
            // 'frontphotos.*.file'    => 'required',
            // 'sidephotos.*.file'    => 'required',
            // 'backphotos.*.file'    => 'required',
        ];
    }
}