<?php

namespace App\Sharp;

use App\Models\Merger;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
// use Code16\Sharp\Utils\LinkToEntity;
// use Illuminate\Database\Eloquent\Builder;
// use App\Sharp\Filters\MergerDateCreatedFilter;
// use App\Sharp\Filters\MergerDateTypeFilter;
// use App\Sharp\Filters\MergerCoordinateFilter;
use App\Sharp\Filters\MergerStoreFilter;
// use App\Sharp\Commands\MergerDepositGenerateHistory;

class MergerSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('main')
                ->setLabel('Utama')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("members")
                // ->setSortable()
                ->setLabel("Member")
        )->addDataContainer(
            EntityListDataContainer::make("updated_at")
                ->setSortable()
                ->setLabel("Updated")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("main", 5, 12)
            ->addColumn("members", 5, 12)
            ->addColumn("updated_at", 2, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('updated_at', 'desc')
            ->addFilter("stores", MergerStoreFilter::class)
            // ->addEntityCommand("generate", MergerDepositGenerateHistory::class)
            ->setPaginated();
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $models = Merger::distinct();

        if ($params->sortedBy()) {
            $models->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%' . $words . '%';
            $models->whereHas('maincustomer', function ($subquery) use ($words) {
                $subquery->where('name', 'like', $words)
                    ->orWhere('phone', 'like', $words);
            })
                ->orWhereHas('members', function ($subquery) use ($words) {
                    $subquery->where('name', 'like', $words)
                        ->orWhere('phone', 'like', $words);
                })
                ->orWhere('note', 'like', $words);
        }

        if ($params->filterFor("stores")) {
            $ids = $params->filterFor("stores");
            $models->whereHas('maincustomer', function ($query) use ($ids) {
                $query->whereHas('addresses', function ($q) use ($ids) {
                    $q->whereIn('store_id', (array)$ids);
                });
            })
                ->orWhereHas('members', function ($query) use ($ids) {
                    $query->whereHas('addresses', function ($q) use ($ids) {
                        $q->whereIn('store_id', (array)$ids);
                    });
                });
        }

        return $this
            ->setCustomTransformer("main", function ($value, $model) {
                $data = [
                    '${name}' => $model->maincustomer->name,
                    '${phone}' => $model->maincustomer->phone,
                    '${address}' => $model->maincustomer->addresses[0]->address,
                    '${latlng}' => $model->maincustomer->addresses[0]->latlng,
                ];
                $text = '';
                $text .= '<div><strong>${name}</strong></div>';
                $text .= '<div><a href="https://wa.me/${phone}" target="_blank"><i class="fab fa-whatsapp mr-1"></i>${phone}</a></div>';
                $text .= '<div><i class="fas fa-home mr-1"></i>${address}</div>';
                $text .= '<div><a href="http://www.google.com/maps/place/${latlng}" target="_blank"><i class="fas fa-location-arrow mr-1"></i>Go to ${latlng}</a></div>';

                return strtr($text, $data);
            })
            ->setCustomTransformer("members", function ($value, $model) {
                return $model->members
                    ->where('is_main', 0)
                    ->map(function ($member) {
                        $data = [
                            '${name}' => $member->name,
                            '${phone}' => $member->phone,
                            '${address}' => $member->addresses[0]->address,
                            '${latlng}' => $member->addresses[0]->latlng,
                        ];
                        $text = '';
                        $text .= '<div><strong>${name}</strong></div>';
                        $text .= '<div><a href="https://wa.me/${phone}" target="_blank"><i class="fab fa-whatsapp mr-1"></i>${phone}</a></div>';
                        $text .= '<div><i class="fas fa-home mr-1"></i>${address}</div>';
                        $text .= '<div><a href="http://www.google.com/maps/place/${latlng}" target="_blank"><i class="fas fa-location-arrow mr-1"></i>Go to ${latlng}</a></div>';

                        return strtr($text, $data);
                    })->implode("<hr>");
            })
            ->setCustomTransformer("updated_at", function ($value, $model) {
                return $model->updated_at->format('jMy(G:i)');
            })
            ->transform($models->with(["members", 'members.addresses', 'maincustomer', 'maincustomer.addresses'])->paginate(30));
    }
}
