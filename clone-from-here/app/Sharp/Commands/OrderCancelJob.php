<?php

namespace App\Sharp\Commands;

use App\Models\Order;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
use Code16\Sharp\Exceptions\Form\SharpApplicativeException;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use App\Sharp\CustomFormFields\SharpFormTextLocationField;
use App\Sharp\CustomFormFields\SharpFormTextLocationAccuracyField;
use Illuminate\Database\Eloquent\Builder;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;

// use Code16\Sharp\Form\Fields\SharpFormTextareaField;

// use Illuminate\Support\Arr;

class OrderCancelJob extends InstanceCommand
{
    use WithSharpFormEloquentUpdater;

    public function label(): string
    {
        return "Batalkan Job";
    }

    public function description(): string
    {
        return "Order gagal dikirim.";
    }

    /**
     * @param $instanceId
     * @return array
     */
    protected function initialData($instanceId): array
    {
        return $this
            ->setCustomTransformer("order_details", function ($value, Order $order) {
                return [
                    'code' => $order->code,
                    'store' => $order->store->name,
                    'customer' => $order->customer->name,
                    'address' => $order->address->address,
                    'products' => $order->products,
                ];
            })
            ->setCustomTransformer("received_at", function ($value, Order $order) {
                return date("Y-m-d H:i");
            })
            ->transform(
                Order::findOrFail($instanceId)
            );
    }

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormHtmlField::make('order_details')
            ->setTemplatePath("/sharp/templates/order_details.vue")
        // )->addField(
        //     SharpFormTextLocationField::make("received_latlng")
        //         ->setLabel("Koordinat *")
        //         // ->setHide()
        // )->addField(
        //     SharpFormTextLocationAccuracyField::make("received_latlng_accuracy")
        //         ->setLabel("Akurasi Koordinat *")
        //         ->setHelpMessage('Dalam meter.')
        //         // ->setHide()
        // )->addField(
        //     SharpFormDateField::make("received_at")
        //             ->setLabel("Diterima Tanggal/Waktu")
        //             ->setDisplayFormat("YYYY-MM-DD HH:mm")
        //             ->setStepTime(1)
        //             ->setHasTime(true)
        //             ->setReadOnly()
        // )->addField(
        //     SharpFormTextField::make('received_by')
        //             ->setLabel('Diterima Oleh *')
        //             // ->setReadOnly()
        )->addField(
            SharpFormTextareaField::make('driver_note')
                    ->setLabel('Alasan Batal *')
                    ->setRowCount(2)
        // )->addField(
        //     SharpFormUploadField::make("receivephoto")
        //         ->setLabel("Foto Penerimaan *")
        //         ->setFileFilterImages()
        //         ->shouldOptimizeImage()
        //         // ->setCompactThumbnail()
        //         // ->setCropRatio("1:1", ["jpg","jpeg","png"])
        //         ->setStorageDisk("public")
        //         ->setStorageBasePath("img/order-received")
        );
    }

    public function execute($instanceId, array $data = []): array
    {
        $this->validate($data, [
            "driver_note" => "required",
        ]);

        if ($data["driver_note"] == "error") {
            throw new SharpApplicativeException("Alasan can't be «error»");
        }

        $order = Order::findOrFail($instanceId);

        $data['status_id'] = 5; // set to Job Selesai
        $this->ignore(['order_details'])->save($order, $data);


        return $this->reload();
    }

    // public function confirmationText(): string
    // {
    //     return "Antar order sekarang?";
    // }

    public function authorizeFor($instanceId): bool
    {
        $order = Order::find($instanceId);
        return in_array(auth()->user()->role_id, [5, 6]) && $order->status_id == 3; // only Driver && status Job Berjalan
    }
}
