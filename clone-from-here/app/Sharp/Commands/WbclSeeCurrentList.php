<?php

namespace App\Sharp\Commands;

use Carbon\Carbon;
use App\Models\Customer;
// use App\Models\Socialchat;
use App\Models\NotificationSchedule;
use Code16\Sharp\EntityList\Commands\InstanceCommand;

// use Code16\Sharp\Form\Fields\SharpFormTextareaField;

// use Illuminate\Support\Arr;

class WbclSeeCurrentList extends InstanceCommand
{
    public function label(): string
    {
        return "See 100 Sample List";
    }

    public function description(): string
    {
        return "Lihat sample 100 list pelanggan saat ini.";
    }

    // public function buildFormFields(): void
    // {
    //     $this->addField(
    //         SharpFormCheckField::make("is_start_now", "Ya")
    //                 ->setLabel("Kirim sekarang?")
    //     );
    // }

    public function execute($instanceId, array $data = []): array
    {
        // $this->validate($data, [
        //     "is_start_now" => "required"
        // ]);

        // if ($data["is_start_now"] == "error") {
        //     throw new SharpApplicativeException("Driver can't be «error»");
        // }

        $notif_schedule = NotificationSchedule::findOrFail($instanceId);
        $criteria = $notif_schedule->criteria;
        if ($criteria == 'test') {
            // ? Super Admin
            $customer = Customer::where('phone', '6281331885989')->orderBy('id', 'desc')->first();
            if ($customer->id) {
                $customers[] = $customer;
            }
            $customer = Customer::where('phone', '6285800156489')->orderBy('id', 'desc')->first();
            if ($customer) {
                $customers[] = $customer;
            }
            // ? Owner
            $customer = Customer::where('phone', '62811298904')->orderBy('id', 'desc')->first();
            if ($customer) {
                $customers[] = $customer;
            }
        } else {
            $month01 = Carbon::now();
            $month02 = Carbon::now();
            $month03 = Carbon::now();
            $month06 = Carbon::now();
            $month01->subDays(30);
            $month02->subDays(60);
            $month03->subDays(90);
            $month06->subDays(180);
            $data = Customer::distinct();
            if (isset($notif_schedule->others['store_ids'])) {
                $store_ids = collect($notif_schedule->others['store_ids'])->pluck('id')->toArray();
                $data->whereHas('addresses', function ($query) use ($store_ids) {
                    $query->whereIn('store_id', $store_ids);
                });
            }
            $data
                ->inRandomOrder()
                ->limit(100)
                ->whereNull('deleted_at')
                ->where(function ($query) {
                    $query->where('options->ignore_notif_reminder', '!=', true)
                        ->orWhereNull('options->ignore_notif_reminder')
                    ;
                });
            if ($criteria === 'gt1-lt2-month') {
                $customers = $data
                    ->whereNotNull('options->last_order_at')
                    ->whereDate('options->last_order_at', '<=', $month01->toDateString())
                    ->whereDate('options->last_order_at', '>', $month02->toDateString())
                    ->where(function ($q) use ($month01, $month02) {
                        $q->whereNull('options->last_notified_at')
                            ->orWhere(function ($sq) use ($month01, $month02) {
                                $sq->whereNotNull('options->last_notified_at')
                                    ->whereDate('options->last_notified_at', '<=', $month01->toDateString())
                                    ->whereDate('options->last_notified_at', '>', $month02->toDateString())
                                ;
                            })
                        ;
                    })
                    ->get();
            } else if ($criteria === 'gt2-lt3-month') {
                $customers = $data
                    ->whereNotNull('options->last_order_at')
                    ->whereDate('options->last_order_at', '<=', $month02->toDateString())
                    ->whereDate('options->last_order_at', '>', $month03->toDateString())
                    ->where(function ($q) use ($month02, $month03) {
                        $q->whereNull('options->last_notified_at')
                            ->orWhere(function ($sq) use ($month02, $month03) {
                                $sq->whereNotNull('options->last_notified_at')
                                    ->whereDate('options->last_notified_at', '<=', $month02->toDateString())
                                    ->whereDate('options->last_notified_at', '>', $month03->toDateString())
                                ;
                            })
                        ;
                    })
                    ->get();
            } else if ($criteria === 'gt3-lt6-month') {
                $customers = $data
                    ->whereNotNull('options->last_order_at')
                    ->whereDate('options->last_order_at', '<=', $month03->toDateString())
                    ->whereDate('options->last_order_at', '>', $month06->toDateString())
                    ->where(function ($q) use ($month03, $month06) {
                        $q->whereNull('options->last_notified_at')
                            ->orWhere(function ($sq) use ($month03, $month06) {
                                $sq->whereNotNull('options->last_notified_at')
                                    ->whereDate('options->last_notified_at', '<=', $month03->toDateString())
                                    ->whereDate('options->last_notified_at', '>', $month06->toDateString())
                                ;
                            })
                        ;
                    })
                    ->get();
            } else if ($criteria === 'gt6-month') {
                $customers = $data
                    ->whereNotNull('options->last_order_at')
                    ->whereDate('options->last_order_at', '<=', $month06->toDateString())
                    ->where(function ($q) use ($month06) {
                        $q->whereNull('options->last_notified_at')
                            ->orWhere(function ($sq) use ($month06) {
                                $sq->whereNotNull('options->last_notified_at')
                                    ->whereDate('options->last_notified_at', '<=', $month06->toDateString())
                                ;
                            })
                        ;
                    })
                    ->get();
            }
        }
        switch ($criteria) {
            case 'test':
                $notif_schedule->criteria_title = 'Test send notif to Super Admin & Owner';
                break;
            case 'gt1-lt2-month':
                $notif_schedule->criteria_title = '1-2 bulan tidak order';
                break;
            case 'gt2-lt3-month':
                $notif_schedule->criteria_title = '2-3 bulan tidak order';
                break;
            case 'gt3-lt6-month':
                $notif_schedule->criteria_title = '3-6 bulan tidak order';
                break;
            case 'gt6-month':
                $notif_schedule->criteria_title = '> 6 bulan tidak order';
                break;
        }
        // return $this->refresh($instanceId);
        return $this->view("sharp.wbcl-customers", compact(
            "notif_schedule",
            "customers",
        ));
    }

    // public function confirmationText(): string
    // {
    //     return "Activate?";
    // }

    public function authorizeFor($instanceId): bool
    {
        // $notif_schedule = NotificationSchedule::findOrFail($instanceId);
        // $is_active = $notif_schedule->is_active;
        // return sharp_user()->role_id <= 2 && $is_active == 0;
        return sharp_user()->role_id <= 2;
    }
}
