<?php

namespace App\Sharp\Commands;

use App\Helper\Helper;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\EntityList\Commands\InstanceCommand;

class DepositGenerateHistory extends InstanceCommand
{
    /**
     * @return string
     */
    public function label(): string
    {
        return "Rapikan Deposit History";
    }

    public function description(): string
    {
        return "Buat ulang dan rapikan deposit history";
    }

    // public function confirmationText()
    // {
    //     return "Buat ulang dan rapikan deposit history?";
    // }

    protected function initialData($instanceId): array
    {
        $month_start = strtotime('first day of this month', time());
        return [
            // "from_date" => date("Y") . '-01-01 00:00:00'
            "from_date" => date("Y-m-d H:i:s", $month_start)
        ];
    }

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormDateField::make("from_date")
                ->setLabel("Rapikan deposit dari tanggal?")
            // ->setDisplayFormat("HH:mm:ss")
            // ->setStepTime(1)
            // ->setHasDate(false)
            // ->setHasTime(true)
            // $this->addField(
            //     SharpFormTextareaField::make("message")
            //         ->setLabel("Message")
        );
    }

    public function execute($instanceId, array $data = []): array
    {
        $customer_id = $instanceId;
        $helper = new Helper;
        $helper->generateDepositHistory($customer_id, $data['from_date']);
        return $this->reload();
        // return $this->info("Deposit History berhasil dirapikan!");
    }
}
