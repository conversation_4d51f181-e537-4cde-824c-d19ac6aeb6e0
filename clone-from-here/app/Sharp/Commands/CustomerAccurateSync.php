<?php

namespace App\Sharp\Commands;

use App\Models\Address;
use App\Helper\Helper;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
// use Illuminate\Database\Eloquent\Builder;

class CustomerAccurateSync extends InstanceCommand
{

    /**
     * @return string
     */
    public function label(): string
    {
        return "Sync Data Accurate";
    }

    public function description(): string
    {
        return "Syncronize data pelanggan Gaplus dan Accurate.";
    }

    public function confirmationText()
    {
        return "Syncronize data pelanggan gaplus (yang memiliki ID Pelanggan Accurate) dan buat pelanggan baru di Accurate (yang tidak memiliki ID Pelanggan Accurate)?";
    }

    public function execute($instanceId, array $data = []): array
    {
        $helper = new Helper;
        $addresses = Address::where('customer_id', $instanceId)
            ->whereNull('deleted_at')
            ->get();
        foreach ($addresses as $address) {
            //? Sync Data Gasplus & Accurate
            if (!empty($address->accurate_customer_code) && empty($address->accurate_customer_id)) {
                $params = [
                    'customerNo' => $address->accurate_customer_code,
                ];
                $body = [];
                $result = $helper->fetchApiAccurate('/accurate/api/customer/detail.do', 'GET', $params, $body);
                if (!$result || (is_object($result) && !$result->s)) {
                    return $this->info('Sync gagal 🔴');
                }
                $address->accurate_customer_id = $result->d->id;
                $address->save();
                $helper->accurateUpsertCustomer($address);
            }
            //? Create New Customer on Accurate
            if (empty($address->accurate_customer_code)) {
                $helper->accurateUpsertCustomer($address, true, false);
            }
        }
        return $this->info('Sync berhasil 👍');
    }

    // public function confirmationText()
    // {
    //     return "Tunggu beberapa menit untuk membuat ulang dan merapikan deposit history";
    // }

    public function authorize(): bool
    {
        return sharp_user()->role_id <= 2;
    }
}
