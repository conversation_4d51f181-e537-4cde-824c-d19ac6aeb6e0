<?php

namespace App\Sharp\Commands;

use App\Models\Marketing;
use App\Models\Address;
use App\Models\Store;
use App\Models\Order;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CustomerMapPreview extends InstanceCommand
{

    public function label(): string
    {
        return "Map Pelanggan";
    }

    public function execute($instanceId, array $data = []): array
    {
        $month01 = Carbon::now();
        $month02 = Carbon::now();
        $month03 = Carbon::now();
        $month06 = Carbon::now();
        $month01->subDays(30);
        $month02->subDays(60);
        $month03->subDays(90);
        $month06->subDays(180);

        $store = Store::findOrFail($instanceId);
        $addresses = Address::where('store_id', $instanceId)
            ->with([
                'customer',
                // 'area', 
                // 'store',
            ])
            ->get()
            ->map(function ($address) use ($month01, $month02, $month03, $month06) {
                $last_order = Order::where('address_id', $address->id)
                    ->whereNull('deleted_at')
                    ->where(function ($q) {
                        $q->where('status_id', 4)
                            ->orWhere('status_id', 6)
                            ->orWhere('status_id', 7);
                    })
                    ->orderBy('created_at', 'desc')
                    ->first();
                $address->mm = null;
                if ($last_order) {
                    $lo = Carbon::createFromFormat('Y-m-d H:i:s', $last_order->created_at);

                    if ($lo->lessThanOrEqualTo($month06)) {
                        $address->mm = 6;
                    } elseif ($lo->lessThanOrEqualTo($month03)) {
                        $address->mm = 3;
                    } elseif ($lo->lessThanOrEqualTo($month02)) {
                        $address->mm = 2;
                    } elseif ($lo->lessThanOrEqualTo($month01)) {
                        $address->mm = 1;
                    }
                    $address->last_order = $last_order->created_at;
                }
                return $address;
            });

        $marketings = Marketing::where('store_id', $instanceId)
            ->whereNull('deleted_at')
            ->where('status_id', 6)
            ->whereDate('created_at', '>', $month01->toDateString())
            ->with([
                'driver',
                // 'area', 
                // 'store',
            ])
            ->get();

        $latest_order = DB::table('orders')
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->select('address_id', DB::raw('MAX(created_at) as last_order_created_at'))
            ->groupBy('address_id');

        $count = DB::table('addresses')
            // ->groupBy('customer_id')
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->joinSub($latest_order, 'latest_order', function ($join) {
                $join->on('addresses.id', '=', 'latest_order.address_id');
            })
            ->selectRaw('COUNT(*) as total')
            ->selectRaw("COUNT(CASE WHEN last_order_created_at > '" . $month01->toDateString() . "' THEN 1 END) as month0")
            ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '" . $month01->toDateString() . "' AND last_order_created_at > '" . $month02->toDateString() . "' THEN 1 END) as month1")
            ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '" . $month02->toDateString() . "' AND last_order_created_at > '" . $month03->toDateString() . "' THEN 1 END) as month2")
            ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '" . $month03->toDateString() . "' AND last_order_created_at > '" . $month06->toDateString() . "' THEN 1 END) as month3")
            ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '" . $month06->toDateString() . "' THEN 1 END) as month6")
            ->first();

        $count_bbro = DB::table('marketings')
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->where(function ($q) {
                $q->where('status_id', 6);
                // ->orWhere('status_id', 6)
                // ->orWhere('status_id', 7);
            })
            ->selectRaw("COUNT(CASE WHEN created_at > '" . $month01->toDateString() . "' THEN 1 END) as month0")
            ->first();



        return $this->view("sharp.customer-map", compact('addresses', 'marketings', 'store', 'count', 'count_bbro'));
    }
}
