<?php

namespace App\Sharp;

use App\Models\Status;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;

class StatusSharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('code')
                ->setLabel('Kode')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("name")
                ->setSortable()
                ->setLabel("Nama Status")
        );
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("code", 6, 12)
            ->addColumn("name", 6, 12);
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('code', 'asc')
            ->setPaginated();
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $statuses = Status::distinct();

        if ($params->sortedBy()) {
            $statuses->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $statuses->where('name', 'like', $word)
                    ->orWhere('code', 'like', $word);
            }
        }

        return $this
            ->setCustomTransformer("name", function ($value, $status) {
                return '<i class="'.$status->icon.' mr-1" style="color: '.$status->color.';"></i>' . $status->name;
            })
            ->transform($statuses->paginate(30));
    }
}
