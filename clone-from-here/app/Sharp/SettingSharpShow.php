<?php

namespace App\Sharp;

use App\Models\Setting;
// use Code16\Sharp\Show\Fields\SharpShowEntityListField;
use Code16\Sharp\Show\SharpSingleShow;
use Code16\Sharp\Show\Layout\ShowLayoutColumn;
use Code16\Sharp\Show\Layout\ShowLayoutSection;
use Code16\Sharp\Show\Fields\SharpShowTextField;
use Code16\Sharp\Show\Fields\SharpShowPictureField;
use Code16\Sharp\Utils\Transformers\Attributes\MarkdownAttributeTransformer;
use Code16\Sharp\Utils\Transformers\Attributes\Eloquent\SharpUploadModelThumbnailUrlTransformer;
// use Code16\Sharp\Utils\Links\LinkToForm;

class SettingSharpShow extends SharpSingleShow
{
	public function buildShowFields(): void
	{
		$this
			->addField(
				SharpShowTextField::make('notifremindermessage01')->setLabel('Message Notif Reminder 01')
			)
			->addField(
				SharpShowTextField::make('notifremindermessage02')->setLabel('Message Notif Reminder 02')
			)
			->addField(
				SharpShowTextField::make('notifremindermessage03')->setLabel('Message Notif Reminder 03')
			)
			->addField(
				SharpShowPictureField::make('notifreminderimage01')
			)
			->addField(
				SharpShowPictureField::make('notifreminderimage02')
			)
			->addField(
				SharpShowPictureField::make('notifreminderimage03')
			)
			->addField(
				SharpShowTextField::make('buttontest01')
			)
			->addField(
				SharpShowTextField::make('buttontest02')
			)
			->addField(
				SharpShowTextField::make('buttontest03')
			)
		;
	}

	// public function buildShowConfig(): void
	// {
	//     $this
	//         ->setBreadcrumbCustomLabelAttribute("name");
	//     // ->addInstanceCommand("message", SettingSendMessage::class)
	//     //     ->addInstanceCommand("preview", SettingPreview::class)
	//     //     ->addInstanceCommandSeparator()
	//     //     ->addInstanceCommand("external", SettingExternalLink::class)
	//     //     ->setEntityState("state", SettingEntityState::class);
	// }

	public function buildShowLayout(): void
	{
		$this->addSection('Notif Reminder', function (ShowLayoutSection $section) {
			$section->addColumn(7, function (ShowLayoutColumn $column) {
				$column
					->withSingleField('notifremindermessage01')
					->withSingleField('notifreminderimage01')
					->withSingleField('buttontest01')
					->withSingleField('notifremindermessage02')
					->withSingleField('notifreminderimage02')
					->withSingleField('buttontest02')
					->withSingleField('notifremindermessage03')
					->withSingleField('notifreminderimage03')
					->withSingleField('buttontest03')
				;
			});
		});
	}

	// public function buildShowConfig(): void
	// {
	//     $this
	//         ->addInstanceCommand("rename", SettingUpdateName::class)
	//         ->setEntityState("status", SettingStatusState::class);
	// }

	public function findSingle(): array
	{
		$setting = Setting::where('name', 'settings')->with([
			'notifreminderimage01',
			'notifreminderimage02',
			'notifreminderimage03',
		])->first();
		if (!$setting) {
			$setting = Setting::create([
				'name' => 'settings',
				'value' => [
					'notifremindermessage01' => '',
					'notifremindermessage02' => '',
					'notifremindermessage03' => '',
				],
			]);
		}
		// $setting = $setting->value;
		$setting->notifremindermessage01 = $setting->value['notifremindermessage01'] ?? null;
		$setting->notifremindermessage02 = $setting->value['notifremindermessage02'] ?? null;
		$setting->notifremindermessage03 = $setting->value['notifremindermessage03'] ?? null;
		return $this
			->setCustomTransformer("notifremindermessage01", new MarkdownAttributeTransformer())
			->setCustomTransformer("notifremindermessage02", new MarkdownAttributeTransformer())
			->setCustomTransformer("notifremindermessage03", new MarkdownAttributeTransformer())
			->setCustomTransformer("notifreminderimage01", new SharpUploadModelThumbnailUrlTransformer(140))
			->setCustomTransformer("notifreminderimage02", new SharpUploadModelThumbnailUrlTransformer(140))
			->setCustomTransformer("notifreminderimage03", new SharpUploadModelThumbnailUrlTransformer(140))
			->setCustomTransformer("buttontest01", function ($value, $setting) {
				return "<a target='_blank' href='/test-notif-reminder/template/t1'>Test Template 1 📲</a>";
			})
			->setCustomTransformer("buttontest02", function ($value, $setting) {
				return "<a target='_blank' href='/test-notif-reminder/template/t2'>Test Template 2 📲</a>";
			})
			->setCustomTransformer("buttontest03", function ($value, $setting) {
				return "<a target='_blank' href='/test-notif-reminder/template/t3'>Test Template 3 📲</a>";
			})
			->transform($setting);
	}
}
