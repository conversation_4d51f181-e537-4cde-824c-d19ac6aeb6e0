<?php

namespace App\Sharp;

use App\Models\Category;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;

class CategorySharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('name')
                ->setLabel('Nama Kota')
                ->setSortable()
                // ->setHtml()
        )->addDataContainer(
            EntityListDataContainer::make("slug")
              ->setSortable()
              ->setLabel("Slug")
        )->addDataContainer(
            EntityListDataContainer::make("insentive")
                ->setSortable()
                ->setLabel("Insentive")
        )->addDataContainer(
            EntityListDataContainer::make("products_count")
              ->setSortable()
              ->setLabel("Jumlah Produk")
        )->addDataContainer(
            EntityListDataContainer::make("sort_order")
              ->setSortable()
              ->setLabel("Urutan")
        );
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("name", 3, 12)
          ->addColumn("slug", 3, 12)
          ->addColumn("insentive", 2, 12)
          ->addColumn("products_count", 2, 12)
          ->addColumn("sort_order", 2, 12);
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('sort_order', 'asc')
            ->setPaginated()
            ->setReorderable(CategorySharpReorderHandler::class);
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $categories = Category::distinct();

        if ($params->sortedBy()) {
            $categories->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $categories->where('name', 'like', $word)
                  ->orWhere('slug', 'like', $word);
            }
        }

        return $this
          ->setCustomTransformer("insentive", function ($value, $model) {
              return $value ? 'Rp' . number_format($value, 0, '', '.') : '-';
          })
          ->transform($categories->withCount(["products"])->paginate(30));
    }
}
