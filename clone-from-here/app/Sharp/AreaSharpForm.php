<?php

namespace App\Sharp;

use App\Models\Area;
use App\Models\Store;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Http\WithSharpContext;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
// use Code16\Sharp\Form\Fields\SharpFormTextareaField;
// use Code16\Sharp\Form\Fields\SharpFormWysiwygField;
// use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormGeolocationField;
// use Code16\Sharp\Form\Fields\SharpFormListField;
// use Code16\Sharp\Form\Fields\SharpFormAutocompleteField;
// use Code16\Sharp\Form\Fields\SharpFormNumberField;
// use Code16\Sharp\Form\Fields\SharpFormMarkdownField;
// use Code16\Sharp\Form\Fields\SharpFormUploadField;
// use Code16\Sharp\Form\Fields\SharpFormAutocompleteListField;
// use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
// use Code16\Sharp\Form\Layout\FormLayoutTab;
use Code16\Sharp\Form\SharpForm;
use App\Sharp\CustomFormFields\SharpFormTextCoordinateField;
// use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;

class AreaSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        // return $this->transform(
        //     Area::findOrFail($id)
        // );
        return $this->setCustomTransformer("latlng_type", function ($value, $model) {
            return 'map';
        })
            ->setCustomTransformer("latlng_map", function ($value, $model) {
                return $model->latlng;
            })
            ->setCustomTransformer("latlng_coordinate", function ($value, $model) {
                return $model->latlng;
            })
            ->setCustomTransformer("userstamp", function ($value, $user) {
                return [
                    "created_by" => $user->creator ? $user->creator->name : 'System',
                    "created_at" => $user->created_at ? $user->created_at->format('jMy(G:i)') : '-',
                    "updated_by" => $user->editor ? $user->editor->name : 'System',
                    "updated_at" => $user->updated_at ? $user->updated_at->format('jMy(G:i)') : '-',
                ];
            })
            ->transform(
                Area::with(['store'])->findOrFail($id)
            );
    }

    public function create(): array
    {
        // return $this->transform(new Area([
        //     "latlng_type" => 'coordinate'
        // ]));
        return [
            "latlng_type" => 'coordinate'
        ];
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $area = $id ? Area::findOrFail($id) : new Area;
        if ($data['latlng_type'] === 'map') {
            $data['latlng'] = $data['latlng_map'];
        } else {
            $data['latlng'] = $data['latlng_coordinate'];
        }
        $area = $this->ignore([
            'userstamp',
            'latlng_type',
            'latlng_map',
            'latlng_coordinate',
        ])->save($area, $data);
        // return $this->notify('test')
        //     ->setDetail(json_encode($type))
        //     ->setLevelSuccess();
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Area::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this->addField(
            SharpFormTextField::make('name')
                ->setLabel('Nama *')
        )->addField(
            SharpFormSelectField::make(
                "store_id",
                Store::orderBy("sort_order")->pluck("name", "id")->toArray()
            )
                ->setDisplayAsDropdown()
                ->setClearable()
                ->setLabel("Toko *")
            // ->setCreatable(true)
            // ->setCreateAttribute("name")
            // ->setMaxTagCount(4)
        )->addField(
            SharpFormHtmlField::make('userstamp')
                ->setTemplatePath("/sharp/templates/userstamp.vue")
        )->addField(
            SharpFormSelectField::make(
                'latlng_type',
                [
                    'map' => "Map",
                    'coordinate' => "Copy Paste Coordinate",
                ]
            )

                ->setLabel('Tipe Input Koordinat *')
                ->setClearable()
                ->setDisplayAsList()
        )->addField(
            SharpFormGeolocationField::make("latlng_map")
                ->setDisplayUnitDecimalDegrees()
                ->setGeocoding()
                ->setInitialPosition(-7.7921967, 110.3699972)
                ->setZoomLevel(14)
                ->setMapsProvider('osm')
                ->setGeocodingProvider('osm')
                // ->setApiKey(env("GMAPS_KEY", "AIzaSyB7Yvda9jQtLu_UmFUh6bBouhxquJfNwtc"))
                ->setLabel("Koordinat *")
                ->addConditionalDisplay('latlng_type', ['map'])
        )->addField(
            SharpFormTextCoordinateField::make("latlng_coordinate")
                ->setLabel("Koordinat *")
                ->setHelpMessage('Contoh: https://goo.gl/maps/acyGGxZ32hWhTwRPA')
                ->addConditionalDisplay('latlng_type', ['coordinate'])
        );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column->withSingleField('name')
                ->withSingleField("latlng_type")
                ->withSingleField("latlng_map")
                ->withSingleField("latlng_coordinate")
                ->withSingleField("store_id");
            // ->withSingleField("notification_is_active")
            // ->withSingleField("subscriber_id");
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
    }
}
