<?php

namespace App\Sharp;

use App\Models\Merger;
use App\Models\Customer;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormAutocompleteField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
// use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\SharpForm;
use Code16\Sharp\Http\WithSharpContext;

class MergerSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this
            ->setCustomTransformer("main_customer_id", function ($value, $model) {
                return Customer::with(['addresses', 'addresses.store'])->where('id', $model->main_customer_id)->first();
            })
            ->setCustomTransformer("members", function ($value, $model) {
                return $model->members
                    // ->where('is_main', 0)
                    // ->filter(function($member) {
                    //     return $member->is_main;
                    // })
                    ->map(function ($member) use ($model) {
                        $member['customer_id'] = Customer::with(['addresses', 'addresses.store'])->where('id', $member->id)->first();
                        return $member;
                    });
            })
            ->setCustomTransformer("userstamp", function ($value, $model) {
                return [
                    "created_by" => $model->creator ? $model->creator->name : 'System',
                    "created_at" => $model->created_at ? $model->created_at->format('jMy(G:i)') : '-',
                    "updated_by" => $model->editor ? $model->editor->name : 'System',
                    "updated_at" => $model->updated_at ? $model->updated_at->format('jMy(G:i)') : '-',
                ];
            })
            ->transform(
                Merger::with(['members', 'members.addresses', 'members.addresses.store', 'maincustomer'])->findOrFail($id)
            );
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $model = $id ? Merger::findOrFail($id) : new Merger;
        $old_members = $model->members ? $model->members : null;
        if ($old_members) {
            foreach ($old_members as $key => $customer) {
                // if ($customer->id !== $model->main_customer_id) {
                $customer->update([
                    'merger_id' => null,
                    'is_main' => 1,
                ]);
                // }
            }
        }

        // if (isset($data['phone'])) {
        //     $phone = $data['phone'];
        //     $phone = str_replace(' ', '', $phone);
        //     $phone = str_replace('-', '', $phone);
        //     $phone = str_replace('+', '', $phone);
        //     $phone = str_replace('(', '', $phone);
        //     $phone = str_replace(')', '', $phone);
        //     if (substr($phone, 0, 1) == '0') {
        //         $phone = '62' . substr($phone, 1);
        //     }
        //     $data['phone'] = $phone;
        // }
        // if (isset($data['area'])) {
        //     $data['area_id'] = $data['area']['id'];
        // }

        $model = $this->ignore(['userstamp', 'members'])->save($model, $data);

        $model->maincustomer->update([
            'merger_id' => $model->id,
        ]);
        foreach ($data['members'] as $key => $member) {
            $customer = Customer::find($member['customer_id']);
            $customer->merger_id = $model->id;
            if ($customer->id !== $model->main_customer_id) {
                $customer->is_main = 0;
            }
            $customer->save();
        }

        // if ($this->context()->isUpdate()) {
        //     // $diff_deposit_balance = $data['deposit_amount'] - $old_deposit_amount;
        //     // if ($diff_deposit_balance != 0) {
        //     //     Deposit::create([
        //     //         'customer_id' => $model->id,
        //     //         'amount' => $diff_deposit_balance,
        //     //         'balance' => $data['deposit_amount'],
        //     //         'note' => 'EDIT DEPOSIT (manual)',   
        //     //     ]);
        //     // }
        // } else {
        //     if ($data['deposit_amount']) {
        //         Deposit::create([
        //             'customer_id' => $model->id,
        //             'amount' => $data['deposit_amount'],
        //             'balance' => $data['deposit_amount'],
        //             'note' => 'INITIAL DEPOSIT (manual)',   
        //         ]);
        //     }
        // }
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Customer::where('merger_id', $id)
            ->update([
                'merger_id' => null,
                'is_main' => 1,
            ]);
        Merger::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this
            ->addField(
                SharpFormAutocompleteField::make("main_customer_id", "remote")
                    // ->setDataWrapper("data")
                    ->setItemIdAttribute('id')
                    ->setLabel('Pelanggan Utama *')
                    ->setPlaceholder('Cari "nama" atau "nomor whatsapp"')
                    // ->setLocalSearchKeys(["address"])
                    // ->setListItemInlineTemplate("{{address}}")
                    // ->setResultItemInlineTemplate("{{address}}")
                    ->setListItemTemplatePath("/sharp/templates/customer_item.vue")
                    ->setResultItemTemplatePath("/sharp/templates/customer_item.vue")
                    ->setDynamicRemoteEndpoint("/search/customers")
                    ->setSearchMinChars(2)
                // ->setPlaceholder('Tekan "spasi" untuk refresh')
                // ->setRemoteMethodPOST()
            )
            ->addField(
                SharpFormTextareaField::make('note')
                    ->setLabel('Catatan')
            )
            ->addField(
                SharpFormListField::make("members")
                    ->setLabel("Member")
                    ->setAddText("Tambah Member")
                    ->setAddable()
                    ->setRemovable()
                    ->setItemIdAttribute("id")
                    ->addItemField(
                        SharpFormAutocompleteField::make("customer_id", "remote")
                            // ->setDataWrapper("data")
                            ->setItemIdAttribute('id')
                            ->setLabel('Pelanggan *')
                            ->setPlaceholder('Cari "nama" atau "nomor whatsapp"')
                            // ->setLocalSearchKeys(["address"])
                            // ->setListItemInlineTemplate("{{address}}")
                            // ->setResultItemInlineTemplate("{{address}}")
                            ->setListItemTemplatePath("/sharp/templates/customer_item.vue")
                            ->setResultItemTemplatePath("/sharp/templates/customer_item.vue")
                            ->setDynamicRemoteEndpoint("/search/customers")
                            ->setSearchMinChars(2)
                        // ->setPlaceholder('Tekan "spasi" untuk refresh')
                        // ->setRemoteMethodPOST()
                    )
            )
            ->addField(
                SharpFormHtmlField::make('userstamp')
                    ->setTemplatePath("/sharp/templates/userstamp.vue")
            );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('main_customer_id')
                ->withSingleField("members", function (FormLayoutColumn $listItem) {
                    $listItem
                        ->withSingleField("customer_id");
                })
                ->withFields("note");
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
    }
}
