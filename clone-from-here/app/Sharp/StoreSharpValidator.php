<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class StoreSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'city_id' => 'required',
            'area' => 'required',
            'name' => [
                'required',
                $this->context()->instanceId() ? 'unique:stores,name,'.$this->context()->instanceId() : 'unique:stores',
            ],
            // 'slug' => [
            //     'required',
            //     $this->context()->instanceId() ? 'unique:stores,slug,'.$this->context()->instanceId() : 'unique:stores',
            // ],
            'open_hour' => 'required',
            'close_hour' => 'required',
            'holiday_start' => 'required_with:holiday_end',
            'holiday_end' => 'required_with:holiday_start',
            'holiday_note' => 'required_with_all:holiday_start,holiday_end',
            'whatsapp_1' => 'required',
            // 'chat_server_url' => ['url', 'nullable'],
            // 'email' => 'sometimes|email',
            // 'featurephoto' => 'max:3000',
            // 'banks' => 'array',
            'banks.*.bank_name' => 'required',
            'banks.*.account_number' => 'required',
            'banks.*.holder_name' => 'required',
            'productstores.*.stock' => $this->context()->instanceId() ? 'required' : '',
        ];
    }
}