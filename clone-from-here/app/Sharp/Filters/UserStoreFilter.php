<?php

namespace App\Sharp\Filters;

use App\Models\Store;
use Code16\Sharp\EntityList\EntityListSelectFilter;

class UserStoreFilter implements EntityListSelectFilter
{
    public function values()
    {
        return Store::pluck("name", "id")
            ->all();
    }

    public function label()
    {
        return "Toko";
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }

    public function isSearchable(): bool
    {
        return true;
    }

    // public function searchKeys(): array
    // {
    //     return ["name"];
    // }

    // public function template(): string
    // {
    //     return "{{name}}";
    // }
}
