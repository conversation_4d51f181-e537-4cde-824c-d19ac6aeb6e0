<?php

namespace App\Sharp\Filters;

use Code16\Sharp\EntityList\EntityListSelectRequiredFilter;

class CustomerCoordinateFilter implements EntityListSelectRequiredFilter
{
    public function values()
    {
        return [
          'all' => 'Semua',
          'empty' => 'Kosong',
        ];
    }

    public function defaultValue()
    {
        return 'all';
    }

    public function label()
    {
        return "Koordinat";
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
