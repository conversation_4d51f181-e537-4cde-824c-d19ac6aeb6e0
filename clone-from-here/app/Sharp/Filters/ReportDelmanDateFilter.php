<?php

namespace App\Sharp\Filters;

use Carbon\Carbon;
use Code16\Sharp\EntityList\EntityListDateRangeRequiredFilter;

class ReportDelmanDateFilter implements EntityListDateRangeRequiredFilter
{
    public function label()
    {
        return "Tanggal";
    }

    /**
     * @return array
     *
     * awaited format:
     *
     *    [
     *       "start" => Carbon::yesterday(),
     *       "end" => Carbon::today(),
     *    ]
     *
     * @throws \Exception
     */
    public function defaultValue()
    {
        return [
            "start" => Carbon::now()->startOfWeek(),
            "end" => Carbon::now()->endOfWeek(),
        ];
    }

    public function dateFormat()
    {
        return "YYYY-MM-DD";
    }

    // public function isMondayFirst()
    // {
    //     return false;
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}