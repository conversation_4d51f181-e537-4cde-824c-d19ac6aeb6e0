<?php

namespace App\Sharp\Filters;

use App\Models\Category;
use Code16\Sharp\EntityList\EntityListSelectMultipleFilter;

class ProductCategoryFilter implements EntityListSelectMultipleFilter
{
    public function values()
    {
        return Category::pluck("name", "id")
            ->all();
    }

    public function label()
    {
        return "Kategori";
    }

    public function isSearchable(): bool
    {
        return true;
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
