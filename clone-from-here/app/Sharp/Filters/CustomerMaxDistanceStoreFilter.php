<?php

namespace App\Sharp\Filters;

// use App\Models\Store;
use Code16\Sharp\EntityList\EntityListSelectFilter;

class CustomerMaxDistanceStoreFilter implements EntityListSelectFilter
{
    public function values()
    {
        return [
            '3000' => '3km',
            '3500' => '3.5km',
            '4000' => '4km',
            '4500' => '4.5km',
            '5000' => '5km',
            '5500' => '5.5km',
            '6000' => '6km',
        ];
    }

    public function label()
    {
        return "Max km";
    }

    // public function isSearchable(): bool
    // {
    //     return true;
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
