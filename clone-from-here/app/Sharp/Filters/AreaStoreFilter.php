<?php

namespace App\Sharp\Filters;

use App\Models\Store;
use Code16\Sharp\EntityList\EntityListSelectMultipleFilter;

class AreaStoreFilter implements EntityListSelectMultipleFilter
{
    public function values()
    {
        return Store::pluck("name", "id")->all();
    }

    public function label()
    {
        return "Toko";
    }

    public function isSearchable(): bool
    {
        return true;
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}