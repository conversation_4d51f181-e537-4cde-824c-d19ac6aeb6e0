<?php

namespace App\Sharp\Filters;

use Code16\Sharp\EntityList\EntityListSelectFilter;

class CustomerLastOrderFilter implements EntityListSelectFilter
{
    public function values()
    {
        return [
            'blash_response' => 'Blash Response',
            '<1' => '< 1 bulan tidak order',
            '>1' => '1-2 bulan tidak order',
            '>2' => '2-3 bulan tidak order',
            '>3' => '3-6 bulan tidak order',
            '>6' => '> 6 bulan tidak order',
        ];
    }

    public function label()
    {
        return "Order Terakhir";
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
