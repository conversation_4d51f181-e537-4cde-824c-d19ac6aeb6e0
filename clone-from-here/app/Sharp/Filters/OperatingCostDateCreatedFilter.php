<?php

namespace App\Sharp\Filters;

use Carbon\Carbon;
use Code16\Sharp\EntityList\EntityListDateRangeFilter;

class OperatingCostDateCreatedFilter implements EntityListDateRangeFilter
{
    public function label()
    {
        return "Tanggal Dibuat";
    }

    /**
     * @return array
     *
     * awaited format:
     *
     *    [
     *       "start" => Carbon::yesterday(),
     *       "end" => Carbon::today(),
     *    ]
     *
     * @throws \Exception
     */
    public function defaultValue()
    {
        return [
            "start" => Carbon::now(),
            "end" => Carbon::now(),
        ];
    }

    public function dateFormat()
    {
        return "YYYY-MM-DD";
    }

    // public function isMondayFirst()
    // {
    //     return false;
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
