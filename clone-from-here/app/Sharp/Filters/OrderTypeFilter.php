<?php

namespace App\Sharp\Filters;

use Code16\Sharp\EntityList\EntityListSelectMultipleFilter;

class OrderTypeFilter implements EntityListSelectMultipleFilter
{
    public function values()
    {
        return [
            'notyet_sync_acc' => "Belum Sync ACC",
            'lt2' => "Lantai 2",
        ];
    }

    public function label()
    {
        return "Tipe";
    }

    // public function defaultValue()
    // {
    //     return Status::pluck("id");
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }

    // public function isSearchable(): bool
    // {
    //     return true;
    // }

    // public function searchKeys(): array
    // {
    //     return ["name"];
    // }

    // public function template(): string
    // {
    //     return '<i v-bind:class="[\'mr-1\', icon]" v-bind:style="{ color: color}"></i>{{ label }} <strong>{{ count }}</strong>';
    // }
}
