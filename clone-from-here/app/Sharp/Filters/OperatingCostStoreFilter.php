<?php

namespace App\Sharp\Filters;

use App\Models\Store;
use Code16\Sharp\EntityList\EntityListSelectMultipleFilter;

class OperatingCostStoreFilter implements EntityListSelectMultipleFilter
{
    public function values()
    {
        if (auth()->user()->role_id == 3) {
            return auth()->user()->stores->pluck("name", "id")->all();
        } else {
            return Store::pluck("name", "id")->all();
        }
    }

    public function label()
    {
        return "Toko";
    }

    public function isSearchable(): bool
    {
        return true;
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
