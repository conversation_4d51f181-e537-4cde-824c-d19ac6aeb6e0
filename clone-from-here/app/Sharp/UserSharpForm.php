<?php

namespace App\Sharp;

use App\Models\User;
use App\Models\Role;
use App\Models\Store;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\SharpForm;
use Code16\Sharp\Http\WithSharpContext;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;

class UserSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this->setCustomTransformer("userstamp", function ($value, $user) {
            return [
                "created_by" => $user->creator ? $user->creator->name : 'System',
                "created_at" => $user->created_at ? $user->created_at->format('jMy(G:i)') : '-',
                "updated_by" => $user->editor ? $user->editor->name : 'System',
                "updated_at" => $user->updated_at ? $user->updated_at->format('jMy(G:i)') : '-',
                "is_show_ltt" => true,
            ];
        })->setCustomTransformer("is_show_ltt", function ($value, $user) {
            return is_array($user->options) && array_key_exists('is_show_ltt', $user->options) ? $user->options['is_show_ltt'] : 0;
        })->transform(
            User::with(['role', 'stores'])->findOrFail($id)
        );
    }

    public function create(): array
    {
        return $this->transform(new User([
            "notification_is_active" => 1
        ]));
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $user = $id ? User::findOrFail($id) : new User;

        if (isset($data['phone'])) {
            $phone = $data['phone'];
            $phone = str_replace(' ', '', $phone);
            $phone = str_replace('-', '', $phone);
            $phone = str_replace('+', '', $phone);
            $phone = str_replace('(', '', $phone);
            $phone = str_replace(')', '', $phone);
            if (substr($phone, 0, 1) == '0') {
                $phone = '62' . substr($phone, 1);
            }
            $data['phone'] = $phone;
        }

        if (isset($data['password'])) {
            $data['password'] = bcrypt($data['password']);
        } else {
            unset($data['password']);
        }

        if (in_array((int)$data['role_id'], [1, 2, 3, 4, 6])) {
            $data['store_id'] = null;
        }
        if (!in_array((int)$data['role_id'], [1, 2, 3, 4, 6])) {
            $data['stores'] = [];
        }

        $options = $user->options ?? [];
        if (isset($data['is_show_ltt'])) {
            $options['is_show_ltt'] = $data['is_show_ltt'];
        }
        $data['options'] = $options;

        $this->ignore(['userstamp', 'is_show_ltt'])->save($user, $data);
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        User::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $current_user = auth()->user();
        $stores_id = $current_user->stores->pluck('id');
        $stores = $current_user->role_id == 3 ? Store::whereIn('id', $stores_id)->orderBy("sort_order")->pluck("name", "id")->toArray() : Store::orderBy("sort_order")->pluck("name", "id")->toArray();
        $stores['0'] = 'Kosong';

        $this->addField(
            SharpFormTextField::make('name')
                ->setLabel('Name *')
        )->addField(
            SharpFormTextCustomField::make('phone')
                ->setLabel('No. WhatsApp *')
                ->setInputType('phone')
        )->addField(
            SharpFormTextCustomField::make('email')
                ->setLabel('Email *')
                ->setInputType('email')
        )->addField(
            SharpFormTextField::make('password')
                ->setLabel($this->context()->instanceId() ? 'Password Baru' : 'Password *')
                ->setHelpMessage($this->context()->instanceId() ? 'Isi untuk mengganti password dengan yang baru.' : '')
                ->setInputTypePassword()
        )->addField(
            SharpFormSelectField::make(
                "role_id",
                $current_user->role_id == 3 ? Role::whereNotIn('id', [1, 2])->pluck("title", "id")->toArray() : Role::pluck("title", "id")->toArray()
            )
                ->setDisplayAsDropdown()
                ->setLabel("Role *")
            // ->setCreatable(true)
            // ->setCreateAttribute("name")
            // ->setMaxTagCount(4)
        )->addField(
            SharpFormSelectField::make(
                "store_id",
                $stores,
            )
                ->setDisplayAsDropdown()
                ->setClearable()
                ->setLabel("Toko *")
                ->addConditionalDisplay('role_id', ['5'])
                ->setConditionalDisplayOrOperator()
            // ->setCreatable(true)
            // ->setCreateAttribute("name")
            // ->setMaxTagCount(4)
        )->addField(
            SharpFormSelectField::make(
                "stores",
                $current_user->role_id == 3 ? Store::whereIn('id', $stores_id)->orderBy("sort_order")->pluck("name", "id")->toArray() : Store::orderBy("sort_order")->pluck("name", "id")->toArray()
            )
                ->setLabel("Toko *")
                ->setMultiple()
                ->setDisplayAsList()
                ->addConditionalDisplay('role_id', ['3', '4', '6'])
            // ->setInline()
            // ->setCreatable(true)
            // ->setCreateAttribute("name")
            // ->setMaxTagCount(4)
        )->addField(
            SharpFormTextField::make('subscriber_id')
                ->setLabel('Subscriber ID')
        )->addField(
            SharpFormCheckField::make("notification_is_active", "Aktif")
                ->setLabel("Notifikasi?")
        )->addField(
            SharpFormCheckField::make("is_show_ltt", "Tampilkan LTT")
                ->setLabel("Laporan Tutup Toko?")
                ->addConditionalDisplay('role_id', ['3', '4', '5', '6']) // admin toko / driver
        )->addField(
            SharpFormCheckField::make("is_partner", "Ya")
                ->setLabel("Sebagai MITRA?")
                ->addConditionalDisplay('role_id', ['3', '5']) // admin toko / driver
        )->addField(
            SharpFormHtmlField::make('userstamp')
                ->setTemplatePath("/sharp/templates/userstamp.vue")
        );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column->withSingleField('name')
                ->withSingleField("phone")
                ->withSingleField("email")
                ->withSingleField("password")
                ->withSingleField("role_id")
                ->withSingleField("store_id")
                ->withSingleField("stores")
                ->withSingleField("is_show_ltt")
                ->withSingleField("is_partner")
                // ->withSingleField("notification_is_active")
                // ->withSingleField("subscriber_id");
            ;
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
    }
}
