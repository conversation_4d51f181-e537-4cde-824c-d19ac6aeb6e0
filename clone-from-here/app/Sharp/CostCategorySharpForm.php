<?php

namespace App\Sharp;

use App\Models\CostCategory;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Http\WithSharpContext;
// use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\SharpForm;

class CostCategorySharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this->transform(
            CostCategory::findOrFail($id)
        );
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $model = $id ? CostCategory::findOrFail($id) : new CostCategory;

        if ($this->context()->isCreation()) {
            $data['is_root'] = false;
        }

        $this->save($model, $data);
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        CostCategory::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this
        ->addField(
            SharpFormTextField::make('title')
                ->setLabel('Name *')
        )
        ->addField(
            SharpFormSelectField::make(
                "parent_id",
                CostCategory::where('is_root', 1)->pluck("title", "id")->toArray()
            )
                ->setDisplayAsDropdown()
                ->setLabel("Category *")
                ->setClearable($this->context()->isUpdate())
                ->setReadOnly($this->context()->isUpdate())
                // ->addConditionalDisplay('is_root', [0])
                // ->setCreatable(true)
                // ->setCreateAttribute("name")
                // ->setMaxTagCount(4)
        )
        ;
        // )->addField(
        //     SharpFormTextCustomField::make("slug")
        //         ->setLabel("Slug *")
        //         ->setPrepend("https://gasplus.online/toko/palagan/produk#")
        //         ->setSlugify()
        // );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column
                ->withSingleField('parent_id')
                ->withSingleField('title')
            ;
            // ->withSingleField("slug");
        });
    }
}