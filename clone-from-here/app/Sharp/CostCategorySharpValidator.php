<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class CostCategorySharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => [
                'required',
                $this->context()->instanceId() ? 'unique:cost_categories,title,'.$this->context()->instanceId() : 'unique:cost_categories',
            ],
            // 'slug' => [
            //     'required',
            //     $this->context()->instanceId() ? 'unique:categories,slug,'.$this->context()->instanceId() : 'unique:categories',
            // ],
        ];
    }
}