<?php

namespace App\Sharp;

use App\Models\Area;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;
use Illuminate\Database\Eloquent\Builder;
use App\Sharp\Filters\AreaStoreFilter;

class AreaSharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('name')
                ->setLabel('Nama')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("store_id")
                ->setSortable()
                ->setLabel("Toko")
        )->addDataContainer(
            EntityListDataContainer::make("count")
                ->setSortable()
                ->setLabel("Checkin")
        )->addDataContainer(
            EntityListDataContainer::make("addresses_count")
                ->setSortable()
                ->setLabel("Jml. Pelanggan")
        )->addDataContainer(
            EntityListDataContainer::make("distance_store_area")
                ->setSortable()
                ->setLabel("Jarak Toko")
        );
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("name", 4, 12)
            ->addColumn("store_id", 3, 12)
            ->addColumn("addresses_count", 2, 12)
            ->addColumn("distance_store_area", 2, 12)
            ->addColumn("count", 1, 12);
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('created_at', 'desc')
            ->addFilter("stores", AreaStoreFilter::class)
            ->setPaginated();
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $areas = Area::distinct();

        if ($params->sortedBy()) {
            $areas->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%'.$words.'%';
            $areas->where(function ($query) use ($words) {
                $query->where('name', 'like', $words)
                    ->orWhere('latlng', 'like', $words);
                });
        }

        if ($params->filterFor("stores")) {
            $ids = $params->filterFor("stores");
            $areas->whereIn('store_id', (array)$ids);
        }

        return $this
            ->setCustomTransformer("name", function ($value, $model) {
                $name = '<div><strong>'.$value.'</strong></div>';
                $map = '<div><a href="http://www.google.com/maps/place/' .$model->latlng.'" target="_blank">📍 ' .$model->latlng.'</a></div>';
                return $name . $map;
            })
            ->setCustomTransformer("store_id", function ($value, $model) {
                $store_link = $model->store ? (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$model->store->name.'</p>', "store"))
                    ->setTooltip("See related store")
                    // ->setSearch($model->store->name)
                    ->toFormOfInstance($model->store)
                    ->render() : '';
                return $store_link;
            })
            ->setCustomTransformer("distance_store_area", function ($value, $model) {
                return number_format($value, 0, "", ".") . ' m';
            })
            ->transform($areas->withCount(["addresses"])->paginate(30));
    }
}