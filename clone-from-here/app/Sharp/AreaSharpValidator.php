<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;
// use Illuminate\Validation\Rule;

class AreaSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'     => [
                'required',
            ],
            'latlng_type'     => [
                'required',
            ],
            'latlng_map'     => [
                'required_if:latlng_type,map',
            ],
            'latlng_coordinate'     => [
                'required_if:latlng_type,coordinate',
            ],
            'store_id'     => [
                'required',
            ],
        ];
    }
}