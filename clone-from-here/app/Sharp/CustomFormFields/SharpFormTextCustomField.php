<?php

namespace App\Sharp\CustomFormFields;

use Code16\Sharp\Form\Fields\Formatters\TextFormatter;
use Code16\Sharp\Form\Fields\SharpFormField;

class SharpFormTextCustomField extends SharpFormField
{
    const FIELD_TYPE = "custom-textCustom";

    protected $inputType = 'text';
    protected $prepend;
    protected $placeholder = '';
    protected $textCase = '';
    protected $slugify = false;
    protected $readonly = false;

    public static function make(string $key)
    {
        return new static($key, static::FIELD_TYPE, new TextFormatter);
    }

    public function setInputType(string $val)
    {
        $this->inputType = $val;

        return $this;
    }

    public function setPrepend(string $val)
    {
        $this->prepend = $val;

        return $this;
    }

    public function setPlaceholder(string $val)
    {
        $this->placeholder = $val;

        return $this;
    }

    public function setTextCase(string $val)
    {
        $this->textCase = $val;

        return $this;
    }

    public function setSlugify()
    {
        $this->slugify = true;

        return $this;
    }

    public function setReadOnly(bool $readOnly = true)
    {
        $this->readonly = $readOnly;

        return $this;
    }

    /**
     * @return array
     */
    // protected function validationRules()
    // {
    //     return [
    //         // "prepend" => "required",
    //     ];
    // }

    /**
     * @return array
     * @throws \Code16\Sharp\Exceptions\Form\SharpFormFieldValidationException
     */
    public function toArray(): array
    {
        return parent::buildArray([
            "inputType" => $this->inputType,
            "prepend" => $this->prepend,
            "slugify" => $this->slugify,
            "readonly" => $this->readonly,
            "placeholder" => $this->placeholder,
            "textCase" => $this->textCase,
        ]);
    }
}