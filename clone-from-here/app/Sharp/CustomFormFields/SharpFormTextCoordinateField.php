<?php

namespace App\Sharp\CustomFormFields;

use Code16\Sharp\Form\Fields\Formatters\TextFormatter;
use Code16\Sharp\Form\Fields\SharpFormField;

class SharpFormTextCoordinateField extends SharpFormField
{
    const FIELD_TYPE = "custom-textCoordinate";

    protected $inputType = 'text';
    protected $placeholder = '';
    protected $readonly = false;

    public static function make(string $key)
    {
        return new static($key, static::FIELD_TYPE, new TextFormatter);
    }

    public function setInputType(string $val)
    {
        $this->inputType = $val;

        return $this;
    }

    public function setPlaceholder(string $val)
    {
        $this->placeholder = $val;

        return $this;
    }

    public function setReadOnly(bool $readOnly = true)
    {
        $this->readonly = $readOnly;

        return $this;
    }

    /**
     * @return array
     */
    // protected function validationRules()
    // {
    //     return [
    //         // "prepend" => "required",
    //     ];
    // }

    /**
     * @return array
     * @throws \Code16\Sharp\Exceptions\Form\SharpFormFieldValidationException
     */
    public function toArray(): array
    {
        return parent::buildArray([
            "inputType" => $this->inputType,
            "readonly" => $this->readonly,
            "placeholder" => $this->placeholder,
        ]);
    }
}