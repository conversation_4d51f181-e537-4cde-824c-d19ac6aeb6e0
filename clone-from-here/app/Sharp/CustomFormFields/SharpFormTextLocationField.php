<?php

namespace App\Sharp\CustomFormFields;

use Code16\Sharp\Form\Fields\Formatters\TextFormatter;
use Code16\Sharp\Form\Fields\SharpFormField;

class SharpFormTextLocationField extends SharpFormField
{
    const FIELD_TYPE = "custom-textLocation";

    protected $is_hide = false;

    public static function make(string $key)
    {
        return new static($key, static::FIELD_TYPE, new TextFormatter);
    }

    public function setHide()
    {
        $this->is_hide = true;

        return $this;
    }

    /**
     * @return array
     */
    protected function validationRules()
    {
        return [
            // "prepend" => "required",
        ];
    }

    /**
     * @return array
     * @throws \Code16\Sharp\Exceptions\Form\SharpFormFieldValidationException
     */
    public function toArray(): array
    {
        return parent::buildArray([
            "is_hide" => $this->is_hide,
        ]);
    }
}
