<?php

namespace App\Sharp\Policies;

use App\Models\User;

class ReportDriverPolicy
{

    /**
     * @param User $user
     * @return bool
     */
    public function entity(User $user)
    {
        // return true;
        return $user->role_id <= 3;
    }

    public function view(User $user, $modelId)
    {
        return false;
    }

    public function update(User $user, $modelId)
    {
        return false;
    }

    public function delete(User $user, $modelId)
    {
        return false;
    }

    public function create(User $user)
    {
        return false;
    }
}