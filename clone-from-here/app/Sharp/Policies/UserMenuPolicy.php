<?php

namespace App\Sharp\Policies;

use App\Models\User;

class UserMenuPolicy
{

    /**
     * @param User $user
     * @return bool
     */
    public function entity(User $user)
    {
        return $user->role_id <= 3 && !$user->is_partner;
    }

    // public function view(User $user, $userId)
    // {
    //     return $user->role_id <= 3;
    // }

    // public function update(User $user, $userId)
    // {
    //     return $user->role_id <= 3;
    // }

    // public function delete(User $user, $userId)
    // {
    //     return $user->role_id <= 3;
    // }

    // public function create(User $user)
    // {
    //     return $user->role_id <= 3;
    // }
}