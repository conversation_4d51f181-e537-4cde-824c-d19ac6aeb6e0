<?php

namespace App\Sharp\Policies;

use App\Models\User;
use App\Models\Order;

class OrderPolicy
{

    /**
     * @param User $user
     * @return bool
     */
    public function entity(User $user)
    {
        return true;
    }

    /**
     * @param User $user
     * @param $order_id
     * @return bool
     */
    public function view(User $user, $order_id)
    {
        if ($user->role_id <= 2) {
            return true;
        } elseif ($user->role_id == 3) {
            $order = Order::find($order_id);
            if ($order) {
                $store_id = $order->store_id;
                return $user->stores()->where('id', $store_id)->count();
            } else {
                false;
            }
        } else {
            return false;
        }
    }

    /**
     * @param User $user
     * @param $order_id
     * @return bool
     */
    public function update(User $user, $order_id)
    {
        if ($user->role_id <= 2) {
            return true;
            // } elseif ($user->role_id == 2) {
            //     $order = Order::find($order_id);
            //     return $order->status_id <= 4;
        } elseif ($user->role_id == 3) {
            $order = Order::find($order_id);
            $store_id = $order->store_id;
            // return $order->status_id <= 4 && $user->stores()->where('id', $store_id)->count();
            return $user->stores()->where('id', $store_id)->count();
        } else {
            return false;
        }
    }

    /**
     * @param User $user
     * @return bool
     */
    public function create(User $user)
    {
        return $user->role_id <= 2;
    }

    /**
     * @param User $user
     * @param $order_id
     * @return bool
     */
    public function delete(User $user, $order_id)
    {
        if ($user->role_id <= 2) {
            return true;
        } elseif ($user->role_id == 3) {
            $order = Order::find($order_id);
            $store_id = $order->store_id;
            return $user->stores()->where('id', $store_id)->count();
        } else {
            return false;
        }
    }
}
