<?php

namespace App\Sharp;

use App\Models\Deposit;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;
use Illuminate\Database\Eloquent\Builder;
use App\Sharp\Filters\DepositDateCreatedFilter;
use App\Sharp\Filters\TransactionTypeFilter;
// use App\Sharp\Commands\DepositGenerateHistory;
// use App\Sharp\Filters\DepositDateTypeFilter;
// use App\Sharp\Filters\DepositCoordinateFilter;
// use App\Sharp\Filters\DepositStoreFilter;
use Code16\Sharp\EntityList\Eloquent\Transformers\SharpUploadModelAttributeTransformer;

class DepositSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('created_at')
                ->setLabel('Tgl')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("nota")
                ->setLabel("🧾 Nota")
        )->addDataContainer(
            EntityListDataContainer::make("deposit_transfer_cash")
                ->setLabel("🌿 Deposit  |  🏧 Trf  | 🪙 Cash")
        )->addDataContainer(
            EntityListDataContainer::make("paymentproof")
                ->setLabel("")
        )->addDataContainer(
            EntityListDataContainer::make("balance")
                ->setLabel("💵 Saldo")
        )->addDataContainer(
            EntityListDataContainer::make("note")
                ->setLabel("Catatan")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("created_at", 1, 12)
            ->addColumn("nota", 2, 12)
            ->addColumn("deposit_transfer_cash", 3, 12)
            ->addColumn("paymentproof", 1, 12)
            ->addColumn("balance", 2, 12)
            ->addColumn("note", 3, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('created_at', 'DESC')
            ->addFilter("date_created", DepositDateCreatedFilter::class)
            ->addFilter("transaction_type", TransactionTypeFilter::class)
            // ->addEntityCommand("regenarete", DepositGenerateHistory::class)
            ->setPaginated();
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $current_user = auth()->user();
        $deposits = Deposit::distinct();

        if ($params->sortedBy()) {
            $deposits->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%' . $words . '%';
            $deposits->where(function ($query) use ($words) {
                $query->whereHas('order', function (Builder $subquery) use ($words) {
                    $subquery->where('code', 'like', $words);
                })
                    ->orWhere('note', 'like', $words)
                    ->orWhere('amount', 'like', $words)
                    ->orWhere('balance', 'like', $words);
            });
        }

        if ($customer_id = $params->filterFor("customer")) {
            $deposits->where("customer_id", $customer_id);
        }

        if ($transaction_type = $params->filterFor("transaction_type")) {
            if ($transaction_type == 'deposit_only') {
                $deposits->where("amount", "!=", 0);
            }
        }

        if ($date_created = $params->filterFor("date_created")) {
            $deposits->whereBetween("created_at", [
                $date_created['start'],
                $date_created['end'],
            ]);
        }

        return $this
            ->setCustomTransformer(
                "paymentproof",
                new SharpUploadModelAttributeTransformer(30, 30, ["fit" => ["w" => 30, "h" => 30]])
            )
            ->setCustomTransformer("created_at", function ($value, $model) {
                return '<span style="font-size: 12px !important;">' . $model->created_at->format('jMy') . '</span>' . '<br><span style="font-size: 12px !important; opacity: 0.6;">' . $model->created_at->format('G:i') . '</span>';
            })
            ->setCustomTransformer("nota", function ($val, $model) {
                $nota = $model->order ? (new LinkToEntity('<span style="white-space: nowrap !important;">🧾 Rp' . number_format($model->order->total, 0, '', '.') . '</span>', 'order'))
                    ->setTooltip("See related order")
                    ->toFormOfInstance($model->order)
                    ->render() : ($model->invoice ? (new LinkToEntity('<span style="white-space: nowrap !important;">🧾 Rp' . number_format($model->invoice->total_after_discount, 0, '', '.') . '</span>', 'invoice'))
                        ->setTooltip("See related invoice")
                        ->toFormOfInstance($model->invoice)
                        ->render() : '-');
                $tagihan_order = $model->order ? '<br><span style="font-size: 12px !important; opacity: 0.4;">💰 Rp' . number_format($model->order->total_after_deposit, 0, '', '.') . '</span>' : '';
                $tagihan_invoice = $model->invoice ? '<br><span style="font-size: 12px !important; opacity: 0.4;">💰 Rp' . number_format($model->invoice->total_after_discount, 0, '', '.') . '</span>' : '';
                $tagihan = $model->invoice ? $tagihan_invoice : $tagihan_order;
                $payment_ask_order = $model->order && $model->order->payment_method_ask ? '<br><span style="font-size: 12px !important; opacity: 0.6; text-transform: uppercase;">' . $model->order->payment_method_ask . '</span>' . ($model->order->payment_method_confirmed ? ': <span style="font-size: 11px !important; opacity: 0.4; text-transform: uppercase;">' . $model->order->payment_method_confirmed . '</span>' : '') : '';
                $payment_method_invoice = $model->invoice && $model->invoice->payment_method_confirmed ? '<br><span style="font-size: 12px !important; opacity: 0.6; text-transform: uppercase;">' . 'INVOICE' . '</span>' . ($model->invoice->payment_method_confirmed ? ': <span style="font-size: 11px !important; opacity: 0.4; text-transform: uppercase;">' . $model->invoice->payment_method_confirmed . '</span>' : '') : '';
                $payment_method = $model->order ? $payment_ask_order : $payment_method_invoice;
                // $payment_confirm = $model->order && $model->order->payment_method_confirmed ? '<br><span style="font-size: 12px !important; opacity: 0.6; text-transform: uppercase;">' . $model->order->payment_method_confirmed . '</span>' : $payment_ask;
                return $nota . $tagihan . $payment_method;
            })
            ->setCustomTransformer("deposit_transfer_cash", function ($val, $model) {
                $deposit_used = $model->order ? $model->order->amount_deposit_used : 0;
                $deposit = $model->order ? ('🌿 <span style="white-space: nowrap !important; color: ' . ($deposit_used < 0 ? 'red' : '') . ($deposit_used > 0 ? 'limegreen' : '') . '">' . ($deposit_used < 0 ? '-' : '') . 'Rp' . number_format(abs($deposit_used), 0, '', '.') . '</span>&nbsp;&nbsp;|&nbsp;&nbsp;') : '-';
                $trf = $model->order ? '🏧 <span style="white-space: nowrap !important;">Rp' . number_format($model->order->amount_pay, 0, '', '.') . '</span>&nbsp;&nbsp;|&nbsp;&nbsp;' : '-';
                $split_cash = $model->order ? '🪙 <span style="white-space: nowrap !important;">Rp' . number_format($model->order->amount_split_to_cash, 0, '', '.') . '</span>' : '-';
                $cash = $model->order ? '🪙 <span style="white-space: nowrap !important;">Rp' . number_format($model->order->total_after_deposit, 0, '', '.') . '</span>' : '-';
                $return_order = $deposit . $trf . ($model->order && $model->order->payment_method_ask == 'cash' && $model->order->amount_split_to_cash == 0 ? $cash : $split_cash);
                $return_invoice = $model->invoice ?
                    '🌿 <span style="white-space: nowrap !important; color: ' . ($deposit_used < 0 ? 'red' : '') . ($deposit_used > 0 ? 'limegreen' : '') . '">' . ($deposit_used < 0 ? '-' : '') . 'Rp' . number_format(0, 0, '', '.') . '</span>&nbsp;&nbsp;|&nbsp;&nbsp;' .
                    '🏧 <span style="white-space: nowrap !important;">Rp' . number_format($model->invoice->amount_pay, 0, '', '.') . '</span>&nbsp;&nbsp;|&nbsp;&nbsp;' .
                    '🪙 <span style="white-space: nowrap !important;">Rp' . number_format(0, 0, '', '.') . '</span>'
                    : '---';
                return $model->order ? $return_order : $return_invoice;
            })
            ->setCustomTransformer("balance", function ($val, $model) {
                $saldo = '<span style="white-space: nowrap !important; color: ' . ($val < 0 ? 'red' : '') . ($val > 0 ? 'limegreen' : '') . '">💵 ' . ($val < 0 ? '-' : '') . 'Rp' . number_format(abs($val), 0, '', '.') . '</span>';
                $amount = '<br><span style="font-size: 12px !important; opacity: 0.6; white-space: nowrap !important; color: ' . ($model->amount < 0 ? 'red' : '') . ($model->amount > 0 ? 'limegreen' : '') . '">' . ($model->amount < 0 ? '-' : '+') . 'Rp' . number_format(abs($model->amount), 0, '', '.') . '</span>';
                return $saldo . $amount;
            })
            ->setCustomTransformer("note", function ($val, $model) {
                $note = $val;
                $order = $model->order ? '<br><a target="_blank" style="font-size: 12px !important;" href="/manager/job-list/' . $model->order->store->slug . '?date=' . date_format($model->order->created_at, 'Y-m-d') . '&status=total&search=' . $model->order->code . '">' . $model->order->code . '</a>' : null;
                $products = $model->order ? '<br>' . $model->order->products->map(function ($product) {
                    return '🟢 ' . $product->code . ' ' . $product->name . ' (' . $product->pivot->qty . 'x)';
                })->join('<br>') : null;
                $driver = $model->order && $model->order->driver ? '<br>🛵 ' . $model->order->driver->name : null;
                return $note . $order . $products . $driver;
            })
            // ->setCustomTransformer("deposit_amount", function ($val, $customer) {
            //     return '<span style="color: '.($val < 0 ? 'red' : '').($val > 0 ? 'limegreen' : '').'">'.($val < 0 ? '-' : '').'Rp'.number_format(abs($val), 0, '', '.').'</span>';
            // })
            // ->setCustomTransformer("addresses", function ($addresses, $customer) {
            //     $addresses =  $customer->addresses->map(function ($address) {
            //         // return $address->title;
            //         $store = (new LinkToEntity("🛍 ".$address->store->name, 'store'))
            //             ->setTooltip("See related store")
            //             // ->setSearch($address->store->area)
            //             ->toFormOfInstance($address->store)
            //             ->render();
            //         $latlng = $address->latlng ? '<br><a href="http://www.google.com/maps/place/' .$address->latlng.'" target="_blank">📍 '.$address->latlng.'</a>' : null;
            //         $short_address = '<br>🏠 '.$address->address;
            //         return $store . $latlng . $short_address;
            //     })->implode("<hr>");
            //     return $addresses;
            // })
            ->transform($deposits->with(['paymentproof'])->paginate(30));
    }
}
