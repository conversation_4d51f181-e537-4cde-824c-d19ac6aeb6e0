<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
// use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Helper\Helper;

// use App\Models\Feedback;
use Illuminate\Queue\Middleware\WithoutOverlapping;

class ProcessNotif implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $datamessage;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($datamessage)
    {
        $this->datamessage = $datamessage;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array
     */
    public function backoff()
    {
        return [2, 5, 10];
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    public function middleware()
    {
        $fake_id = '9b7d62f1-2ff5-420a-8beb-d68df0b173cf';
        return [new WithoutOverlapping($fake_id)];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $helper = new Helper;
        $res = $helper->processSendNotifToServer($this->datamessage);

        if (isset($res['status']) && $res['status'] === 'error') {
            $this->fail();
        }
    }
}
