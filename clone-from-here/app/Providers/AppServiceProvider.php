<?php

namespace App\Providers;

use App\Models\Setting;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\ServiceProvider;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\RateLimiter;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // IAK Balance (Saldo)
        $iak_balance = Cache::remember(
            'iak_balance',
            60 * 2,
            function () {
                return Setting::where('name', 'iak_balance')->first();
            }
        );
        View::share('iak_balance', $iak_balance && $iak_balance->value ? $iak_balance->value['balance'] : 0);
        View::share('iak_balance_updated_at', $iak_balance ? $iak_balance->updated_at : null);

        RateLimiter::for('backups', function ($job) {
            return $job->user->vipCustomer()
                ? Limit::none()
                : Limit::perHour(1)->by($job->user->id);
        });
    }
}
