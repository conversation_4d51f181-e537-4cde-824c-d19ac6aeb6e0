<?php

namespace App\ScheduleObjects;

use App\Helper\Helper;
use Carbon\Carbon;
use App\Models\Customer;
use App\Models\Socialchat;
use App\Models\NotificationLog;
use App\Helper\HelperSocialchat;
use App\Models\NotificationSchedule;
use App\Models\Setting;

class SendNotifReminder
{
  public function __invoke($force = false, $notification_schedule_id = null)
  {
    $responses = [];
    $helperSocialchat = new HelperSocialchat();
    $helper = new Helper();
    $notif_schedules = NotificationSchedule::distinct();
    if ($notification_schedule_id) {
      $notif_schedules->where('id', $notification_schedule_id);
    }
    if (!$force) {
      $notif_schedules->where('is_active', 1);
    }
    $notif_schedules = $notif_schedules->get();
    foreach ($notif_schedules as $notif_schedule) {
      $getsocialchat_executed = 0;
      $notif_executed = 0;
      $criteria = $notif_schedule->criteria;
      $customers = [];
      // ? Get Customer Phone Numbers
      if ($criteria == 'test') {
        // ? Super Admin
        $customer = Customer::where('phone', '6281331885989')->orderBy('id', 'desc')->first();
        if ($customer->id) {
          $customers[] = $customer;
        }
        $customer = Customer::where('phone', '6285800156489')->orderBy('id', 'desc')->first();
        if ($customer) {
          $customers[] = $customer;
        }
        // ? Owner
        $customer = Customer::find('10860');
        if ($customer) {
          $customers[] = $customer;
        }
      } else {
        $month01 = Carbon::now();
        $month02 = Carbon::now();
        $month03 = Carbon::now();
        $month06 = Carbon::now();
        $month01->subDays(30);
        $month02->subDays(60);
        $month03->subDays(90);
        $month06->subDays(180);
        $data = Customer::distinct();
        if (isset($notif_schedule->others['store_ids'])) {
          $store_ids = collect($notif_schedule->others['store_ids'])->pluck('id')->toArray();
          $data->whereHas('addresses', function ($query) use ($store_ids) {
            $query->whereIn('store_id', $store_ids);
          });
        }
        $data
          ->inRandomOrder()
          ->take(5)
          ->whereNull('deleted_at')
          ->whereNotNull('socialchat_conversation_id')
          ->where(function ($query) {
            $query->where('options->ignore_notif_reminder', '!=', 1)
              ->orWhereNull('options->ignore_notif_reminder')
            ;
          });
        if ($criteria === 'gt1-lt2-month') {
          $customers = $data
            ->whereNotNull('options->last_order_at')
            ->whereDate('options->last_order_at', '<=', $month01->toDateString())
            ->whereDate('options->last_order_at', '>', $month02->toDateString())
            ->where(function ($q) use ($month01, $month02) {
              $q->whereNull('options->last_notified_at')
                ->orWhere(function ($sq) use ($month01, $month02) {
                  $sq->whereNotNull('options->last_notified_at')
                    ->whereDate('options->last_notified_at', '<=', $month01->toDateString())
                    ->whereDate('options->last_notified_at', '>', $month02->toDateString())
                  ;
                })
              ;
            })
            ->get();
        } else if ($criteria === 'gt2-lt3-month') {
          $customers = $data
            ->whereNotNull('options->last_order_at')
            ->whereDate('options->last_order_at', '<=', $month02->toDateString())
            ->whereDate('options->last_order_at', '>', $month03->toDateString())
            ->where(function ($q) use ($month02, $month03) {
              $q->whereNull('options->last_notified_at')
                ->orWhere(function ($sq) use ($month02, $month03) {
                  $sq->whereNotNull('options->last_notified_at')
                    ->whereDate('options->last_notified_at', '<=', $month02->toDateString())
                    ->whereDate('options->last_notified_at', '>', $month03->toDateString())
                  ;
                })
              ;
            })
            ->get();
        } else if ($criteria === 'gt3-lt6-month') {
          $customers = $data
            ->whereNotNull('options->last_order_at')
            ->whereDate('options->last_order_at', '<=', $month03->toDateString())
            ->whereDate('options->last_order_at', '>', $month06->toDateString())
            ->where(function ($q) use ($month03, $month06) {
              $q->whereNull('options->last_notified_at')
                ->orWhere(function ($sq) use ($month03, $month06) {
                  $sq->whereNotNull('options->last_notified_at')
                    ->whereDate('options->last_notified_at', '<=', $month03->toDateString())
                    ->whereDate('options->last_notified_at', '>', $month06->toDateString())
                  ;
                })
              ;
            })
            ->get();
        } else if ($criteria === 'gt6-month') {
          $customers = $data
            ->whereNotNull('options->last_order_at')
            ->whereDate('options->last_order_at', '<=', $month06->toDateString())
            ->where(function ($q) use ($month06) {
              $q->whereNull('options->last_notified_at')
                ->orWhere(function ($sq) use ($month06) {
                  $sq->whereNotNull('options->last_notified_at')
                    ->whereDate('options->last_notified_at', '<=', $month06->toDateString())
                  ;
                })
              ;
            })
            ->get();
        }
      }
      // ? Get Social Chat Conversation IDs
      // foreach ($customers as &$customer) {
      //   if (!$customer) {
      //     continue;
      //   }
      //   $last_socialchat = Socialchat::where('phone', $customer->phone)->orderBy('id', 'desc')->first();
      //   if ($last_socialchat) {
      //     $customer->socialchat_conversation_id = $last_socialchat->conversation_id;
      //     $customer->save();
      //   } else {
      //     if ($customer->lastorder) {
      //       $conversation_id = $helperSocialchat->getConversationId($customer->lastorder->store, $customer->phone);
      //       if ($conversation_id) {
      //         Socialchat::create([
      //           'store_id' => $customer->lastorder->store_id,
      //           'customer_id' => $customer->id,
      //           'phone' => $customer->phone,
      //           'conversation_id' => $conversation_id,
      //         ]);
      //         $customer->socialchat_conversation_id = $conversation_id;
      //         $customer->save();
      //       }
      //     }
      //   }
      //   $getsocialchat_executed++;
      //   if ($getsocialchat_executed === 10) {
      //     break;
      //   }
      // }
      // ? Send Notification
      foreach ($customers as $customer) {
        if (!$customer) {
          continue;
        }
        if (!$customer->socialchat_conversation_id) {
          // $wbcl_unsent = Setting::where('name', 'wbcl-unsent')->first();
          // if ($wbcl_unsent) {
          //   $wbcl_unsent->value = array_merge((array) $wbcl_unsent->value, [[
          //     'store' => $customer->addresses[0]->store->name,
          //     'phone' => $customer->phone,
          //   ]]);
          //   $wbcl_unsent->save();
          // } else {
          //   Setting::create([
          //     'name' => 'wbcl-unsent',
          //     'value' => [[
          //       'store' => $customer->addresses[0]->store->name,
          //       'phone' => $customer->phone,
          //     ]],
          //   ]);
          // }
          continue;
        }
        $notification_log = NotificationLog::create([
          'notification_schedule_id' => $notif_schedule->id,
          'customer_id' => $customer->id,
          'response' => '{}',
          'message' => '',
        ]);
        $hashed_notification_log_id = $helper->miniEncrypt($notification_log->id);
        $store_name = 'Gasplus';
        if ($customer->lastorder) {
          $store_name = $customer->lastorder->store->name;
        }
        $customer_name = $customer->name;
        $link_unsubscribe = url('/us/' . $hashed_notification_log_id);

        $text = $notif_schedule->message_template;
        $text = preg_replace_callback('/\{link_feedback:([a-z_0-9]+)\}/', function ($matches) use ($hashed_notification_log_id) {
          return url('/fb/' . $hashed_notification_log_id . '/' . $matches[1]);
        }, $text);
        $text = preg_replace_callback('/\{link_feedback_with_input:([a-z_0-9]+)\}/', function ($matches) use ($hashed_notification_log_id) {
          return url('/fb/' . $hashed_notification_log_id . '/' . $matches[1] . '?in=1');
        }, $text);
        $text = str_replace('{nama_pelanggan}', $customer_name, $text);
        $text = str_replace('{nama_toko}', $store_name, $text);
        $text = str_replace('{link_unsubscribe}', $link_unsubscribe, $text);
        $media = $notif_schedule->notifreminderimage ? [
          'type' => 'image',
          // 'name' => 'Gasplus.' . explode('.', $notif_schedule->notifreminderimage->file_name)[1],
          // 'mimetype' => $setting->notifreminderimage01->mime_type,
          'url' => explode('?', $notif_schedule->notifreminderimage->thumbnail(500))[0] . '?ik=' . env('CLOUDFLARE_IGNORE_KEY'),
        ] : [];
        sleep(rand(1, 6));
        $res = $helperSocialchat->sendMessage($customer->socialchat_conversation_id, $text, $media);
        $responses[] = $res;
        $message = 'SUCCESS';
        if (empty($res->messageId) || empty($res->sendAt) || !$res) {
          $message = 'FAILED';
        } else {
          // $customer->options = array_merge((array) $customer->options, ['last_order_at' => Carbon::now()->toDateTimeString()]);
          $customer->options = array_merge((array) $customer->options, ['last_notified_at' => Carbon::now()->toDateTimeString()]);
          $customer->options = array_merge((array) $customer->options, ['last_order_or_notified_at' => Carbon::now()->toDateTimeString()]);
          $customer->options = array_merge((array) $customer->options, ['last_notification_schedule_id' => $notif_schedule->id]);
          $customer->save();
        }
        $notification_log->response = $res;
        $notification_log->message = $message;
        $notification_log->save();
        // $notif_executed++;
        // if ($notif_executed === 5) {
        //   break;
        // }
      }
    }

    return $responses;
  }
}
