<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
// use App\Models\Store;
// use App\Helper\Helper;

class NotificationSchedule extends Model
{
    use HasFactory;
    use HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     // 'deleted_at',
    // ];

    protected $fillable = [
        'title',
        'criteria',
        'message_template',
        'is_active',
        'others',
    ];

    // public $rules = [
    //     'name' => 'required',
    // ];

    // Convert Input
    protected $casts = [
        'others' => 'array',
    ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function notificationlogs()
    {
        return $this->hasMany(NotificationLog::class);
    }

    public function notifreminderimage()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "notifreminderimage");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, [
            "notifreminderimage",
        ])
            ? ["model_key" => $attribute]
            : [];
    }
}
