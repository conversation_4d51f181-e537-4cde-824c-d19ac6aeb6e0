<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
// use Sqits\UserStamps\Concerns\HasUserStamps;

class OperatingCostItem extends Model
{
    use HasFactory;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'operating_cost_id',
        'cost_category_id',
        'price',
        'note',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function operatingcost()
    {
        return $this->belongsTo(OperatingCost::class, 'operating_cost_id', 'id');
    }

    public function costcategory()
    {
        return $this->belongsTo(CostCategory::class, 'cost_category_id', 'id');
    }
}