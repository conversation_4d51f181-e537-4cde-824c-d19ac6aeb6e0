<?php

namespace App\Models;

use App\Models\Product;
use App\Models\Category;
use App\Models\ProductStore;
use App\Models\CategoryStore;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use Illuminate\Database\Eloquent\SoftDeletes;

class Store extends Model
{
    use HasFactory, HasUserStamps, SoftDeletes;
    // use SoftDeletes;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'slug',
        'city_id',
        'accurate_branch_id',
        'accurate_warehouse_name',
        'accurate_cash_glaccount_id',
        'area',
        'name',
        'address',
        'description',
        'whatsapp_1',
        'whatsapp_2',
        'open_hour',
        'close_hour',
        'holiday_start',
        'holiday_end',
        'holiday_note',
        'whatsapp_2',
        'is_comingsoon',
        'is_notif_wa',
        'msg_order_new',
        'msg_order_delivered',
        'msg_order_canceled',
        'email',
        'chat_server_url',
        'sort_order',
        'marketing_start',
        'marketing_each_day',
        'hide_freejob_fordelman',
        'additionalcost_secondfloor',
        'message_blash',
        'socialchat_channel_id',
        'meta',
    ];

    // public $rules = [
    //     'slug' => 'required|string|unique:stores',
    //     'city_id' => 'required|integer',
    //     'area' => 'required|string',
    //     'name' => 'required|string',
    //     'address' => 'string',
    //     'latlng' => 'string',
    //     'description' => 'string',
    //     'whatsapp_1' => 'required|string',
    //     'whatsapp_2' => 'string',
    //     'email' => 'string',
    //     'banks' => 'array',
    //     'banks.*' => 'integer',
    //     'photo' => 'max:3000|image',
    // ];

    // Convert Input
    protected $casts = [
        // 'is_admin' => 'boolean',
        'meta' => 'array',
    ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    protected static function boot()
    {
        parent::boot();

        Store::creating(function ($model) {
            $model->sort_order = Store::max('sort_order') + 1;
        });

        Store::created(function ($store) {
            $products = Product::all();
            foreach ($products as $product) {
                ProductStore::create([
                    'store_id' => $store->id,
                    'product_id' => $product->id,
                ]);
            }
            $categories = Category::all();
            foreach ($categories as $category) {
                CategoryStore::create([
                    'store_id' => $store->id,
                    'category_id' => $category->id,
                ]);
            }
        });
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    public function drivers()
    {
        return $this->hasMany(User::class)
            ->where('role_id', 5);
    }

    public function sdms()
    {
        // $this->hasManyThrough(Employee::class, User::class)
        return $this->hasMany(User::class)
            ->whereIn('users.role_id', [4, 5, 6]);
    }

    public function banks()
    {
        return $this->belongsToMany(Bank::class)
            ->orderBy("sort_order")
            ->withTimestamps();
    }

    public function storesearchexcludes()
    {
        return $this->belongsToMany(Store::class, 'store_search_exclude', 'store_id', 'store_exclude_id');
    }

    // public function storesearchexcludes()
    // {
    //     return $this->hasMany(ProductStore::class)
    //         ->whereHas("product", function($q) {
    //             $q->whereNull('deleted_at');
    //         });
    // }

    public function products()
    {
        return $this->belongsToMany(Product::class)
            ->withPivot(
                'stock',
                'local_price',
                'is_available'
            )
            ->withTimestamps();
    }

    public function productstores()
    {
        return $this->hasMany(ProductStore::class)
            ->whereHas("product", function ($q) {
                $q->whereNull('deleted_at');
            });
    }

    public function categorystores()
    {
        return $this->hasMany(CategoryStore::class)
            ->whereHas("category", function ($q) {
                $q->where('insentive', '>', 0);
            });
    }

    // public function customers()
    // {
    //     return $this->hasManyThrough(Customer::class, Address::class);
    // }

    public function featurephoto()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "featurephoto");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["featurephoto"])
            ? ["model_key" => $attribute]
            : [];
    }
}
