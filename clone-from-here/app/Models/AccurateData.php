<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
// use Illuminate\Database\Eloquent\SoftDeletes;
// use Sqits\UserStamps\Concerns\HasUserStamps;
// use App\Models\Store;
// use App\Helper\Helper;

class AccurateData extends Model
{
    use HasFactory;

    protected $table = 'accurate_datas';
    public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'accuratable_type',
        'accuratable_id',
        'accuratable_key',
        'accurate_id',
        'accurate_no',
        'accurate_name',
        'error_message',
        'synced_at',
        // 'created_at',
        // 'updated_at',
    ];

    public function accuratable(): MorphTo
    {
        return $this->morphTo();
    }

    // public $rules = [
    //     'store_id' => 'required|integer',
    //     'area_id' => 'integer',
    //     'customer_id' => 'required|integer',
    //     'label' => 'required|string',
    //     'address' => 'required|string',
    //     'latlng' => 'string',
    //     // 'is_need_marketing' => 'string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     static::saved(function ($address) {
    //     });

    //     static::created(function ($model) {
    //         $helper = new Helper;
    //         $helper->accurateUpsertCustomer($model, true);
    //     });

    //     static::updated(function ($model) {
    //         $helper = new Helper;
    //         $helper->accurateUpsertCustomer($model);
    //     });

    //     static::deleted(function ($model) {
    //         $helper = new Helper;
    //         $helper->accurateDeleteCustomer($model);
    //     });
    // }

    // public function order()
    // {
    //     return $this->belongsTo(Order::class)
    //         ->withTrashed();
    // }
}