<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PpobPricelist extends Model
{
    use HasFactory;
    // public $timestamps = false;
    protected $table = 'ppob_pricelists';

    protected $fillable = [
        'type',
        'operator',
        'data',
        'product_id',
        'updated_at',
    ];

    protected $casts = [
        'data' => 'array',
    ];


    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
