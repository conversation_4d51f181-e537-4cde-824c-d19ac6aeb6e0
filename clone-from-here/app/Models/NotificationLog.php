<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
// use App\Models\Store;
// use App\Helper\Helper;

class NotificationLog extends Model
{
    use HasFactory;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     // 'deleted_at',
    // ];

    protected $fillable = [
        'notification_schedule_id',
        'customer_id',
        'message',
        'response',
        'feedback_title',
        'feedback_message',
    ];

    // public $rules = [
    //     'name' => 'required',
    // ];

    // Convert Input
    protected $casts = [
        'response' => 'array',
    ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function notificationschedule()
    {
        return $this->belongsTo(NotificationSchedule::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class)
            ->withTrashed();
    }
}
