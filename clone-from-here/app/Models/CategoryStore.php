<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

// use Illuminate\Database\Eloquent\SoftDeletes;

class CategoryStore extends Model
{
    use HasFactory;
    // use SoftDeletes;

    protected $table = 'category_store';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'store_id',
        'category_id',
        'insentive',
    ];

    // public $rules = [
    //     'store_id' => 'required|integer',
    //     'product_id' => 'required|integer',
    //     'stock' => 'integer',
    //     'local_price' => 'integer',
    //     'is_available' => 'required|boolean',
    //     // 'sort_order' => 'required|integer',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     Product::creating(function ($model) {
    //         $model->sort_order = Product::max('sort_order') + 1;
    //     });
    // }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function setInsentiveAttribute($value)
    {
        $this->attributes['insentive'] = (int)$value > 0 ? $value : null;
    }
}