<?php

namespace App\Models;

use Code16\Sharp\Form\Eloquent\Uploads\SharpUploadModel;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\File;

// use Spatie\Translatable\Events\TranslationHasBeenSet;
// use Spatie\Translatable\HasTranslations;

class Media extends SharpUploadModel
{
    // use HasTranslations;

    // public $translatable = ['legend'];

    protected $table = "medias";

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
	{
		parent::boot();

		Media::deleting(function ($model) {
			$file_path = public_path(
				'gasplus' . DIRECTORY_SEPARATOR . $model->file_name
			);
			if (File::exists($file_path)) {
				File::delete($file_path);
			}
		});

		// TODO: convert to webp and resize

		// Event::listen(TranslationHasBeenSet::class, function (TranslationHasBeenSet $event) {
		//     $event->model->updateCustomProperty($event->key, $event->newValue);
		//     unset($event->model->attributes[$event->key]);
		// });
	}
}