<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
use App\Helper\Helper;

class Area extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'name',
        'latlng',
        'store_id',
        'count',
        'distance_store_area',
        // 'created_at',
        // 'updated_at',
    ];

    public $rules = [
        'name' => 'required|string',
        'latlng' => 'string',
    ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    protected static function booted()
    {
        static::saving(function ($model) {
            $model->countDistance();
        });
    }

    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    public function marketings()
    {
        return $this->hasMany(Marketing::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function countDistance($manual = false)
    {
        if ($manual) {
            $this->fresh();
        }

        if ($this->store && $this->latlng && $this->store->latlng) {
            $helper = new Helper();
            $this->distance_store_area = $helper->getDistance($this->store->latlng, $this->latlng);
        }

        // $this->distance_store_customer = $helper->getDistance('-7.8157452,110.3434034', '-7.810886,110.3476412');

        if ($manual) {
            $this->save();
        }
    }
}
