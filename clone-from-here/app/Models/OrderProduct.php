<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Order;
use App\Models\ProductStore;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrderProduct extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'order_product';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'order_id',
        'product_id',
        'qty',
        'ppob_label',
        'ppob_key',
        'ppob_product_code',
        'ppob_ref_id',
        'ppob_tr_id',
        'ppob_nominal',
        'ppob_price',
        'ppob_fee',
        'ppob_komisi',
        'ppob_status',
        'price',
        'accurate_invoice_item_id',
        'deleted_at',
        'notif_sent_at',
        'ppob_response_data_id',
    ];

    // public $rules = [
    //     'store_id' => 'required|integer',
    //     'product_id' => 'required|integer',
    //     'stock' => 'integer',
    //     'local_price' => 'integer',
    //     'is_available' => 'required|boolean',
    //     // 'sort_order' => 'required|integer',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($model) {
            $order = Order::find($model->order_id);
            if ($order) {
                $order->countTotal();
            }
        });

        static::created(function ($model) {
            $product_store = ProductStore::where('store_id', $model->order->store_id)
                ->where('product_id', $model->product_id)
                ->where('stock', '!=', 999)
                ->first();
            if ($product_store) {
                $product_store->stock = $product_store->stock - $model->qty;
                $product_store->save();
            }
        });
        static::updating(function ($model) {
            $product_store = ProductStore::where('store_id', $model->order->store_id)
                ->where('product_id', $model->product_id)
                ->where('stock', '!=', 999)
                ->first();
            $qty_before = $model->getOriginal('qty');
            $qty_after = $model->qty;
            if ($product_store) {
                $product_store->stock = $product_store->stock + $qty_before - $qty_after;
                $product_store->save();
            }
        });
        static::deleted(function ($model) {
            $product_store = ProductStore::where('store_id', $model->order->store_id)
                ->where('product_id', $model->product_id)
                ->where('stock', '!=', 999)
                ->first();
            if ($product_store) {
                $product_store->stock = $product_store->stock + $model->qty;
                $product_store->save();
            }
        });
    }

    // public function ppobresponsedata()
    // {
    //     return $this->hasOne(PpobResponseData::class);
    // }

    public function ppobresponsedata()
    {
        return $this->belongsTo(PpobResponseData::class, 'ppob_response_data_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class)->withTrashed();
    }

    public function order()
    {
        return $this->belongsTo(Order::class)->withTrashed();
    }
}
