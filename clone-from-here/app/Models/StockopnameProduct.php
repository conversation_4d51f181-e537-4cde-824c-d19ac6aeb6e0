<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
// use App\Models\Order;
// use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;

class StockopnameProduct extends Model
{
    use HasFactory;
    use HasUserStamps;

    protected $table = 'stockopname_products';
    // public $timestamps = false;

    protected $dates = [
        'created_at',
        'updated_at',
        // 'deleted_at',
    ];

    protected $fillable = [
        'stockopname_id',
        'product_id',
        'product_code',
        'start_balance',
        'quantity_in',
        'quantity_out',
        'last_balance',
        'quantity_check',
        'balance_diff',
        'is_checked',
        'last_synced_at',
        'note',
        'error',
    ];

    // public $rules = [
    //     'store_id' => 'required|integer',
    //     'product_id' => 'required|integer',
    //     'stock' => 'integer',
    //     'local_price' => 'integer',
    //     'is_available' => 'required|boolean',
    //     // 'sort_order' => 'required|integer',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     static::saved(function ($model) {
    //         $order = Order::find($model->order_id);
    //         if ($order) {
    //             $order->countTotal();
    //         }
    //     });
    // }

    public function product()
    {
        return $this->belongsTo(Product::class)->withTrashed();
    }

    public function stockopname()
    {
        return $this->belongsTo(Stockopname::class)->withTrashed();
    }
}
