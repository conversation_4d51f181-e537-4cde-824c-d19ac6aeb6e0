<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;

// use Illuminate\Database\Eloquent\SoftDeletes;

class Feedback extends Model
{
    use HasFactory, HasUserStamps;

    protected $table = 'feedbacks';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'order_id',
        'service_rating',
        'delivery_rating',
        'note',
        // 'created_at',
        // 'updated_at',
    ];

    // public $rules = [
    //     'order_id' => 'required|integer',
    //     'service' => 'required|integer|min:1|max:5',
    //     'delivery' => 'required|integer|min:1|max:5',
    //     'note' => 'string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     Category::creating(function ($model) {
    //         $model->sort_order = Category::max('sort_order') + 1;
    //     });
    // }


    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
