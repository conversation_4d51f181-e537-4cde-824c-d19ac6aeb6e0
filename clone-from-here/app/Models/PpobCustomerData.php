<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PpobCustomerData extends Model
{
    use HasFactory;
    public $timestamps = false;
    protected $table = 'ppob_customer_datas';

    protected $fillable = [
        'product_id',
        'customer_id',
        'key',
        'ppob_product_code',
        'inquiry_data',
    ];

    protected $casts = [
        'inquiry_data' => 'array',
    ];


    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
}
