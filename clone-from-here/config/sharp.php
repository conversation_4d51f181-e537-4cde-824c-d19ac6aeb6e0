<?php

return [

    // Required. The name of your app, as it will be displayed in Sharp.
    "name" => "Gasplus",

    // Optional. You can here customize the URL segment in which <PERSON> will live. Default in "sharp".
    "custom_url_segment" => "cs",

    // Optional. You can prevent Sharp version to be displayed in the page title. Default is true.
    "display_sharp_version_in_title" => false,

    // Optional. Handle extensions.
    "extensions" => [
        "assets" => [
            "strategy" => "asset",
            "head" => [
                "/public/css/inject.css",
            ],
        ],
        //
        "activate_custom_fields" => true,
    ],

    // Required. Your entities list; each one must define a "list",
    // and can define "form", "validator", "policy" and "authorizations".
    "entities" => [
        "account" => [
            "show" => \App\Sharp\AccountSharpShow::class,
            "form" => \App\Sharp\AccountSharpForm::class,
            "label" => "Akun Saya"
        ],
        "user" => [
            "list" => \App\Sharp\UserSharpList::class,
            "form" => \App\Sharp\UserSharpForm::class,
            "validator" => \App\Sharp\UserSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "role" => [
            "list" => \App\Sharp\RoleSharpList::class,
            "form" => \App\Sharp\RoleSharpForm::class,
            "validator" => \App\Sharp\RoleSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "permission" => [
            "list" => \App\Sharp\PermissionSharpList::class,
            "form" => \App\Sharp\PermissionSharpForm::class,
            "validator" => \App\Sharp\PermissionSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "city" => [
            "list" => \App\Sharp\CitySharpList::class,
            "form" => \App\Sharp\CitySharpForm::class,
            "validator" => \App\Sharp\CitySharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "store" => [
            "list" => \App\Sharp\StoreSharpList::class,
            "form" => \App\Sharp\StoreSharpForm::class,
            "validator" => \App\Sharp\StoreSharpValidator::class,
            "policy" => \App\Sharp\Policies\StorePolicy::class,
        ],
        "customer" => [
            "list" => \App\Sharp\CustomerSharpList::class,
            "show" => \App\Sharp\CustomerSharpShow::class,
            "form" => \App\Sharp\CustomerSharpForm::class,
            "validator" => \App\Sharp\CustomerSharpValidator::class,
            "policy" => \App\Sharp\Policies\CustomerPolicy::class,
        ],
        "merger" => [
            "list" => \App\Sharp\MergerSharpList::class,
            "form" => \App\Sharp\MergerSharpForm::class,
            "validator" => \App\Sharp\MergerSharpValidator::class,
            "policy" => \App\Sharp\Policies\CsMenuPolicy::class,
        ],
        "customer_deposit" => [
            "list" => \App\Sharp\DepositSharpList::class,
            // "show" => \App\Sharp\CustomerSharpShow::class,
            "form" => \App\Sharp\DepositSharpForm::class,
            "validator" => \App\Sharp\DepositSharpValidator::class,
            "policy" => \App\Sharp\Policies\DepositPolicy::class,
        ],
        "category" => [
            "list" => \App\Sharp\CategorySharpList::class,
            "form" => \App\Sharp\CategorySharpForm::class,
            "validator" => \App\Sharp\CategorySharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "product" => [
            "list" => \App\Sharp\ProductSharpList::class,
            "form" => \App\Sharp\ProductSharpForm::class,
            "validator" => \App\Sharp\ProductSharpValidator::class,
            "policy" => \App\Sharp\Policies\ProductMenuPolicy::class,
        ],
        "status" => [
            "list" => \App\Sharp\StatusSharpList::class,
            "form" => \App\Sharp\StatusSharpForm::class,
            "validator" => \App\Sharp\StatusSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "order" => [
            "list" => \App\Sharp\OrderSharpList::class,
            "form" => \App\Sharp\OrderSharpForm::class,
            "validator" => \App\Sharp\OrderSharpValidator::class,
            "policy" => \App\Sharp\Policies\OrderPolicy::class,
        ],
        "area" => [
            "list" => \App\Sharp\AreaSharpList::class,
            "form" => \App\Sharp\AreaSharpForm::class,
            "validator" => \App\Sharp\AreaSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "report_driver" => [
            "list" => \App\Sharp\ReportDriverSharpList::class,
            // "form" => \App\Sharp\ReportDriverSharpForm::class,
            // "validator" => \App\Sharp\ReportDriverSharpValidator::class,
            "policy" => \App\Sharp\Policies\ReportDriverPolicy::class,
        ],
        "feedback" => [
            "list" => \App\Sharp\FeedbackSharpList::class,
            "authorizations" => [
                "delete" => false,
                "create" => false,
                "update" => false,
                "view" => false
            ],
            // "form" => \App\Sharp\FeedbackSharpForm::class,
            // "validator" => \App\Sharp\FeedbackSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "cost_category" => [
            "list" => \App\Sharp\CostCategorySharpList::class,
            "form" => \App\Sharp\CostCategorySharpForm::class,
            "validator" => \App\Sharp\CostCategorySharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "employee" => [
            "list" => \App\Sharp\EmployeeSharpList::class,
            "form" => \App\Sharp\EmployeeSharpForm::class,
            "validator" => \App\Sharp\EmployeeSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "armada" => [
            "list" => \App\Sharp\ArmadaSharpList::class,
            "form" => \App\Sharp\ArmadaSharpForm::class,
            "validator" => \App\Sharp\ArmadaSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "operating_cost" => [
            "list" => \App\Sharp\OperatingCostSharpList::class,
            "form" => \App\Sharp\OperatingCostSharpForm::class,
            "validator" => \App\Sharp\OperatingCostSharpValidator::class,
            "policy" => \App\Sharp\Policies\OperatingCostPolicy::class,
        ],
        "wbcl" => [
            "list" => \App\Sharp\WbclSharpList::class,
            "show" => \App\Sharp\WbclSharpShow::class,
            "form" => \App\Sharp\WbclSharpForm::class,
            "validator" => \App\Sharp\WbclSharpValidator::class,
            "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        ],
        "notification_log" => [
            "list" => \App\Sharp\NotificationLogSharpList::class,
            // "show" => \App\Sharp\CustomerSharpShow::class,
            // "form" => \App\Sharp\DepositSharpForm::class,
            // "validator" => \App\Sharp\DepositSharpValidator::class,
            "policy" => \App\Sharp\Policies\NotificationLogMenuPolicy::class,
        ],
        // 'setting' => [
        //     'show' => \App\Sharp\SettingSharpShow::class,
        //     'form' => \App\Sharp\SettingSharpForm::class,
        //     'validator' => \App\Sharp\SettingSharpValidator::class,
        //     // 'policy' => \App\Sharp\SettingPolicy::class,
        //     "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        //     'label' => 'Settings',
        // ],
        // "operating_cost" => [
        //     "list" => \App\Sharp\OperatingCostSharpList::class,
        //     "form" => \App\Sharp\OperatingCostSharpForm::class,
        //     "validator" => \App\Sharp\OperatingCostSharpValidator::class,
        //     "policy" => \App\Sharp\Policies\AdminMenuPolicy::class,
        // ],
    ],

    // Optional. Your dashboards list; each one must define a "view", and can define "policy".
    "dashboards" => [
        "main" => [
            "view" => \App\Sharp\DashboardMain::class,
            "policy" => \App\Sharp\Policies\DashboardPolicy::class,
        ],
        // "toko" => [
        //     "view" => \App\Sharp\DashboardStore::class,
        //     "policy" => \App\Sharp\Policies\DashboardPolicy::class,
        // ],
    ],

    // Optional. Your global filters list, which will be displayed in the main menu.
    "global_filters" => [
        //        "my_global_filter" => \App\Sharp\Filters\MyGlobalFilter::class
    ],

    // Required. The main menu (left bar), which may contain links to entities, dashboards
    // or external URLs, grouped in categories.
    "menu" => [
        [
            "label" => "Dashboard",
            "icon" => "fa-dashboard",
            "dashboard" => "main"
        ],
        [
            "label" => "Order",
            "entities" => [
                [
                    "label" => "Order",
                    "icon" => "fa-shopping-cart",
                    "entity" => "order"
                ],
                // [
                //     "label" => "Performa Delman",
                //     "icon" => "fa-chart-line",
                //     "entity" => "report_driver"
                // ]
                // [
                //     "label" => "Rating",
                //     "icon" => "fa-star",
                //     "entity" => "feedback"
                // ],
            ]
        ],
        [
            "label" => "Toko",
            "entities" => [
                [
                    "label" => "Kota",
                    "icon" => "fa-city",
                    "entity" => "city"
                ],
                [
                    "label" => "Toko",
                    "icon" => "fa-store",
                    "entity" => "store"
                ],
                [
                    "label" => "Pelanggan",
                    "icon" => "fa-users",
                    "entity" => "customer"
                ],
                [
                    "label" => "Merger Pelanggan",
                    "icon" => "fa-user-tag",
                    "entity" => "merger"
                ],
                [
                    "label" => "Perumahan",
                    "icon" => "fa-map-marked-alt",
                    "entity" => "area"
                ],
            ]
        ],
        [
            "label" => "Produk",
            "entities" => [
                [
                    "label" => "Kategori",
                    "icon" => "fa-cubes",
                    "entity" => "category"
                ],
                [
                    "label" => "Produk",
                    "icon" => "fa-cube",
                    "entity" => "product"
                ]
            ]
        ],
        [
            "label" => "Operasional",
            "entities" => [
                [
                    "label" => "Kategori",
                    "icon" => "fa-cubes",
                    "entity" => "cost_category"
                ],
                [
                    "label" => "SDM",
                    "icon" => "fa-user-friends",
                    "entity" => "employee"
                ],
                [
                    "label" => "Armada",
                    "icon" => "fa-motorcycle",
                    "entity" => "armada"
                ],
                [
                    "label" => "Pengeluaran",
                    "icon" => "fa-money",
                    "entity" => "operating_cost"
                ],
                [
                    "label" => "WBCL",
                    "icon" => "fa-calendar",
                    "entity" => "wbcl"
                ],
            ]
        ],
        [
            "label" => "Admin",
            "entities" => [
                [
                    "label" => "User",
                    "icon" => "fa-user-friends",
                    "entity" => "user"
                ],
                [
                    "label" => "Role",
                    "icon" => "fa-user-tag",
                    "entity" => "role"
                ],
                [
                    "label" => "Permission",
                    "icon" => "fa-user-lock",
                    "entity" => "permission"
                ],
                [
                    "label" => "Status Order",
                    "icon" => "fa-tags",
                    "entity" => "status"
                ],
                // [
                //     "label" => "Settings",
                //     'icon' => 'fa-cogs',
                //     'entity' => 'setting',
                //     'single' => true,
                // ]
            ]
        ],
        [
            "label" => "Akun Saya",
            "icon" => "fas fa-user",
            "entity" => "account",
            "single" => true
        ],
        [
            "label" => "Update PWA",
            "icon" => "fas fa-mobile",
            "url" => env('UPDATE_APP_URL', '')
        ],
    ],

    // Optional. Your file upload configuration.
    "uploads" => [
        // Tmp directory used for file upload.
        "tmp_dir" => env("SHARP_UPLOADS_TMP_DIR", "tmp"),

        // These two configs are used for thumbnail generation inside Sharp.
        "thumbnails_disk" => env("SHARP_UPLOADS_THUMBS_DISK", "public"),
        "thumbnails_dir" => env("SHARP_UPLOADS_THUMBS_DIR", "thumbnails"),
    ],

    // Optional. Auth related configuration.
    "auth" => [
        // Name of the login and password attributes of the User Model.
        "login_attribute" => "email",
        "password_attribute" => "password",

        // Name of the attribute used to display the current user in the UI.
        "display_attribute" => "name",

        // Optional additional auth check.
        //        "check_handler" => \App\Sharp\Auth\MySharpCheckHandler::class,

        // Optional custom guard
        //        "guard" => "sharp",
    ],


    // "login_page_message_blade_path" => env("SHARP_LOGIN_PAGE_MESSAGE_BLADE_PATH", "sharp/_login-page-message"),

];
