<template>
    <div class="d-flex align-items-center">
        <div class="SharpNumberPrepend__text" v-html="prepend"></div>
        <input
            type="number"
            min="0"
            class="SharpText SharpNumberPrepend"
            :value="value"
            @input="handleChanged"
        />
    </div>
</template>

<script>
export default {
    props: {
        value: String, // field value
        prepend: String // custom added props (given in field definition)
    },
    methods: {
        handleChanged(e) {
            this.$emit("input", e.target.value); // emit input when the value change, form data is updated
        }
    }
};
</script>

<style type="text/css" scoped>
.SharpNumberPrepend {
    flex-grow: 1;
    padding-left: 0.5em;
}
.SharpNumberPrepend__text {
    font-size: 14px;
    padding: 0 0.5em;
    background-color: #e0e4e8;
    line-height: 40px;
    border: 1px solid transparent;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.1);
}
</style>
