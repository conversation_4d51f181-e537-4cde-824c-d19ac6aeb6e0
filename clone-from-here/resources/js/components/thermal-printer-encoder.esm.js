/**
 * Bundled by js<PERSON>eliv<PERSON> using Rollup v2.79.1 and Terser v5.19.2.
 * Original file: /npm/thermal-printer-encoder@2.0.0/dist/thermal-printer-encoder.esm.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t={exports:{}},i={html:{skipScheme:"html",lineBreakScheme:"html",whitespace:"collapse"}},s=/<\s*br(?:[\s/]*|\s[^>]*)>/gi,n={unix:[/\n/g,"\n"],dos:[/\r\n/g,"\r\n"],mac:[/\r/g,"\r"],html:[s,"<br>"],xhtml:[s,"<br/>"]},r={"ansi-color":/\x1B\[[^m]*m/g,html:/<[^>]*>/g,bbcode:/\[[^]]*\]/g},a={soft:1,hard:1},o={collapse:1,default:1,line:1,all:1},h={all:1,multi:1,none:1},l=/([sm])(\d+)/,d=/[-/\\^$*+?.()|[\]{}]/g;function c(e){return e.replace(d,"\\$&")}var p=t.exports=function(e,t,s){"object"==typeof e&&(e=(s=e).start,t=s.stop),"object"==typeof t&&(s=t,e=e||s.start,t=void 0),t||(t=e,e=0),s||(s={});var d,p,u,g,f,w,_,b,m,y,v,k,q,E,L,A,x,j,I="soft",M="default",R=4,B="all",S="",C="";if(d=s.preset)for(d instanceof Array||(d=[d]),j=0;j<d.length;j++){if(!(A=i[d[j]]))throw new TypeError('preset must be one of "'+Object.keys(i).join('", "')+'"');A.mode&&(I=A.mode),A.whitespace&&(M=A.whitespace),void 0!==A.tabWidth&&(R=A.tabWidth),A.skip&&(p=A.skip),A.skipScheme&&(u=A.skipScheme),A.lineBreak&&(g=A.lineBreak),A.lineBreakScheme&&(f=A.lineBreakScheme),A.respectLineBreaks&&(B=A.respectLineBreaks),void 0!==A.preservedLineIndent&&(_=A.preservedLineIndent),void 0!==A.wrapLineIndent&&(b=A.wrapLineIndent),A.wrapLineIndentBase&&(m=A.wrapLineIndentBase)}if(s.mode){if(!a[s.mode])throw new TypeError('mode must be one of "'+Object.keys(a).join('", "')+'"');I=s.mode}if(s.whitespace){if(!o[s.whitespace])throw new TypeError('whitespace must be one of "'+Object.keys(o).join('", "')+'"');M=s.whitespace}if(void 0!==s.tabWidth){if(!(parseInt(s.tabWidth,10)>=0))throw new TypeError("tabWidth must be a non-negative integer");R=parseInt(s.tabWidth,10)}if(L=new Array(R+1).join(" "),s.respectLineBreaks){if(!h[s.respectLineBreaks]&&!l.test(s.respectLineBreaks))throw new TypeError('respectLineBreaks must be one of "'+Object.keys(h).join('", "')+'", "m<num>", "s<num>"');B=s.respectLineBreaks}if("multi"===B)B="m",w=2;else if(!h[B]){var O=l.exec(B);B=O[1],w=parseInt(O[2],10)}if(void 0!==s.preservedLineIndent){if(!(parseInt(s.preservedLineIndent,10)>=0))throw new TypeError("preservedLineIndent must be a non-negative integer");_=parseInt(s.preservedLineIndent,10)}if(_>0&&(S=new Array(_+1).join(" ")),void 0!==s.wrapLineIndent){if(isNaN(parseInt(s.wrapLineIndent,10)))throw new TypeError("wrapLineIndent must be an integer");b=parseInt(s.wrapLineIndent,10)}if(s.wrapLineIndentBase&&(m=s.wrapLineIndentBase),m){if(void 0===b)throw new TypeError("wrapLineIndent must be specified when wrapLineIndentBase is specified");if(m instanceof RegExp)E=m;else{if("string"!=typeof m)throw new TypeError("wrapLineIndentBase must be either a RegExp object or a string");E=new RegExp(c(m))}}else if(b>0)C=new Array(b+1).join(" ");else if(b<0)throw new TypeError("wrapLineIndent must be non-negative when a base is not specified");if(s.skipScheme){if(!r[s.skipScheme])throw new TypeError('skipScheme must be one of "'+Object.keys(r).join('", "')+'"');u=s.skipScheme}if(s.skip&&(p=s.skip),p)if(p instanceof RegExp)(y=p).global||(x="g",y.ignoreCase&&(x+="i"),y.multiline&&(x+="m"),y=new RegExp(y.source,x));else{if("string"!=typeof p)throw new TypeError("skip must be either a RegExp object or a string");y=new RegExp(c(p),"g")}if(!y&&u&&(y=r[u]),s.lineBreakScheme){if(!n[s.lineBreakScheme])throw new TypeError('lineBreakScheme must be one of "'+Object.keys(n).join('", "')+'"');f=s.lineBreakScheme}if(s.lineBreak&&(g=s.lineBreak),f&&(A=n[f])&&(v=A[0],k=A[1]),g)if(g instanceof Array&&(1===g.length?g=g[0]:g.length>=2&&(g[0]instanceof RegExp?(v=g[0],"string"==typeof g[1]&&(k=g[1])):g[1]instanceof RegExp?(v=g[1],"string"==typeof g[0]&&(k=g[0])):"string"==typeof g[0]&&"string"==typeof g[1]?(v=new RegExp(c(g[0]),"g"),k=g[1]):g=g[0])),"string"==typeof g)k=g,v||(v=new RegExp(c(g),"g"));else if(g instanceof RegExp)v=g;else if(!(g instanceof Array))throw new TypeError("lineBreak must be a RegExp object, a string, or an array consisted of a RegExp object and a string");v||(v=/\n/g,k="\n"),x="g",v.ignoreCase&&(x+="i"),v.multiline&&(x+="m"),q=new RegExp("\\s*(?:"+v.source+")(?:"+v.source+"|\\s)*",x),v.global||(v=new RegExp(v.source,x));var z="hard"===I?/\b/:/(\S+\s+)/,T=new Array(e+1).join(" "),U="default"===M||"collapse"===M,W="collapse"===M,N="line"===M,P="all"===M,H=/\t/g,G=/  +/g,K=/^\s+/,$=/\s+$/,F=/\S/,Q=/\s/,D=t-e;return function(i){var s;if(i=i.toString().replace(H,L),!k){if(v.lastIndex=0,!(s=v.exec(i)))throw new TypeError("Line break string for the output not specified");k=s[0]}var n,r,a,o,h,l,d,c,p,u=0;for(n=[],q.lastIndex=0,s=q.exec(i);s;){if(n.push(i.substring(u,s.index)),"none"!==B){for(a=[],o=0,v.lastIndex=0,r=v.exec(s[0]);r;)a.push(s[0].substring(o,r.index)),o=r.index+r[0].length,r=v.exec(s[0]);a.push(s[0].substring(o)),n.push({type:"break",breaks:a})}else h=W?" ":s[0].replace(v,""),n.push({type:"break",remaining:h});u=s.index+s[0].length,s=q.exec(i)}if(n.push(i.substring(u)),y)for(p=[],l=0;l<n.length;l++){var g=n[l];if("string"!=typeof g)p.push(g);else{for(u=0,y.lastIndex=0,s=y.exec(g);s;)p.push(g.substring(u,s.index)),p.push({type:"skip",value:s[0]}),u=s.index+s[0].length,s=y.exec(g);p.push(g.substring(u))}}else p=n;var f=[];for(l=0;l<p.length;l++){var _=p[l];if("string"!=typeof _)f.push(_);else{W&&(_=_.replace(G," "));var m=_.split(z),A=[];for(d=0;d<m.length;d++){var x=m[d];if("hard"===I)for(c=0;c<x.length;c+=D)A.push(x.slice(c,c+D));else A.push(x)}f=f.concat(A)}}var j,M=0,R=e+S.length,O=[T+S],V=0,Y=!0,J=!0,X=C;function Z(i){var s,n,r,a=O[M];if(P)R>t&&(V=V||t,r=a.length-(R-V),O[M]=a.substring(0,r)),V=0;else{for(s=a.length-1;s>=e&&" "===a[s];)s--;for(;s>=e&&Q.test(a[s]);)s--;++s!==a.length&&(O[M]=a.substring(0,s)),J&&Y&&N&&R>t&&(r=a.length-(R-t))<s&&(r=s)}if(J&&(J=!1,E&&(s=O[M].substring(e).search(E),X=s>=0&&s+b>0?new Array(s+b+1).join(" "):"")),r){for(;r+D<a.length;)P?(n=a.substring(r,r+D),O.push(T+X+n)):O.push(T+X),r+=D,M++;if(!i)return n=a.substring(r),X+n;P?(n=a.substring(r),O.push(T+X+n)):O.push(T+X),M++}return""}for(l=0;l<f.length;l++){var ee=f[l];if(""!==ee)if("string"==typeof ee){for(var te;;){if(te=void 0,R+ee.length>t&&R+(te=ee.replace($,"")).length>t&&""!==te&&R>e){if(j=Z(!1),O.push(T+X),M++,R=e+X.length,j){O[M]+=j,R+=j.length,Y=!0;continue}!U&&(!N||J&&Y)||(ee=ee.replace(K,"")),Y=!1}else Y&&(U||N&&(!J||!Y)?""!==(ee=ee.replace(K,""))&&(Y=!1):F.test(ee)&&(Y=!1));break}P&&te&&R+te.length>t&&(V=R+te.length),O[M]+=ee,R+=ee.length}else if("break"===ee.type)if("none"!==B){var ie=ee.breaks,se=ie.length-1;if("s"===B){for(d=0;d<se;d++)ie[d+1].length<w?ie[d+1]=W?" ":ie[d]+ie[d+1]:(P&&(O[M]+=ie[d],R+=ie[d].length),Z(!0),O.push(T+S),M++,R=e+S.length,J=Y=!0);(!Y||P||N&&J)&&((W||!Y&&""===ie[se])&&(ie[se]=" "),O[M]+=ie[se],R+=ie[se].length)}else if("m"===B&&se<w)(!Y||P||N&&J)&&(W?ee=" ":(ee=ie.join(""),Y||""!==ee||(ee=" ")),O[M]+=ee,R+=ee.length);else if(U){for(Z(!0),d=0;d<se;d++)O.push(T+S),M++;R=e+S.length,J=Y=!0}else for((P||J&&Y)&&(O[M]+=ie[0],R+=ie[0].length),d=0;d<se;d++)Z(!0),O.push(T+S+ie[d+1]),M++,R=e+S.length+ie[d+1].length,J=Y=!0}else(!Y||P||N&&J)&&(ee=ee.remaining,(W||!Y&&""===ee)&&(ee=" "),O[M]+=ee,R+=ee.length);else"skip"===ee.type&&(R>t&&(j=Z(!1),O.push(T+X),M++,R=e+X.length,j&&(O[M]+=j,R+=j.length),Y=!0),O[M]+=ee.value)}return Z(!0),O.join(k)}};p.soft=p,p.hard=function(){var e=[].slice.call(arguments),t=e.length-1;return"object"==typeof e[t]?e[t].mode="hard":e.push({mode:"hard"}),p.apply(null,e)},p.wrap=function(e){var t=[].slice.call(arguments);return t.shift(),p.apply(null,t)(e)};var u=e(t.exports),g=function(e,t){return Object.assign(document.createElement("canvas"),{width:e,height:t})};var f=e(new class{grayscale(e){for(let t=0;t<e.data.length;t+=4){const i=.299*e.data[t]+.587*e.data[t+1]+.114*e.data[t+2];e.data.fill(i,t,t+3)}return e}threshold(e,t){for(let i=0;i<e.data.length;i+=4){const s=.299*e.data[i]+.587*e.data[i+1]+.114*e.data[i+2]<t?0:255;e.data.fill(s,i,i+3)}return e}bayer(e,t){const i=[[15,135,45,165],[195,75,225,105],[60,180,30,150],[240,120,210,90]];for(let s=0;s<e.data.length;s+=4){const n=.299*e.data[s]+.587*e.data[s+1]+.114*e.data[s+2],r=s/4%e.width,a=Math.floor(s/4/e.width),o=Math.floor((n+i[r%4][a%4])/2)<t?0:255;e.data.fill(o,s,s+3)}return e}floydsteinberg(e){const t=e.width,i=new Uint8ClampedArray(e.width*e.height);for(let t=0,s=0;s<e.data.length;t++,s+=4)i[t]=.299*e.data[s]+.587*e.data[s+1]+.114*e.data[s+2];for(let s=0,n=0;n<e.data.length;s++,n+=4){const r=i[s]<129?0:255,a=Math.floor((i[s]-r)/16);e.data.fill(r,n,n+3),i[s+1]+=7*a,i[s+t-1]+=3*a,i[s+t]+=5*a,i[s+t+1]+=1*a}return e}atkinson(e){const t=e.width,i=new Uint8ClampedArray(e.width*e.height);for(let t=0,s=0;s<e.data.length;t++,s+=4)i[t]=.299*e.data[s]+.587*e.data[s+1]+.114*e.data[s+2];for(let s=0,n=0;n<e.data.length;s++,n+=4){const r=i[s]<129?0:255,a=Math.floor((i[s]-r)/8);e.data.fill(r,n,n+3),i[s+1]+=a,i[s+2]+=a,i[s+t-1]+=a,i[s+t]+=a,i[s+t+1]+=a,i[s+2*t]+=a}return e}});var w=e(new class{flatten(e,t){for(let i=0;i<e.data.length;i+=4){const s=e.data[i+3],n=255-s;e.data[i]=(s*e.data[i]+n*t[0])/255,e.data[i+1]=(s*e.data[i+1]+n*t[1])/255,e.data[i+2]=(s*e.data[i+2]+n*t[2])/255,e.data[i+3]=255}return e}});const _={cp437:{name:"USA, Standard Europe",languages:["en"],offset:128,chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp720:{name:"Arabic",languages:["ar"],offset:128,chars:"éâàçêëèïîّْô¤ـûùءآأؤ£إئابةتثجحخدذرزسشص«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀ضطظعغفµقكلمنهوىي≡ًٌٍَُِ≈°∙·√ⁿ²■ "},cp737:{name:"Greek",languages:["el"],offset:128,chars:"ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩαβγδεζηθικλμνξοπρσςτυφχψ░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀ωάέήϊίόύϋώΆΈΉΊΌΎΏ±≥≤ΪΫ÷≈°∙·√ⁿ²■ "},cp775:{name:"Baltic Rim",languages:["et","lt"],offset:128,chars:"ĆüéāäģåćłēŖŗīŹÄÅÉæÆōöĢ¢ŚśÖÜø£Ø×¤ĀĪóŻżź”¦©®¬½¼Ł«»░▒▓│┤ĄČĘĖ╣║╗╝ĮŠ┐└┴┬├─┼ŲŪ╚╔╩╦╠═╬Žąčęėįšųūž┘┌█▄▌▐▀ÓßŌŃõÕµńĶķĻļņĒŅ’­±“¾¶§÷„°∙·¹³²■ "},cp850:{name:"Multilingual",languages:["en"],offset:128,chars:"ÇüéâäůćçłëŐőîŹÄĆÉĹĺôöĽľŚśÖÜŤťŁ×čáíóúĄąŽžĘę¬źČş«»░▒▓│┤ÁÂĚŞ╣║╗╝Żż┐└┴┬├─┼Ăă╚╔╩╦╠═╬¤đĐĎËďŇÍÎě┘┌█▄ŢŮ▀ÓßÔŃńňŠšŔÚŕŰýÝţ´­˝˛ˇ˘§÷¸°¨˙űŘř■ "},cp851:{name:"Greek",languages:["el"],offset:128,chars:"ÇüéâäàΆçêëèïîΈÄΉΊ ΌôöΎûùΏÖÜά£έήίϊΐόύΑΒΓΔΕΖΗ½ΘΙ«»░▒▓│┤ΚΛΜΝ╣║╗╝ΞΟ┐└┴┬├─┼ΠΡ╚╔╩╦╠═╬ΣΤΥΦΧΨΩαβγ┘┌█▄δε▀ζηθικλμνξοπρσςτ´­±υφχ§ψ¸°¨ωϋΰώ■ "},cp852:{name:"Latin 2",languages:["hu","pl","cz"],offset:128,chars:"ÇüéâäůćçłëŐőîŹÄĆÉĹĺôöĽľŚśÖÜŤťŁ×čáíóúĄąŽžĘę¬źČş«»░▒▓│┤ÁÂĚŞ╣║╗╝Żż┐└┴┬├─┼Ăă╚╔╩╦╠═╬¤đĐĎËďŇÍÎě┘┌█▄ŢŮ▀ÓßÔŃńňŠšŔÚŕŰýÝţ´­˝˛ˇ˘§÷¸°¨˙űŘř■ "},cp853:{name:"Turkish",languages:["tr"],offset:128,chars:"ÇüéâäàĉçêëèïîìÄĈÉċĊôöòûùİÖÜĝ£Ĝ×ĵáíóúñÑĞğĤĥ�½Ĵş«»░▒▓│┤ÁÂÀŞ╣║╗╝Żż┐└┴┬├─┼Ŝŝ╚╔╩╦╠═╬¤��ÊËÈıÍÎÏ┘┌█▄�Ì▀ÓßÔÒĠġµĦħÚÛÙŬŭ�´­�ℓŉ˘§÷¸°¨˙�³²■ "},cp855:{name:"Cyrillic",languages:["bg"],offset:128,chars:"ђЂѓЃёЁєЄѕЅіІїЇјЈљЉњЊћЋќЌўЎџЏюЮъЪаАбБцЦдДеЕфФгГ«»░▒▓│┤хХиИ╣║╗╝йЙ┐└┴┬├─┼кК╚╔╩╦╠═╬¤лЛмМнНоОп┘┌█▄Пя▀ЯрРсСтТуУжЖвВьЬ№­ыЫзЗшШэЭщЩчЧ§■ "},cp857:{name:"Turkish",languages:["tr"],offset:128,chars:"ÇüéâäàåçêëèïîıÄÅÉæÆôöòûùİÖÜø£ØŞşáíóúñÑĞğ¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤ºªÊËÈ�ÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµ�×ÚÛÙìÿ¯´­±�¾¶§÷¸°¨·¹³²■ "},cp858:{name:"Euro",languages:["en"],offset:128,chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø×ƒáíóúñÑªº¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤ðÐÊËÈ€ÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµþÞÚÛÙýÝ¯´­±‗¾¶§÷¸°¨·¹³²■ "},cp860:{name:"Portuguese",languages:["pt"],offset:128,chars:"ÇüéâãàÁçêÊèÍÔìÃÂÉÀÈôõòÚùÌÕÜ¢£Ù₧ÓáíóúñÑªº¿Ò¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp861:{name:"Icelandic",languages:["is"],offset:128,chars:"ÇüéâäàåçêëèÐðÞÄÅÉæÆôöþûÝýÖÜø£Ø₧ƒáíóúÁÍÓÚ¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp862:{name:"Hebrew",languages:["he"],offset:128,chars:"אבגדהוזחטיךכלםמןנסעףפץצקרשת¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp863:{name:"Canadian French",languages:["fr"],offset:128,chars:"ÇüéâÂà¶çêëèïî‗À§ÉÈÊôËÏûù¤ÔÜ¢£ÙÛƒ¦´óú¨¸³¯Î⌐¬½¼¾«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp864:{name:"Arabic",languages:["ar"],offset:0,chars:"\0\b\t\n\v\f\r !\"#$٪&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~°·∙√▒─│┼┤┬├┴┐┌└┘β∞φ±½¼≈«»ﻷﻸ��ﻻﻼ� ­ﺂ£¤ﺄ��ﺎﺏﺕﺙ،ﺝﺡﺥ٠١٢٣٤٥٦٧٨٩ﻑ؛ﺱﺵﺹ؟¢ﺀﺁﺃﺅﻊﺋﺍﺑﺓﺗﺛﺟﺣﺧﺩﺫﺭﺯﺳﺷﺻﺿﻁﻅﻋﻏ¦¬÷×ﻉـﻓﻗﻛﻟﻣﻧﻫﻭﻯﻳﺽﻌﻎﻍﻡﹽّﻥﻩﻬﻰﻲﻐﻕﻵﻶﻝﻙﻱ■�"},cp865:{name:"Nordic",languages:["sv","dk"],offset:128,chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø₧ƒáíóúñÑªº¿⌐¬½¼¡«¤░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp866:{name:"Cyrillic 2",languages:["ru"],offset:128,chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёЄєЇїЎў°∙·√№¤■ "},cp869:{name:"Greek",languages:["el"],offset:128,chars:"������Ά�·¬¦‘’Έ―ΉΊΪΌ��ΎΫ©Ώ²³ά£έήίϊΐόύΑΒΓΔΕΖΗ½ΘΙ«»░▒▓│┤ΚΛΜΝ╣║╗╝ΞΟ┐└┴┬├─┼ΠΡ╚╔╩╦╠═╬ΣΤΥΦΧΨΩαβγ┘┌█▄δε▀ζηθικλμνξοπρσςτ΄­±υφχ§ψ΅°¨ωϋΰώ■ "},cp874:{name:"Thai",languages:["th"],offset:128,chars:"€����…�����������‘’“”•–—�������� กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����"},cp1098:{name:"Farsi",languages:["fa"],offset:128,chars:"  ،؛؟ًآﺂاﺎءأﺄؤﺋبﺑﭖﭘتﺗثﺛجﺟﭺﭼ×حﺣخﺧدذرزﮊسﺳشﺷصﺻ«»░▒▓│┤ضﺿﻁﻃ╣║╗╝¤ﻅ┐└┴┬├─┼ﻇع╚╔╩╦╠═╬ ﻊﻋﻌغﻎﻏﻐفﻓ┘┌█▄قﻗ▀ﮎﻛﮒﮔلﻟمﻣنﻧوهﻫﻬﮤﯼ­ﯽﯾـ٠١٢٣٤٥٦٧٨٩■ "},cp1118:{name:"Lithuanian",languages:["lt"],offset:128,chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤ĄČĘĖ╣║╗╝ĮŠ┐└┴┬├─┼ŲŪ╚╔╩╦╠═╬Žąčęėįšųūž┘┌█▄▌▐▀αβΓπΣσµτΦΘΩδ∞φε⋂≡±≥≤„“÷≈°∙˙√ⁿ²■ "},cp1119:{name:"Lithuanian",languages:["lt"],offset:128,chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤ĄČĘĖ╣║╗╝ĮŠ┐└┴┬├─┼ŲŪ╚╔╩╦╠═╬Žąčęėįšųūž┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁё≥≤„“÷≈°∙·√ⁿ²■ "},cp1125:{name:"Ukrainian",languages:["uk"],offset:128,chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёҐґЄєІіЇї·√№¤■ "},cp1162:{name:"Thai",languages:["th"],offset:128,chars:"€…‘’“”•–— กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����"},cp2001:{name:"Lithuanian KBL or 771",languages:["lt"],offset:128,chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█ĄąČčрстуфхцчшщъыьэюяĘęĖėĮįŠšŲųŪūŽž■ "},cp3001:{name:"Estonian 1 or 1116",languages:["et"],offset:128,chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø×ƒáíóúñÑªº¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤šŠÊËÈıÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµžŽÚÛÙýÝ¯´­±‗¾¶§÷¸°¨·¹³²■ "},cp3002:{name:"Estonian 2",languages:["et"],offset:128,chars:" ¡¢£¤¥¦§¨©ª«¬­®‾°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏŠÑÒÓÔÕÖ×ØÙÚÛÜÝŽßàáâãäåæçèéêëìíîïšñòóôõö÷øùúûüýžÿ"},cp3011:{name:"Latvian 1",languages:["lv"],offset:128,chars:"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤Ā╢ņ╕╣║╗╝╜╛┐└┴┬├─┼ā╟╚╔╩╦╠═╬╧Š╤čČ╘╒ģĪī┘┌█▄ūŪ▀αßΓπΣσµτΦΘΩδ∞φε∩ĒēĢķĶļĻžŽ∙·√Ņš■ "},cp3012:{name:"Latvian 2 (modified 866)",languages:["lv"],offset:128,chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤Ā╢ņ╕╣║╗╝Ō╛┐└┴┬├─┼ā╟╚╔╩╦╠═╬╧Š╤čČ╘╒ģĪī┘┌█▄ūŪ▀рстуфхцчшщъыьэюяĒēĢķĶļĻžŽō·√Ņš■ "},cp3021:{name:"Bulgarian (MIK)",languages:["bg"],offset:128,chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя└┴┬├─┼╣║╚╔╩╦╠═╬┐░▒▓│┤№§╗╝┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp3041:{name:"Maltese ISO 646",languages:["mt"],offset:0,chars:"\0\b\t\n\v\f\r !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZġżħ^_ċabcdefghijklmnopqrstuvwxyzĠŻĦĊ"},cp3840:{name:"Russian (modified 866)",languages:["ru"],offset:128,chars:"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюя≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp3841:{name:"Ghost",languages:["ru"],offset:128,chars:"ғәёіїјҝөўүӽӈҹҷє£ҒӘЁІЇЈҜӨЎҮӼӇҸҶЄЪ !\"#$%&'()*+,-./0123456789:;<=>?юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧ∅"},cp3843:{name:"Polish (Mazovia)",languages:["pl"],offset:128,chars:"ÇüéâäàąçêëèïîćÄĄĘęłôöĆûùŚÖÜ¢Ł¥śƒŹŻóÓńŃźż¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp3844:{name:"Czech (Kamenický)",languages:["cz"],offset:128,chars:"ČüéďäĎŤčěĚĹÍľĺÄÁÉžŽôöÓůÚýÖÜŠĽÝŘťáíóúňŇŮÔšřŕŔ¼§«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp3845:{name:"Hungarian (CWI-2)",languages:["hu"],offset:128,chars:"ÇüéâäàåçêëèïîÍÄÁÉæÆőöÓűÚŰÖÜ¢£¥₧ƒáíóúñÑªŐ¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp3846:{name:"Turkish",languages:["tr"],offset:128,chars:"ÇüéâäàåçêëèïîıÄÅÉæÆôöòûùİÖÜ¢£¥ŞşáíóúñÑĞğ¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "},cp3847:{name:"Brazil ABNT",languages:["pt"],offset:256,chars:""},cp3848:{name:"Brazil ABICOMP",languages:["pt"],offset:160,chars:" ÀÁÂÃÄÇÈÉÊËÌÍÎÏÑÒÓÔÕÖŒÙÚÛÜŸ¨£¦§°¡àáâãäçèéêëìíîïñòóôõöœùúûüÿßªº¿±"},iso88591:{name:"Latin 1",languages:["en"],offset:128,chars:" ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ"},iso88592:{name:"Latin 2",languages:["hu","pl","cz"],offset:128,chars:" Ą˘Ł¤ĽŚ§¨ŠŞŤŹ­ŽŻ°ą˛ł´ľśˇ¸šşťź˝žżŔÁÂĂÄĹĆÇČÉĘËĚÍÎĎĐŃŇÓÔŐÖ×ŘŮÚŰÜÝŢßŕáâăäĺćçčéęëěíîďđńňóôőö÷řůúűüýţ˙"},iso88597:{name:"Greek",languages:["el"],offset:128,chars:" ‘’£€₯¦§¨©ͺ«¬­�―°±²³΄΅Ά·ΈΉΊ»Ό½ΎΏΐΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡ�ΣΤΥΦΧΨΩΪΫάέήίΰαβγδεζηθικλμνξοπρςστυφχψωϊϋόύώ�"},iso885915:{name:"Latin 9",languages:["fr"],offset:128,chars:" ¡¢£€¥Š§š©ª«¬­®¯°±²³Žµ¶·ž¹º»ŒœŸ¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ"},rk1048:{name:"Kazakh",languages:["kk"],offset:128,chars:"ЂЃ‚ѓ„…†‡€‰Љ‹ЊҚҺЏђ‘’“”•–—�™љ›њқһџ ҰұӘ¤Ө¦§Ё©Ғ«¬­®Ү°±Ііөµ¶·ё№ғ»әҢңүАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя"},windows1250:{name:"Latin 2",languages:["hu","pl","cz"],offset:128,chars:"€�‚�„…†‡�‰Š‹ŚŤŽŹ�‘’“”•–—�™š›śťžź ˇ˘Ł¤Ą¦§¨©Ş«¬­®Ż°±˛ł´µ¶·¸ąş»Ľ˝ľżŔÁÂĂÄĹĆÇČÉĘËĚÍÎĎĐŃŇÓÔŐÖ×ŘŮÚŰÜÝŢßŕáâăäĺćçčéęëěíîďđńňóôőö÷řůúűüýţ˙"},windows1251:{name:"Cyrillic",languages:["ru"],offset:128,chars:"ЂЃ‚ѓ„…†‡€‰Љ‹ЊЌЋЏђ‘’“”•–—�™љ›њќћџ ЎўЈ¤Ґ¦§Ё©Є«¬­®Ї°±Ііґµ¶·ё№є»јЅѕїАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя"},windows1252:{name:"Latin",languages:["fr"],offset:128,chars:"€�‚ƒ„…†‡ˆ‰Š‹Œ�Ž��‘’“”•–—˜™š›œ�žŸ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ"},windows1253:{name:"Greek",languages:["el"],offset:128,chars:"€�‚ƒ„…†‡�‰�‹�����‘’“”•–—�™�›���� ΅Ά£¤¥¦§¨©�«¬­®―°±²³΄µ¶·ΈΉΊ»Ό½ΎΏΐΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡ�ΣΤΥΦΧΨΩΪΫάέήίΰαβγδεζηθικλμνξοπρςστυφχψωϊϋόύώ�"},windows1254:{name:"Turkish",languages:["tr"],offset:128,chars:"€�‚ƒ„…†‡ˆ‰Š‹Œ����‘’“”•–—˜™š›œ��Ÿ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏĞÑÒÓÔÕÖ×ØÙÚÛÜİŞßàáâãäåæçèéêëìíîïğñòóôõö÷øùúûüışÿ"},windows1255:{name:"Hebrew",languages:["he"],offset:128,chars:"€�‚ƒ„…†‡ˆ‰�‹�����‘’“”•–—˜™�›���� ¡¢£₪¥¦§¨©×«¬­®¯°±²³´µ¶·¸¹÷»¼½¾¿ְֱֲֳִֵֶַָֹֺֻּֽ־ֿ׀ׁׂ׃װױײ׳״�������אבגדהוזחטיךכלםמןנסעףפץצקרשת��‎‏�"},windows1256:{name:"Arabic",languages:["ar"],offset:128,chars:"€پ‚ƒ„…†‡ˆ‰ٹ‹Œچژڈگ‘’“”•–—ک™ڑ›œ‌‍ں ،¢£¤¥¦§¨©ھ«¬­®¯°±²³´µ¶·¸¹؛»¼½¾؟ہءآأؤإئابةتثجحخدذرزسشصض×طظعغـفقكàلâمنهوçèéêëىيîïًٌٍَôُِ÷ّùْûü‎‏ے"},windows1257:{name:"Baltic Rim",languages:["et","lt"],offset:128,chars:"€�‚�„…†‡�‰�‹�¨ˇ¸�‘’“”•–—�™�›�¯˛� �¢£¤�¦§Ø©Ŗ«¬­®Æ°±²³´µ¶·ø¹ŗ»¼½¾æĄĮĀĆÄÅĘĒČÉŹĖĢĶĪĻŠŃŅÓŌÕÖ×ŲŁŚŪÜŻŽßąįāćäåęēčéźėģķīļšńņóōõö÷ųłśūüżž˙"},windows1258:{name:"Vietnamese",languages:["vi"],offset:128,chars:"€�‚ƒ„…†‡ˆ‰�‹Œ����‘’“”•–—˜™�›œ��Ÿ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂĂÄÅÆÇÈÉÊË̀ÍÎÏĐÑ̉ÓÔƠÖ×ØÙÚÛÜỮßàáâăäåæçèéêë́íîïđṇ̃óôơö÷øùúûüư₫ÿ"}},b={en:"The quick brown fox jumps over the lazy dog.",jp:"イロハニホヘト チリヌルヲ ワカヨタレソ ツネナラム",pt:"O próximo vôo à noite sobre o Atlântico, põe freqüentemente o único médico.",fr:"Les naïfs ægithales hâtifs pondant à Noël où il gèle sont sûrs d'être déçus en voyant leurs drôles d'œufs abîmés.",sv:"Flygande bäckasiner söka strax hwila på mjuka tuvor.",dk:"Quizdeltagerne spiste jordbær med fløde",el:"ξεσκεπάζω την ψυχοφθόρα βδελυγμία",tr:"Pijamalı hasta, yağız şoföre çabucak güvendi.",ru:"Съешь же ещё этих мягких французских булок да выпей чаю",hu:"Árvíztűrő tükörfúrógép",pl:"Pchnąć w tę łódź jeża lub ośm skrzyń fig",cz:"Mohu jíst sklo, neublíží mi.",ar:"أنا قادر على أكل الزجاج و هذا لا يؤلمني.",et:"Ma võin klaasi süüa, see ei tee mulle midagi.",lt:"Aš galiu valgyti stiklą ir jis manęs nežeidžia.",bg:"Мога да ям стъкло, то не ми вреди.",is:"Ég get etið gler án þess að meiða mig.",he:"אני יכול לאכול זכוכית וזה לא מזיק לי.",fa:".من می توانم بدونِ احساس درد شيشه بخورم",uk:"Я можу їсти скло, і воно мені не зашкодить.",vi:"Tôi có thể ăn thủy tinh mà không hại gì.",kk:"қазақша",lv:"Es varu ēst stiklu, tas man nekaitē.",mt:"Nista' niekol il-ħġieġ u ma jagħmilli xejn.",th:"ฉันกินกระจกได้ แต่มันไม่ทำให้ฉันเจ็บ"};class m{static getEncodings(){return Object.keys(_)}static getTestStrings(e){return void 0!==_[e]&&void 0!==_[e].languages?_[e].languages.map((e=>({language:e,string:b[e]}))):[]}static supports(e){return void 0!==_[e]&&void 0!==_[e].chars}static encode(e,t){const i=new Uint8Array(e.length);let s="\0".repeat(128),n=128;void 0!==_[t]&&void 0!==_[t].chars&&(s=_[t].chars,n=_[t].offset);for(let t=0;t<e.length;t++){const r=e.codePointAt(t);if(r<128)i[t]=r;else{const a=s.indexOf(e[t]);-1!==a?i[t]=n+a:r<256&&(r<n||r>=n+s.length)?i[t]=r:i[t]=63}}return i}static autoEncode(e,t){const i=[];let s,n=-1;for(let r=0;r<e.length;r++){const a=e.codePointAt(r);let o,h=0;if(a<128&&(o=s||t[0],h=a),!o&&s){const t=_[s].chars.indexOf(e[r]);-1!==t&&(o=s,h=_[s].offset+t)}if(!o)for(let i=0;i<t.length;i++){const s=_[t[i]].chars.indexOf(e[r]);if(-1!==s){o=t[i],h=_[t[i]].offset+s;break}}o||(o=s||t[0],h=63),s!=o&&(s&&(i[n].bytes=new Uint8Array(i[n].bytes)),n++,i[n]={codepage:o,bytes:[]},s=o),i[n].bytes.push(h)}return s&&(i[n].bytes=new Uint8Array(i[n].bytes)),i}}const y={epson:{cp437:0,shiftjis:1,cp850:2,cp860:3,cp863:4,cp865:5,cp851:11,cp853:12,cp857:13,cp737:14,iso88597:15,windows1252:16,cp866:17,cp852:18,cp858:19,cp720:32,cp775:33,cp855:34,cp861:35,cp862:36,cp864:37,cp869:38,iso88592:39,iso885915:40,cp1098:41,cp1118:42,cp1119:43,cp1125:44,windows1250:45,windows1251:46,windows1253:47,windows1254:48,windows1255:49,windows1256:50,windows1257:51,windows1258:52,rk1048:53},zjiang:{cp437:0,shiftjis:1,cp850:2,cp860:3,cp863:4,cp865:5,windows1252:16,cp866:17,cp852:18,cp858:19,windows1255:32,cp861:56,cp855:60,cp857:61,cp862:62,cp864:63,cp737:64,cp851:65,cp869:66,cp1119:68,cp1118:69,windows1250:72,windows1251:73,cp3840:74,cp3843:76,cp3844:77,cp3845:78,cp3846:79,cp3847:80,cp3848:81,cp2001:83,cp3001:84,cp3002:85,cp3011:86,cp3012:87,cp3021:88,cp3041:89,windows1253:90,windows1254:91,windows1256:92,cp720:93,windows1258:94,cp775:95},bixolon:{cp437:0,shiftjis:1,cp850:2,cp860:3,cp863:4,cp865:5,cp851:11,cp858:19},star:{cp437:0,shiftjis:1,cp850:2,cp860:3,cp863:4,cp865:5,windows1252:16,cp866:17,cp852:18,cp858:19},citizen:{cp437:0,shiftjis:1,cp850:2,cp860:3,cp863:4,cp865:5,cp852:18,cp866:17,cp857:8,windows1252:16,cp858:19,cp864:40},legacy:{cp437:0,cp737:64,cp850:2,cp775:95,cp852:18,cp855:60,cp857:61,cp858:19,cp860:3,cp861:56,cp862:62,cp863:4,cp864:28,cp865:5,cp866:17,cp869:66,cp936:255,cp949:253,cp950:254,cp1252:16,iso88596:22,shiftjis:252,windows874:30,windows1250:72,windows1251:73,windows1252:71,windows1253:90,windows1254:91,windows1255:32,windows1256:92,windows1257:25,windows1258:94}};class v{constructor(e){this._reset(e)}_reset(e){this._options=Object.assign({width:null,embedded:!1,wordWrap:!0,imageMode:"column",codepageMapping:"epson",codepageCandidates:["cp437","cp858","cp860","cp861","cp863","cp865","cp852","cp857","cp855","cp866","cp869"]},e),this._embedded=this._options.width&&this._options.embedded,this._buffer=[],this._queued=[],this._cursor=0,this._codepage="ascii",this._state={codepage:0,align:"left",bold:!1,italic:!1,underline:!1,invert:!1,width:1,height:1}}_encode(e){if("auto"!=this._codepage)return m.encode(e,this._codepage);let t;t="string"==typeof this._options.codepageMapping?y[this._options.codepageMapping]:this._options.codepageMapping;const i=m.autoEncode(e,this._options.codepageCandidates);let s=0;for(let e=0;e<i.length;e++)s+=3+i[e].bytes.byteLength;const n=new Uint8Array(s);let r=0;for(let e=0;e<i.length;e++)n.set([27,116,t[i[e].codepage]],r),n.set(i[e].bytes,r+3),r+=3+i[e].bytes.byteLength,this._state.codepage=t[i[e].codepage];return n}_queue(e){e.forEach((e=>this._queued.push(e)))}_flush(){if(this._embedded){let e=this._options.width-this._cursor;if("left"==this._state.align&&this._queued.push(new Array(e).fill(32)),"center"==this._state.align){const t=e%2;e>>=1,e>0&&this._queued.push(new Array(e).fill(32)),e+t>0&&this._queued.unshift(new Array(e+t).fill(32))}"right"==this._state.align&&this._queued.unshift(new Array(e).fill(32))}this._buffer=this._buffer.concat(this._queued),this._queued=[],this._cursor=0}_wrap(e,t){if(t||this._options.wordWrap&&this._options.width){const i="-".repeat(this._cursor);return u(t||this._options.width,{lineBreak:"\n",whitespace:"all"})(i+e).substring(this._cursor).split("\n")}return[e]}_restoreState(){this.bold(this._state.bold),this.italic(this._state.italic),this.underline(this._state.underline),this.invert(this._state.invert),this._queue([27,116,this._state.codepage])}_getCodepageIdentifier(e){let t;return t="string"==typeof this._options.codepageMapping?y[this._options.codepageMapping]:this._options.codepageMapping,t[e]}initialize(){return this._queue([27,64]),this._flush(),this}codepage(e){if("auto"===e)return this._codepage=e,this;if(!m.supports(e))throw new Error("Unknown codepage");let t;if(t="string"==typeof this._options.codepageMapping?y[this._options.codepageMapping]:this._options.codepageMapping,void 0===t[e])throw new Error("Codepage not supported by printer");return this._codepage=e,this._state.codepage=t[e],this._queue([27,116,t[e]]),this}text(e,t){const i=this._wrap(e,t);for(let e=0;e<i.length;e++){const t=this._encode(i[e]);this._queue([t]),this._cursor+=i[e].length*this._state.width,this._options.width&&!this._embedded&&(this._cursor=this._cursor%this._options.width),e<i.length-1&&this.newline()}return this}newline(){return this._flush(),this._queue([10,13]),this._embedded&&this._restoreState(),this}line(e,t){return this.text(e,t),this.newline(),this}underline(e){return void 0===e&&(e=!this._state.underline),this._state.underline=e,this._queue([27,45,Number(e)]),this}italic(e){return void 0===e&&(e=!this._state.italic),this._state.italic=e,this._queue([27,52,Number(e)]),this}bold(e){return void 0===e&&(e=!this._state.bold),this._state.bold=e,this._queue([27,69,Number(e)]),this}width(e){if(void 0===e&&(e=1),"number"!=typeof e)throw new Error("Width must be a number");if(e<1||e>8)throw new Error("Width must be between 1 and 8");return this._state.width=e,this._queue([29,33,this._state.height-1|this._state.width-1<<4]),this}height(e){if(void 0===e&&(e=1),"number"!=typeof e)throw new Error("Height must be a number");if(e<1||e>8)throw new Error("Height must be between 1 and 8");return this._state.height=e,this._queue([29,33,this._state.height-1|this._state.width-1<<4]),this}invert(e){return void 0===e&&(e=!this._state.invert),this._state.invert=e,this._queue([29,66,Number(e)]),this}size(e){return e="small"===e?1:0,this._queue([27,77,e]),this}align(e){const t={left:0,center:1,right:2};if(!(e in t))throw new Error("Unknown alignment");return this._state.align=e,this._embedded||this._queue([27,97,t[e]]),this}table(e,t){0!=this._cursor&&this.newline();for(let i=0;i<t.length;i++){const s=[];let n=0;for(let r=0;r<e.length;r++){const a=[];if("string"==typeof t[i][r]){const s=u(e[r].width,{lineBreak:"\n"})(t[i][r]).split("\n");for(let t=0;t<s.length;t++)"right"==e[r].align?a[t]=this._encode(s[t].padStart(e[r].width)):a[t]=this._encode(s[t].padEnd(e[r].width))}if("function"==typeof t[i][r]){const s=new v(Object.assign({},this._options,{width:e[r].width,embedded:!0}));s._codepage=this._codepage,s.align(e[r].align),t[i][r](s);const n=s.encode();let o=[];for(let e=0;e<n.byteLength;e++)e<n.byteLength-1&&10===n[e]&&13===n[e+1]?(a.push(o),o=[],e++):o.push(n[e]);o.length&&a.push(o)}n=Math.max(n,a.length),s[r]=a}for(let t=0;t<e.length;t++)if(s[t].length<n)for(let i=s[t].length;i<n;i++){let i="top";void 0!==e[t].verticalAlign&&(i=e[t].verticalAlign),"bottom"==i?s[t].unshift(new Array(e[t].width).fill(32)):s[t].push(new Array(e[t].width).fill(32))}for(let t=0;t<n;t++){for(let i=0;i<e.length;i++)void 0!==e[i].marginLeft&&this.raw(new Array(e[i].marginLeft).fill(32)),this.raw(s[i][t]),void 0!==e[i].marginRight&&this.raw(new Array(e[i].marginRight).fill(32));this.newline()}}return this}rule(e){return e=Object.assign({style:"single",width:this._options.width||10},e||{}),this._queue([27,116,this._getCodepageIdentifier("cp437"),new Array(e.width).fill("double"===e.style?205:196)]),this._queue([27,116,this._state.codepage]),this.newline(),this}box(e,t){let i;i="double"==(e=Object.assign({style:"single",width:this._options.width||30,marginLeft:0,marginRight:0,paddingLeft:0,paddingRight:0},e||{})).style?[201,187,200,188,205,186]:[218,191,192,217,196,179],0!=this._cursor&&this.newline(),this._restoreState(),this._queue([27,116,this._getCodepageIdentifier("cp437")]),this._queue([new Array(e.marginLeft).fill(32),i[0],new Array(e.width-2).fill(i[4]),i[1],new Array(e.marginRight).fill(32)]),this.newline();const s=[];if("string"==typeof t){const i=u(e.width-2-e.paddingLeft-e.paddingRight,{lineBreak:"\n"})(t).split("\n");for(let t=0;t<i.length;t++)"right"==e.align?s[t]=this._encode(i[t].padStart(e.width-2-e.paddingLeft-e.paddingRight)):s[t]=this._encode(i[t].padEnd(e.width-2-e.paddingLeft-e.paddingRight))}if("function"==typeof t){const i=new v(Object.assign({},this._options,{width:e.width-2-e.paddingLeft-e.paddingRight,embedded:!0}));i._codepage=this._codepage,i.align(e.align),t(i);const n=i.encode();let r=[];for(let e=0;e<n.byteLength;e++)e<n.byteLength-1&&10===n[e]&&13===n[e+1]?(s.push(r),r=[],e++):r.push(n[e]);r.length&&s.push(r)}for(let t=0;t<s.length;t++)this._queue([new Array(e.marginLeft).fill(32),i[5],new Array(e.paddingLeft).fill(32)]),this._queue([s[t]]),this._restoreState(),this._queue([27,116,this._getCodepageIdentifier("cp437")]),this._queue([new Array(e.paddingRight).fill(32),i[5],new Array(e.marginRight).fill(32)]),this.newline();return this._queue([new Array(e.marginLeft).fill(32),i[2],new Array(e.width-2).fill(i[4]),i[3],new Array(e.marginRight).fill(32)]),this._restoreState(),this.newline(),this}barcode(e,t,i){if(this._embedded)throw new Error("Barcodes are not supported in table cells or boxes");const s={upca:0,upce:1,ean13:2,ean8:3,code39:4,coda39:4,itf:5,codabar:6,code93:72,code128:73,"gs1-128":80,"gs1-databar-omni":81,"gs1-databar-truncated":82,"gs1-databar-limited":83,"gs1-databar-expanded":84,"code128-auto":85};if(!(t in s))throw new Error("Symbology not supported by printer");{const n=m.encode(e,"ascii");0!=this._cursor&&this.newline(),this._queue([29,104,i,29,119,"code39"===t?2:3]),"code128"==t&&123!==n[0]?this._queue([29,107,s[t],n.length+2,123,66,n]):s[t]>64?this._queue([29,107,s[t],n.length,n]):this._queue([29,107,s[t],n,0])}return this._flush(),this}qrcode(e,t,i,s){if(this._embedded)throw new Error("QR codes are not supported in table cells or boxes");this._queue([10]);const n={1:49,2:50};if(void 0===t&&(t=2),!(t in n))throw new Error("Model must be 1 or 2");if(this._queue([29,40,107,4,0,49,65,n[t],0]),void 0===i&&(i=6),"number"!=typeof i)throw new Error("Size must be a number");if(i<1||i>8)throw new Error("Size must be between 1 and 8");this._queue([29,40,107,3,0,49,67,i]);const r={l:48,m:49,q:50,h:51};if(void 0===s&&(s="m"),!(s in r))throw new Error("Error level must be l, m, q or h");this._queue([29,40,107,3,0,49,69,r[s]]);const a=m.encode(e,"iso88591"),o=a.length+3;return this._queue([29,40,107,o%255,o/255,49,80,48,a]),this._queue([29,40,107,3,0,49,81,48]),this._flush(),this}image(e,t,i,s,n){if(this._embedded)throw new Error("Images are not supported in table cells or boxes");if(t%8!=0)throw new Error("Width must be a multiple of 8");if(i%8!=0)throw new Error("Height must be a multiple of 8");void 0===s&&(s="threshold"),void 0===n&&(n=128);const r=g(t,i).getContext("2d");r.drawImage(e,0,0,t,i);let a=r.getImageData(0,0,t,i);switch(a=w.flatten(a,[255,255,255]),s){case"threshold":a=f.threshold(a,n);break;case"bayer":a=f.bayer(a,n);break;case"floydsteinberg":a=f.floydsteinberg(a);break;case"atkinson":a=f.atkinson(a)}const o=(e,s)=>e<t&&s<i?a.data[4*(t*s+e)]>0?0:1:0,h=(e,t)=>{const i=new Uint8Array(e*t>>3);for(let s=0;s<t;s++)for(let t=0;t<e;t+=8)for(let n=0;n<8;n++)i[s*(e>>3)+(t>>3)]|=o(t+n,s)<<7-n;return i};return 0!=this._cursor&&this.newline(),"column"==this._options.imageMode&&(this._queue([27,51,36]),((e,t)=>{const i=[];for(let s=0;s<Math.ceil(t/24);s++){const t=new Uint8Array(3*e);for(let i=0;i<e;i++)for(let e=0;e<3;e++)for(let n=0;n<8;n++)t[3*i+e]|=o(i,24*s+n+8*e)<<7-n;i.push(t)}return i})(t,i).forEach((e=>{this._queue([27,42,33,255&t,t>>8&255,e,10])})),this._queue([27,50])),"raster"==this._options.imageMode&&this._queue([29,118,48,0,t>>3&255,t>>3>>8&255,255&i,i>>8&255,h(t,i)]),this._flush(),this}cut(e){if(this._embedded)throw new Error("Cut is not supported in table cells or boxes");let t=0;return"partial"==e&&(t=1),this._queue([29,86,t]),this}pulse(e,t,i){if(this._embedded)throw new Error("Pulse is not supported in table cells or boxes");return void 0===e&&(e=0),void 0===t&&(t=100),void 0===i&&(i=500),t=Math.min(500,Math.round(t/2)),i=Math.min(500,Math.round(i/2)),this._queue([27,112,e?1:0,255&t,255&i]),this}raw(e){return this._queue(e),this}encode(){this._flush();let e=0;this._buffer.forEach((t=>{"number"==typeof t?e++:e+=t.length}));const t=new Uint8Array(e);let i=0;return this._buffer.forEach((e=>{"number"==typeof e?(t[i]=e,i++):(t.set(e,i),i+=e.length)})),this._reset(),t}}const k={star:{cp437:1,cp858:4,cp852:5,cp860:6,cp861:7,cp863:8,cp865:9,cp866:10,cp855:11,cp857:12,cp862:13,cp864:14,cp737:15,cp869:17,cp874:20,windows1252:32,windows1250:33,windows1251:34}};class q{constructor(e){this._reset(e)}_reset(e){this._options=Object.assign({width:null,embedded:!1,wordWrap:!0,codepageMapping:"star",codepageCandidates:["cp437","cp858","cp860","cp861","cp863","cp865","cp852","cp857","cp855","cp866","cp869"]},e),this._embedded=this._options.width&&this._options.embedded,this._buffer=[],this._queued=[],this._cursor=0,this._codepage="ascii",this._state={codepage:0,align:"left",bold:!1,italic:!1,underline:!1,invert:!1,width:1,height:1}}_encode(e){if("auto"!=this._codepage)return m.encode(e,this._codepage);let t;t="string"==typeof this._options.codepageMapping?k[this._options.codepageMapping]:this._options.codepageMapping;const i=m.autoEncode(e,this._options.codepageCandidates);let s=0;for(let e=0;e<i.length;e++)s+=4+i[e].bytes.byteLength;const n=new Uint8Array(s);let r=0;for(let e=0;e<i.length;e++)n.set([27,29,116,t[i[e].codepage]],r),n.set(i[e].bytes,r+4),r+=4+i[e].bytes.byteLength,this._state.codepage=t[i[e].codepage];return n}_queue(e){e.forEach((e=>this._queued.push(e)))}_flush(){if(this._embedded){let e=this._options.width-this._cursor;if("left"==this._state.align&&this._queued.push(new Array(e).fill(32)),"center"==this._state.align){const t=e%2;e>>=1,e>0&&this._queued.push(new Array(e).fill(32)),e+t>0&&this._queued.unshift(new Array(e+t).fill(32))}"right"==this._state.align&&this._queued.unshift(new Array(e).fill(32))}this._buffer=this._buffer.concat(this._queued),this._queued=[],this._cursor=0}_wrap(e,t){if(t||this._options.wordWrap&&this._options.width){const i="-".repeat(this._cursor);return u(t||this._options.width,{lineBreak:"\n",whitespace:"all"})(i+e).substring(this._cursor).split("\n")}return[e]}_restoreState(){this.bold(this._state.bold),this.italic(this._state.italic),this.underline(this._state.underline),this.invert(this._state.invert),this._queue([27,29,116,this._state.codepage])}_getCodepageIdentifier(e){let t;return t="string"==typeof this._options.codepageMapping?k[this._options.codepageMapping]:this._options.codepageMapping,t[e]}initialize(){return this._queue([27,64,24]),this._flush(),this}codepage(e){if("auto"===e)return this._codepage=e,this;if(!m.supports(e))throw new Error("Unknown codepage");let t;if(t="string"==typeof this._options.codepageMapping?k[this._options.codepageMapping]:this._options.codepageMapping,void 0===t[e])throw new Error("Codepage not supported by printer");return this._codepage=e,this._state.codepage=t[e],this._queue([27,29,116,t[e]]),this}text(e,t){const i=this._wrap(e,t);for(let e=0;e<i.length;e++){const t=this._encode(i[e]);this._queue([t]),this._cursor+=i[e].length*this._state.width,this._options.width&&!this._embedded&&(this._cursor=this._cursor%this._options.width),e<i.length-1&&this.newline()}return this}newline(){return this._flush(),this._queue([10,13]),this._embedded&&this._restoreState(),this}line(e,t){return this.text(e,t),this.newline(),this}underline(e){return void 0===e&&(e=!this._state.underline),this._state.underline=e,this._queue([27,45,Number(e)]),this}italic(e){return this}bold(e){return void 0===e&&(e=!this._state.bold),this._state.bold=e,e?this._queue([27,69]):this._queue([27,70]),this}width(e){if(void 0===e&&(e=1),"number"!=typeof e)throw new Error("Width must be a number");if(e<1||e>6)throw new Error("Width must be between 1 and 6");return this._state.width=e,this._queue([27,105,this._state.height-1,this._state.width-1]),this}height(e){if(void 0===e&&(e=1),"number"!=typeof e)throw new Error("Height must be a number");if(e<1||e>6)throw new Error("Height must be between 1 and 6");return this._state.height=e,this._queue([27,105,this._state.height-1,this._state.width-1]),this}invert(e){return void 0===e&&(e=!this._state.invert),this._state.invert=e,this._queue([27,e?52:53]),this}size(e){return e="smaller"===e?2:"small"===e?1:0,this._queue([27,30,70,e]),this}align(e){const t={left:0,center:1,right:2};if(!(e in t))throw new Error("Unknown alignment");return this._state.align=e,this._embedded||this._queue([27,29,97,t[e]]),this}table(e,t){0!=this._cursor&&this.newline();for(let i=0;i<t.length;i++){const s=[];let n=0;for(let r=0;r<e.length;r++){const a=[];if("string"==typeof t[i][r]){const s=u(e[r].width,{lineBreak:"\n"})(t[i][r]).split("\n");for(let t=0;t<s.length;t++)"right"==e[r].align?a[t]=this._encode(s[t].padStart(e[r].width)):a[t]=this._encode(s[t].padEnd(e[r].width))}if("function"==typeof t[i][r]){const s=new q(Object.assign({},this._options,{width:e[r].width,embedded:!0}));s._codepage=this._codepage,s.align(e[r].align),t[i][r](s);const n=s.encode();let o=[];for(let e=0;e<n.byteLength;e++)e<n.byteLength-1&&10===n[e]&&13===n[e+1]?(a.push(o),o=[],e++):o.push(n[e]);o.length&&a.push(o)}n=Math.max(n,a.length),s[r]=a}for(let t=0;t<e.length;t++)if(s[t].length<n)for(let i=s[t].length;i<n;i++){let i="top";void 0!==e[t].verticalAlign&&(i=e[t].verticalAlign),"bottom"==i?s[t].unshift(new Array(e[t].width).fill(32)):s[t].push(new Array(e[t].width).fill(32))}for(let t=0;t<n;t++){for(let i=0;i<e.length;i++)void 0!==e[i].marginLeft&&this.raw(new Array(e[i].marginLeft).fill(32)),this.raw(s[i][t]),void 0!==e[i].marginRight&&this.raw(new Array(e[i].marginRight).fill(32));this.newline()}}return this}rule(e){return e=Object.assign({style:"single",width:this._options.width||10},e||{}),this._queue([27,29,116,this._getCodepageIdentifier("cp437"),new Array(e.width).fill("double"===e.style?205:196)]),this._queue([27,29,116,this._state.codepage]),this.newline(),this}box(e,t){let i;i="double"==(e=Object.assign({style:"single",width:this._options.width||30,marginLeft:0,marginRight:0,paddingLeft:0,paddingRight:0},e||{})).style?[201,187,200,188,205,186]:[218,191,192,217,196,179],0!=this._cursor&&this.newline(),this._restoreState(),this._queue([27,29,116,this._getCodepageIdentifier("cp437")]),this._queue([new Array(e.marginLeft).fill(32),i[0],new Array(e.width-2).fill(i[4]),i[1],new Array(e.marginRight).fill(32)]),this.newline();const s=[];if("string"==typeof t){const i=u(e.width-2-e.paddingLeft-e.paddingRight,{lineBreak:"\n"})(t).split("\n");for(let t=0;t<i.length;t++)"right"==e.align?s[t]=this._encode(i[t].padStart(e.width-2-e.paddingLeft-e.paddingRight)):s[t]=this._encode(i[t].padEnd(e.width-2-e.paddingLeft-e.paddingRight))}if("function"==typeof t){const i=new q(Object.assign({},this._options,{width:e.width-2-e.paddingLeft-e.paddingRight,embedded:!0}));i._codepage=this._codepage,i.align(e.align),t(i);const n=i.encode();let r=[];for(let e=0;e<n.byteLength;e++)e<n.byteLength-1&&10===n[e]&&13===n[e+1]?(s.push(r),r=[],e++):r.push(n[e]);r.length&&s.push(r)}for(let t=0;t<s.length;t++)this._queue([new Array(e.marginLeft).fill(32),i[5],new Array(e.paddingLeft).fill(32)]),this._queue([s[t]]),this._restoreState(),this._queue([27,29,116,this._getCodepageIdentifier("cp437")]),this._queue([new Array(e.paddingRight).fill(32),i[5],new Array(e.marginRight).fill(32)]),this.newline();return this._queue([new Array(e.marginLeft).fill(32),i[2],new Array(e.width-2).fill(i[4]),i[3],new Array(e.marginRight).fill(32)]),this._restoreState(),this.newline(),this}barcode(e,t,i){if(this._embedded)throw new Error("Barcodes are not supported in table cells or boxes");const s={upce:0,upca:1,ean8:2,ean13:3,code39:4,itf:5,code128:6,code93:7,"nw-7":8,"gs1-128":9,"gs1-databar-omni":10,"gs1-databar-truncated":11,"gs1-databar-limited":12,"gs1-databar-expanded":13};if(!(t in s))throw new Error("Symbology not supported by printer");{const n=m.encode(e,"ascii");this._queue([27,98,s[t],1,3,i,n,30])}return this._flush(),this}qrcode(e,t,i,s){if(this._embedded)throw new Error("QR codes are not supported in table cells or boxes");this._queue([10]);const n={1:1,2:2};if(void 0===t&&(t=2),!(t in n))throw new Error("Model must be 1 or 2");if(this._queue([27,29,121,83,48,n[t]]),void 0===i&&(i=6),"number"!=typeof i)throw new Error("Size must be a number");if(i<1||i>8)throw new Error("Size must be between 1 and 8");this._queue([27,29,121,83,50,i]);const r={l:0,m:1,q:2,h:3};if(void 0===s&&(s="m"),!(s in r))throw new Error("Error level must be l, m, q or h");this._queue([27,29,121,83,49,r[s]]);const a=m.encode(e,"iso88591"),o=a.length;return this._queue([27,29,121,68,49,0,o%255,o/255,a]),this._queue([27,29,121,80]),this._flush(),this}image(e,t,i,s,n){if(this._embedded)throw new Error("Images are not supported in table cells or boxes");if(t%8!=0)throw new Error("Width must be a multiple of 8");if(i%24!=0)throw new Error("Height must be a multiple of 24");void 0===s&&(s="threshold"),void 0===n&&(n=128);const r=g(t,i).getContext("2d");r.drawImage(e,0,0,t,i);let a=r.getImageData(0,0,t,i);switch(a=w.flatten(a,[255,255,255]),s){case"threshold":a=f.threshold(a,n);break;case"bayer":a=f.bayer(a,n);break;case"floydsteinberg":a=f.floydsteinberg(a);break;case"atkinson":a=f.atkinson(a)}const o=(e,i)=>a.data[4*(t*i+e)]>0?0:1;this._queue([27,48]);for(let e=0;e<i/24;e++){const i=24*e,s=new Uint8Array(3*t);for(let e=0;e<t;e++){const t=3*e;s[t]=o(e,i+0)<<7|o(e,i+1)<<6|o(e,i+2)<<5|o(e,i+3)<<4|o(e,i+4)<<3|o(e,i+5)<<2|o(e,i+6)<<1|o(e,i+7),s[t+1]=o(e,i+8)<<7|o(e,i+9)<<6|o(e,i+10)<<5|o(e,i+11)<<4|o(e,i+12)<<3|o(e,i+13)<<2|o(e,i+14)<<1|o(e,i+15),s[t+2]=o(e,i+16)<<7|o(e,i+17)<<6|o(e,i+18)<<5|o(e,i+19)<<4|o(e,i+20)<<3|o(e,i+21)<<2|o(e,i+22)<<1|o(e,i+23)}this._queue([27,88,255&t,t>>8&255,s,10,13])}return this._queue([27,122,1]),this._flush(),this}cut(e){if(this._embedded)throw new Error("Cut is not supported in table cells or boxes");let t=0;return"partial"==e&&(t=1),this._queue([27,100,t]),this}pulse(e,t,i){if(this._embedded)throw new Error("Pulse is not supported in table cells or boxes");return void 0===e&&(e=0),void 0===t&&(t=200),void 0===i&&(i=200),t=Math.min(127,Math.round(t/10)),i=Math.min(127,Math.round(i/10)),this._queue([27,7,255&t,255&i,e?26:7]),this}raw(e){return this._queue(e),this}encode(){this._flush();let e=0;this._buffer.forEach((t=>{"number"==typeof t?e++:e+=t.length}));const t=new Uint8Array(e);let i=0;return this._buffer.forEach((e=>{"number"==typeof e?(t[i]=e,i++):(t.set(e,i),i+=e.length)})),this._reset(),t}}class E{constructor(e){const t={"esc-pos":v,"star-prnt":q};if(void 0===e||void 0===e.language)throw new Error("You need to specify the language of the thermal printer");if(void 0===t[e.language])throw new Error("Language not supported by this library");this.language=e.language;const i=t[this.language].prototype;Object.getOwnPropertyNames(i).forEach((e=>{this[e]=i[e]})),this._reset(e)}}export{E as default};
//# sourceMappingURL=/sm/84c71870203e4e9b70b6f274c12afb5a0a85bea6c3462538a430f1b3622b8501.map