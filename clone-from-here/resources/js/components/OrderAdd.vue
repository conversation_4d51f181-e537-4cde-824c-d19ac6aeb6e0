<template>
    <div>
        <!-- STORE_ID -->
        <div class="fixed top-0 left-0 right-0 z-40 pointer-events-none">
            <div class="max-w-xl mx-auto pointer-events-none">
                <div
                    class="relative flex items-center px-4 text-white h-14 pointer-events-none"
                >
                    <select
                        id="input.store_id"
                        name="input.store_id"
                        v-model="input.store_id"
                        class="p-0 ml-auto text-sm text-right font-bold bg-transparent border-none pointer-events-auto"
                    >
                        <option
                            v-for="option in stores"
                            :value="option.id.toString()"
                        >
                            {{ option.name }}
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <div class="flex flex-col gap-5 px-5 pt-20 pb-20">
            <!-- TANGGAL JAM (CREATED_AT) -->
            <label class="block relative">
                <div
                    ref="scrollview-created_at"
                    class="absolute left-0 -top-16"
                />
                <div class="font-bold text-gray-900">
                    Tanggal & Jam <span class="text-red-600">*</span>
                </div>
                <input
                    ref="input-created_at"
                    name="created_at"
                    type="datetime-local"
                    required
                    value=""
                    @input="() => removeError('created_at')"
                    v-model="input.created_at"
                    :readonly="[4, 6].includes(current_role_id)"
                    :min="generateDateTime()"
                    :max="generateDateTime(7)"
                    :class="[
                        'block w-full mt-1 rounded-md shadow-sm focus:ring',
                        {
                            'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                !temp.errors.hasOwnProperty('created_at'),
                        },
                        {
                            'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                temp.errors.hasOwnProperty('created_at'),
                        },
                        { 'bg-gray-100': [4, 6].includes(current_role_id) },
                    ]"
                    :disabled="temp.isLoadingSubmit"
                />
                <p
                    v-if="temp.errors.hasOwnProperty('created_at')"
                    class="text-sm text-red-600"
                >
                    {{ temp.errors.created_at[0] }}
                </p>
            </label>

            <!-- JOB TYPE (OLD/NEW) -->
            <div class="block">
                <div class="font-bold text-gray-900">
                    Tipe Job <span class="text-red-600">*</span>
                </div>
                <div class="flex flex-col gap-2 mt-1.5">
                    <template v-if="![4, 6].includes(current_role_id)">
                        <label class="inline-flex items-center">
                            <input
                                class=""
                                type="radio"
                                name="order_type"
                                id="order_type"
                                v-model="input.order_type"
                                required
                                value="old"
                                :disabled="temp.isLoadingSubmit"
                            />
                            <span class="ml-2">👤 Pelanggan Lama</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input
                                class=""
                                type="radio"
                                name="order_type"
                                id="order_type"
                                v-model="input.order_type"
                                required
                                value="new"
                            />
                            <span class="ml-2">🆕 Pelanggan Baru</span>
                        </label>
                        <label
                            class="inline-flex items-center"
                            v-if="current_role_id <= 3"
                        >
                            <input
                                class=""
                                type="radio"
                                name="order_type"
                                id="order_type"
                                v-model="input.order_type"
                                required
                                value="marketing"
                            />
                            <span class="ml-2">📢 Bagi Brosur</span>
                        </label>
                    </template>
                    <template v-if="[1, 2, 3, 4, 6].includes(current_role_id)">
                        <label class="inline-flex items-center">
                            <input
                                class=""
                                type="radio"
                                name="order_type"
                                id="order_type"
                                v-model="input.order_type"
                                required
                                value="asdt"
                            />
                            <span class="ml-2">🛒 ASDT</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input
                                class=""
                                type="radio"
                                name="order_type"
                                id="order_type"
                                v-model="input.order_type"
                                required
                                value="konsumsitoko"
                            />
                            <span class="ml-2">🏬 Konsumsi Toko</span>
                        </label>
                    </template>
                </div>
            </div>

            <!-- PELANGGAN LAMA -->
            <template v-if="input.order_type === 'old'">
                <!-- CUSTOMER (ADDRESS_ID) -->
                <label
                    class="relative block"
                    for="address_id"
                    :style="{
                        paddingBottom: !input.address_id ? '700px' : '0',
                    }"
                >
                    <div
                        class="bg-gradient-to-b flex justify-center items-end p-5 text-white text-sm rounded-md from-transparent to-red-300 absolute left-0 right-0 bottom-0 top-20 pointer-events-none"
                    >
                        Scroll up to input.
                    </div>
                    <div
                        id="input_customer_address"
                        class="absolute -top-16"
                    ></div>
                    <div class="font-bold text-gray-900">
                        Pelanggan <span class="text-red-600">*</span>
                    </div>
                    <!-- TODO: Harusnya posisi sudah terpilih, jika buka dropdown akan terselec yg sudah dipilih dan bisa scroll k bawah otomatis. Belom bisa pake prop :clearSearchOnSelect=false -->
                    <v-select
                        class="mt-1"
                        :required="!input.address_id"
                        v-model="temp.valueCustomerAddress"
                        :filterable="false"
                        :clearable="false"
                        placeholder="Cari nama / no. wa / alamat ..."
                        :options="temp.optionsCustomerAddress"
                        :selectOnTab="true"
                        @search="onSearchCustomerAddress"
                        @open="
                            document
                                .getElementById('input_customer_address')
                                .scrollIntoView()
                        "
                        :transition="''"
                        :disabled="temp.isLoadingSubmit"
                    >
                        <template slot="no-options">
                            {{
                                temp.keywordCustomerAddress.length >= 2
                                    ? "Maaf, data tidak ditemukan."
                                    : ""
                            }}
                        </template>
                        <template slot="option" slot-scope="option">
                            <customer-address-item
                                :option="option"
                                :keyword="temp.keywordCustomerAddress"
                                :withBorder="true"
                            />
                        </template>
                        <template slot="selected-option" slot-scope="option">
                            <customer-address-item
                                :option="option"
                                :keyword="temp.keywordCustomerAddress"
                            />
                        </template>
                    </v-select>
                </label>

                <!-- IS_HIDE_DEPOSIT -->
                <label class="inline-flex items-center" v-if="input.address_id">
                    <input
                        type="checkbox"
                        name="is_hide_deposit"
                        v-model="input.is_hide_deposit"
                        class="text-blue-600 border-gray-300 rounded shadow-sm focus:border-blue-300 focus:ring focus:ring-offset-0 focus:ring-blue-200 focus:ring-opacity-50"
                        :disabled="temp.isLoadingSubmit"
                    />
                    <span class="ml-2 font-bold text-blue-600"
                        >Sembunyikan DEPOSIT</span
                    >
                </label>

                <!-- RECEIVER_PHONE -->
                <label
                    class="block"
                    for="receiver_phone"
                    v-if="input.address_id"
                >
                    <div class="font-bold text-gray-900">
                        No. WhatsApp Pemesan
                    </div>
                    <input
                        type="tel"
                        v-model="input.receiver_phone"
                        name="receiver_phone"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        placeholder="Selain No. WhatsApp asli pelanggan"
                        :disabled="temp.isLoadingSubmit"
                    />
                </label>
            </template>

            <!-- PELANGGAN BARU -->
            <template v-if="input.order_type === 'new'">
                <!-- STORE_ID_NEW -->
                <label class="block relative" for="store_id_new">
                    <div
                        ref="scrollview-store_id_new"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-700">
                        Toko <span class="text-red-600">*</span>
                    </div>
                    <select
                        ref="input-store_id_new"
                        id="store_id_new"
                        name="store_id_new"
                        required
                        v-model="input.store_id_new"
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                    !temp.errors.hasOwnProperty('store_id_new'),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty('store_id_new'),
                            },
                        ]"
                        :disabled="temp.isLoadingSubmit"
                    >
                        <option value="">Pilih toko..</option>
                        <option
                            v-for="option in stores"
                            :value="option.id.toString()"
                        >
                            {{ option.name }}
                        </option>
                    </select>
                    <p
                        v-if="temp.errors.hasOwnProperty('store_id_new')"
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors.store_id_new[0] }}
                    </p>
                </label>

                <!-- CUSTOMER_NAME -->
                <label class="block relative" for="customer_name">
                    <div
                        ref="scrollview-customer_name"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-700">
                        Nama Pelanggan Baru <span class="text-red-600">*</span>
                    </div>
                    <input
                        ref="input-customer_name"
                        type="text"
                        id="customer_name"
                        name="customer_name"
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                    !temp.errors.hasOwnProperty(
                                        'customer_name'
                                    ),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty('customer_name'),
                            },
                        ]"
                        value=""
                        required
                        placeholder=""
                        v-model="input.customer_name"
                        @input="
                            (value) => {
                                removeError(`customer_name`);
                            }
                        "
                        :disabled="temp.isLoadingSubmit"
                    />
                    <p
                        v-if="temp.errors.hasOwnProperty('customer_name')"
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors.customer_name[0] }}
                    </p>
                </label>

                <!-- CUSTOMER_PHONE -->
                <label class="block" for="customer_phone">
                    <div
                        ref="scrollview-customer_phone"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-700">
                        No. WA Pelanggan Baru
                        <span class="text-red-600">*</span>
                    </div>
                    <input
                        ref="input-customer_phone"
                        type="tel"
                        id="customer_phone"
                        name="customer_phone"
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                    !temp.errors.hasOwnProperty(
                                        'customer_phone'
                                    ),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty(
                                        'customer_phone'
                                    ),
                            },
                        ]"
                        value=""
                        required
                        placeholder=""
                        v-model="input.customer_phone"
                        @input="
                            (value) => {
                                removeError(`customer_phone`);
                            }
                        "
                        :disabled="temp.isLoadingSubmit"
                    />
                    <p
                        v-if="temp.errors.hasOwnProperty('customer_phone')"
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors.customer_phone[0] }}
                    </p>
                </label>

                <!-- CUSTOMER_ADDRESS -->
                <label class="block relative" for="customer_address">
                    <div
                        ref="scrollview-customer_address"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-700">
                        Alamat Pelanggan Baru
                        <span class="text-red-600">*</span>
                    </div>
                    <textarea
                        ref="input-customer_address"
                        id="customer_address"
                        name="customer_address"
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                    !temp.errors.hasOwnProperty(
                                        'customer_address'
                                    ),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty(
                                        'customer_address'
                                    ),
                            },
                        ]"
                        rows="2"
                        required
                        v-model="input.customer_address"
                        @input="
                            (value) => {
                                removeError(`customer_address`);
                            }
                        "
                        :disabled="temp.isLoadingSubmit"
                    ></textarea>
                    <p
                        v-if="temp.errors.hasOwnProperty('customer_address')"
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors.customer_address[0] }}
                    </p>
                </label>

                <!-- CUSTOMER_LATLNG -->
                <label class="block" for="customer_latlng">
                    <div class="font-bold text-gray-700">
                        Koordinat (Latitude,Longitude)
                    </div>
                    <input
                        type="text"
                        id="customer_latlng"
                        name="customer_latlng"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-new-customer-latlng focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        value=""
                        placeholder="e.g. -7.123456,110.123456"
                        v-model="input.customer_latlng"
                        :disabled="temp.isLoadingSubmit"
                    />
                </label>

                <!-- IS_SECONDFLOOR (PELANGGAN BARU) -->
                <div class="block">
                    <div class="font-bold text-gray-700">Catatan Tambahan</div>
                    <div class="flex flex-col gap-2 mt-1.5">
                        <div>
                            <label class="inline-flex items-center">
                                <input
                                    type="checkbox"
                                    value="1"
                                    name="is_secondfloor"
                                    class="text-red-600 border-gray-300 rounded shadow-sm _js-additionalcost-secondfloor-new focus:border-red-300 focus:ring focus:ring-offset-0 focus:ring-red-200 focus:ring-opacity-50"
                                    v-model="input.is_secondfloor"
                                    :disabled="temp.isLoadingSubmit"
                                />
                                <span class="ml-2 font-semibold text-red-600"
                                    >Jasa lantai 2 @Rp3.000</span
                                >
                            </label>
                            <p class="-mt-1 text-sm text-gray-400">
                                Tambah Biaya setiap ORDER
                            </p>
                        </div>
                    </div>
                </div>
            </template>

            <!-- MARKETING (BAGI BROSUR) -->
            <template v-if="input.order_type === 'marketing'">
                <!-- STORE_ID_MARKETING -->
                <label class="block relative" for="store_id_marketing">
                    <div
                        ref="scrollview-store_id_marketing"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-700">
                        Toko <span class="text-red-600">*</span>
                    </div>
                    <select
                        ref="input-store_id_marketing"
                        id="store_id_marketing"
                        name="store_id_marketing"
                        required
                        v-model="input.store_id_marketing"
                        @change="() => removeError('store_id_marketing')"
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                    !temp.errors.hasOwnProperty(
                                        'store_id_marketing'
                                    ),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty(
                                        'store_id_marketing'
                                    ),
                            },
                        ]"
                        :disabled="temp.isLoadingSubmit"
                    >
                        <option value="">Pilih toko..</option>
                        <option
                            v-for="option in stores"
                            :value="option.id.toString()"
                        >
                            {{ option.name }}
                        </option>
                    </select>
                    <p
                        v-if="temp.errors.hasOwnProperty('store_id_marketing')"
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors.store_id_marketing[0] }}
                    </p>
                </label>

                <!-- AREA_ID -->
                <!-- TODO: Pake v-select -->
                <label class="relative block" for="area_id">
                    <div
                        id="input_area_id"
                        ref="scrollview-area_id"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-700">
                        Perumahan <span class="text-red-600">*</span>
                    </div>
                    <v-select
                        ref="input-area_id"
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                    !temp.errors.hasOwnProperty('area_id'),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty('area_id'),
                            },
                        ]"
                        :required="!input.area_id"
                        v-model="temp.valueArea"
                        :filterable="false"
                        :clearable="true"
                        :taggable="true"
                        :create-option="
                            (val) => ({
                                id: val,
                                name: val,
                                latlng: '',
                                isNew: true,
                            })
                        "
                        placeholder="Cari nama perumahan ..."
                        label="name"
                        :options="temp.optionsArea"
                        :selectOnTab="true"
                        @search="onSearchArea"
                        @input="
                            (val) => {
                                onSelectArea(val);
                                removeError(`area_id`);
                            }
                        "
                        @open="
                            document
                                .getElementById('input_area_id')
                                .scrollIntoView()
                        "
                        :transition="''"
                        :disabled="temp.isLoadingSubmit"
                    >
                        <template slot="no-options">
                            {{
                                temp.keywordArea.length >= 2
                                    ? "Maaf, data tidak ditemukan."
                                    : ""
                            }}
                        </template>
                        <template slot="option" slot-scope="option">
                            <area-item
                                :option="option"
                                :keyword="temp.keywordArea"
                                :withBorder="true"
                            />
                        </template>
                        <template slot="selected-option" slot-scope="option">
                            <area-item
                                :option="option"
                                :keyword="temp.keywordArea"
                                :isSelection="true"
                            />
                        </template>
                    </v-select>
                    <p
                        v-if="temp.errors.hasOwnProperty('area_id')"
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors["area_id"][0] }}
                    </p>
                </label>

                <!-- LATLANG -->
                <label class="block relative" for="latlng">
                    <div
                        ref="scrollview-latlng"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-700">
                        Koordinat <span class="text-red-600">*</span>
                    </div>
                    <div class="relative">
                        <input
                            ref="input-latlng"
                            type="text"
                            id="latlng"
                            name="latlng"
                            :class="[
                                'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                {
                                    'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                        !temp.errors.hasOwnProperty('latlng'),
                                },
                                {
                                    'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                        temp.errors.hasOwnProperty('latlng'),
                                },
                            ]"
                            :value="input.latlng"
                            @input="
                                (e) => {
                                    onInputLatlng(e);
                                    removeError('latlng');
                                }
                            "
                            :disabled="
                                temp.isLoadingSubmit || temp.isLoadingLatlng
                            "
                        />
                        <div
                            v-if="temp.isLoadingLatlng"
                            class="absolute top-0 bottom-0 z-10 flex items-center justify-center right-3 pointer-events-none"
                        >
                            <svg
                                class="w-5 h-5 text-gray-500 animate-spin"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    class="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    stroke-width="4"
                                ></circle>
                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-400 break-all">
                        Contoh: https://goo.gl/maps/acyGGxZ32hWhTwRPA
                    </p>
                    <p
                        v-if="temp.errors.hasOwnProperty('latlng')"
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors.latlng[0] }}
                    </p>
                </label>
            </template>

            <!-- PRODUCT -->
            <div class="block" v-if="isShowInputProducts">
                <hr class="mt-2" />
                <div class="flex flex-col gap-2">
                    <h3 class="mt-5 text-lg font-bold text-gray-900">
                        Keranjang
                        <span class="text-gray-400 text-base"
                            >({{ countProductQty }} Item /
                            {{ countProductVariants }} Variant)</span
                        >
                    </h3>
                    <div
                        class="block p-3 border-2 border-blue-200 rounded-lg bg-blue-50 relative"
                        v-for="(product, index) in input.products"
                        :key="product.id"
                    >
                        <!-- BUTTON REMOVE PRODUCT -->
                        <button
                            type="button"
                            :class="[
                                'absolute z-10 top-1.5 right-1.5 text-red-600',
                                {
                                    'opacity-50 pointer-events-none':
                                        temp.isLoadingSubmit,
                                },
                            ]"
                            @click="onClickRemoveProduct(index)"
                            :disabled="temp.isLoadingSubmit"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                class="h-5 w-5 stroke-2"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M6 18 18 6M6 6l12 12"
                                />
                            </svg>
                        </button>

                        <!-- PRODUCT_ID -->
                        <label
                            :for="`product_id_${index}`"
                            class="block relative"
                        >
                            <div
                                :id="'input_product_' + index"
                                :ref="`scrollview-products.${index}.product_id`"
                                class="absolute left-0 -top-16"
                            />
                            <div class="font-bold text-gray-900">
                                Produk <span class="text-red-600">*</span>
                            </div>
                            <!-- TODO: Harusnya posisi sudah terpilih, jika buka dropdown akan terselec yg sudah dipilih dan bisa scroll k bawah otomatis. Belom bisa pake prop :clearSearchOnSelect=false -->
                            <v-select
                                :ref="`input-products.${index}.product_id`"
                                :class="[
                                    'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                    {
                                        'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                            !temp.errors.hasOwnProperty(
                                                `products.${index}.product_id`
                                            ),
                                    },
                                    {
                                        'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                            temp.errors.hasOwnProperty(
                                                `products.${index}.product_id`
                                            ),
                                    },
                                ]"
                                :name="'product_id_' + index"
                                required
                                :filterBy="productFilterBy"
                                :clearable="false"
                                placeholder="Pilih produk.."
                                :options="temp.optionsProducts"
                                :transition="''"
                                :value="product.product_id"
                                :selectOnTab="true"
                                :loading="temp.isLoadingProducts"
                                @search="
                                    (search) => onSearchProduct(product, search)
                                "
                                @input="
                                    (value) => {
                                        updateProductValue(product, value);
                                        removeError(
                                            `products.${index}.product_id`
                                        );
                                    }
                                "
                                @open="
                                    document
                                        .getElementById(
                                            'input_product_' + index
                                        )
                                        .scrollIntoView()
                                "
                                :disabled="temp.isLoadingSubmit"
                            >
                                <template slot="no-options">
                                    {{
                                        product.temp.product_keyword.length >= 2
                                            ? "Maaf, produk tidak ditemukan."
                                            : ""
                                    }}
                                </template>
                                <template slot="option" slot-scope="option">
                                    <product-item
                                        :option="option"
                                        :keyword="product.temp.product_keyword"
                                        :withBorder="true"
                                    />
                                </template>
                                <template
                                    slot="selected-option"
                                    slot-scope="option"
                                >
                                    <product-item
                                        :option="product.temp.product_value"
                                        :keyword="product.temp.product_keyword"
                                    />
                                </template>
                            </v-select>
                            <p
                                v-if="
                                    temp.errors.hasOwnProperty(
                                        `products.${index}.product_id`
                                    )
                                "
                                class="text-sm text-red-600"
                            >
                                {{
                                    temp.errors[
                                        `products.${index}.product_id`
                                    ][0]
                                }}
                            </p>
                        </label>

                        <!-- IS PPOB -->
                        <template v-if="product.temp.product_value.is_ppob">
                            <!-- EMONEY -->
                            <template
                                v-if="
                                    product.temp.product_value.code === 'EMONEY'
                                "
                            >
                                <label
                                    class="block mt-2 mb-2 relative"
                                    for="ppob_product_code"
                                >
                                    <div
                                        :id="'input_ppob_product_code' + index"
                                        :ref="`scrollview-products.${index}.ppob_product_code`"
                                        class="absolute left-0 -top-16"
                                    />
                                    <div class="font-bold text-gray-900">
                                        Provider
                                    </div>
                                    <v-select
                                        autocomplete="off"
                                        :ref="`input-products.${index}.ppob_product_code`"
                                        :options="
                                            product.temp.options_pricelist
                                        "
                                        :class="[
                                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                            {
                                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                                    !temp.errors.hasOwnProperty(
                                                        `products.${index}.ppob_product_code`
                                                    ),
                                            },
                                            {
                                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                                    temp.errors.hasOwnProperty(
                                                        `products.${index}.ppob_product_code`
                                                    ),
                                            },
                                        ]"
                                        :name="'ppob_emoney_provider_' + index"
                                        required
                                        :clearable="false"
                                        placeholder="Cari nama provider.."
                                        :transition="''"
                                        :selectOnTab="true"
                                        :loading="
                                            product.temp.loading_pricelist
                                        "
                                        @input="
                                            (value) => {
                                                updateEmoneyProviderValue(
                                                    product,
                                                    value
                                                );
                                                removeError(
                                                    `products.${index}.ppob_product_code`
                                                );
                                            }
                                        "
                                        @open="
                                            document
                                                .getElementById(
                                                    'input_ppob_product_code' +
                                                        index
                                                )
                                                .scrollIntoView()
                                        "
                                        :disabled="temp.isLoadingSubmit"
                                        label="product_code"
                                        :filterBy="emoneyFilterBy"
                                    >
                                        <template
                                            slot="option"
                                            slot-scope="option"
                                        >
                                            <div
                                                class="py-1 px-2 flex gap-1.5 w-full"
                                            >
                                                <img
                                                    :src="option.icon_url"
                                                    :alt="
                                                        option.product_nominal
                                                    "
                                                    class="object-contain w-11 h-11"
                                                />
                                                <div
                                                    class="flex flex-col gap-0.1 w-auto flex-wrap"
                                                >
                                                    <span
                                                        class="font-bold break-all whitespace-normal"
                                                    >
                                                        {{
                                                            option.product_nominal
                                                        }}
                                                    </span>
                                                    <span
                                                        class="text-xs text-gray-500 break-all whitespace-normal"
                                                    >
                                                        {{
                                                            option.product_details
                                                        }}
                                                    </span>
                                                    <span
                                                        v-if="
                                                            option.active_period !==
                                                            '0'
                                                        "
                                                        class="text-xs text-red-600 font-bold break-all whitespace-normal"
                                                    >
                                                        {{
                                                            option.active_period
                                                        }}
                                                    </span>
                                                </div>
                                            </div>
                                        </template>
                                        <template
                                            slot="selected-option"
                                            slot-scope="option"
                                        >
                                            <div
                                                class="py-1 px-2 flex gap-1.5 w-full"
                                            >
                                                <img
                                                    :src="option.icon_url"
                                                    :alt="
                                                        option.product_nominal
                                                    "
                                                    class="object-contain w-11 h-11"
                                                />
                                                <div
                                                    class="flex flex-col gap-0.1 w-auto flex-wrap"
                                                >
                                                    <span
                                                        class="font-bold break-all whitespace-normal"
                                                    >
                                                        {{
                                                            option.product_nominal
                                                        }}
                                                    </span>
                                                    <span
                                                        class="text-xs text-gray-500 break-all whitespace-normal"
                                                    >
                                                        {{
                                                            option.product_details
                                                        }}
                                                    </span>
                                                    <span
                                                        v-if="
                                                            option.active_period !==
                                                            '0'
                                                        "
                                                        class="text-xs text-red-600 font-bold break-all whitespace-normal"
                                                    >
                                                        {{
                                                            option.active_period
                                                        }}
                                                    </span>
                                                </div>
                                            </div>
                                        </template>
                                    </v-select>
                                    <p
                                        v-if="
                                            temp.errors.hasOwnProperty(
                                                `products.${index}.ppob_product_code`
                                            )
                                        "
                                        class="text-sm text-red-600"
                                    >
                                        {{
                                            temp.errors[
                                                `products.${index}.ppob_product_code`
                                            ][0]
                                        }}
                                    </p>
                                </label>
                            </template>
                            <!-- AIR PDAM -->
                            <template
                                v-if="
                                    product.temp.product_value.code ===
                                    'AIR_PDAM'
                                "
                            >
                                <label
                                    class="block mt-2 mb-2 relative"
                                    for="ppob_product_code"
                                >
                                    <div
                                        :id="'input_ppob_product_code' + index"
                                        :ref="`scrollview-products.${index}.ppob_product_code`"
                                        class="absolute left-0 -top-16"
                                    />
                                    <div class="font-bold text-gray-900">
                                        Wilayah
                                    </div>
                                    <v-select
                                        autocomplete="off"
                                        :ref="`input-products.${index}.ppob_product_code`"
                                        :options="
                                            product.temp.options_pricelist
                                        "
                                        :class="[
                                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                            {
                                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                                    !temp.errors.hasOwnProperty(
                                                        `products.${index}.ppob_product_code`
                                                    ),
                                            },
                                            {
                                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                                    temp.errors.hasOwnProperty(
                                                        `products.${index}.ppob_product_code`
                                                    ),
                                            },
                                        ]"
                                        :name="'ppob_pdam_area_' + index"
                                        required
                                        :clearable="false"
                                        placeholder="Pilih wilayah.."
                                        :transition="''"
                                        v-model="product.temp.pricelist_value"
                                        :selectOnTab="true"
                                        :loading="
                                            product.temp.loading_pricelist
                                        "
                                        @input="
                                            (value) => {
                                                updateWilayahValue(
                                                    product,
                                                    value
                                                );
                                                removeError(
                                                    `products.${index}.ppob_product_code`
                                                );
                                            }
                                        "
                                        @open="
                                            document
                                                .getElementById(
                                                    'input_ppob_product_code' +
                                                        index
                                                )
                                                .scrollIntoView()
                                        "
                                        :disabled="temp.isLoadingSubmit"
                                        label="name"
                                    >
                                        <template
                                            slot="option"
                                            slot-scope="option"
                                        >
                                            <div class="py-1 px-2">
                                                {{ option.name }}
                                            </div>
                                        </template>
                                        <template
                                            slot="selected-option"
                                            slot-scope="option"
                                        >
                                            <div class="py-1 px-2">
                                                {{ option.name }}
                                            </div>
                                        </template>
                                    </v-select>
                                    <p
                                        v-if="
                                            temp.errors.hasOwnProperty(
                                                `products.${index}.ppob_product_code`
                                            )
                                        "
                                        class="text-sm text-red-600"
                                    >
                                        {{
                                            temp.errors[
                                                `products.${index}.ppob_product_code`
                                            ][0]
                                        }}
                                    </p>
                                </label>
                            </template>
                            <!-- PPOB_KEY -->
                            <label
                                v-if="
                                    product.temp.product_value.code ===
                                        'PLN_TKN' ||
                                    (product.temp.product_value.code ===
                                        'AIR_PDAM' &&
                                        product.ppob_product_code) ||
                                    (product.temp.product_value.code ===
                                        'EMONEY' &&
                                        product.ppob_product_code)
                                "
                                class="block mt-2 mb-2 relative"
                                for="key"
                            >
                                <div
                                    :ref="`scrollview-products.${index}.ppob_key`"
                                    class="absolute left-0 -top-16"
                                />
                                <div
                                    v-if="
                                        product.temp.product_value.code ===
                                        'EMONEY'
                                    "
                                    class="font-bold text-gray-900"
                                >
                                    No. ID / Kartu
                                </div>
                                <div
                                    v-else-if="
                                        product.temp.product_value.code ===
                                        'AIR_PDAM'
                                    "
                                    class="font-bold text-gray-900"
                                >
                                    No. Pelanggan
                                </div>
                                <div v-else class="font-bold text-gray-900">
                                    No. Meter / ID Pelanggan
                                </div>
                                <div class="relative">
                                    <input
                                        :ref="`input-products.${index}.ppob_key`"
                                        type="tel"
                                        name="ppob_key"
                                        required
                                        autocomplete="off"
                                        :class="[
                                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                            {
                                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                                    !temp.errors.hasOwnProperty(
                                                        `products.${index}.ppob_key`
                                                    ),
                                            },
                                            {
                                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                                    temp.errors.hasOwnProperty(
                                                        `products.${index}.ppob_key`
                                                    ),
                                            },
                                        ]"
                                        v-model="product.ppob_key"
                                        @input="
                                            (e) => {
                                                onInputPpobKey(product, e);
                                                removeError(
                                                    `products.${index}.ppob_key`
                                                );
                                            }
                                        "
                                        placeholder=""
                                        :disabled="
                                            product.temp
                                                .loading_ppobkey_inquiry ||
                                            temp.isLoadingSubmit
                                        "
                                    />
                                    <!-- <div
                                        v-if="
                                            product.temp.product_value.code ===
                                            'AIR_PDAM'
                                        "
                                        :class="[
                                            'absolute top-0 right-0 bottom-0 w-20',
                                            {
                                                'opacity-50':
                                                    !product.temp
                                                        .is_show_check_inquiry,
                                            },
                                        ]"
                                    >
                                        <span
                                            v-if="
                                                product.temp
                                                    .is_show_check_inquiry
                                            "
                                            class="absolute z-10 animate-ping inset-0 opacity-75 bg-blue-400 rounded-lg"
                                        />
                                        <button
                                            type="button"
                                            class="absolute z-20 flex justify-center items-center inset-1 font-bold text-white bg-blue-500 rounded-md px-2 py-1"
                                            @click="
                                                onClickCheckInquiry(product)
                                            "
                                            :disabled="
                                                !product.temp
                                                    .is_show_check_inquiry ||
                                                temp.isLoadingSubmit
                                            "
                                        >
                                            Check
                                        </button>
                                    </div> -->
                                </div>
                                <p
                                    v-if="
                                        temp.errors.hasOwnProperty(
                                            `products.${index}.ppob_key`
                                        )
                                    "
                                    class="mt0.5 text-xs text-red-600 font-bold"
                                >
                                    {{
                                        temp.errors[
                                            `products.${index}.ppob_key`
                                        ][0]
                                    }}
                                </p>
                                <div
                                    v-if="
                                        product.temp.loading_ppobkey_inquiry ||
                                        product.ppobkey_inquiry_data
                                    "
                                    class="mt0.5 font-bold"
                                >
                                    <div
                                        class="text-xs text-gray-400 py-0.5"
                                        v-if="
                                            product.temp.loading_ppobkey_inquiry
                                        "
                                    >
                                        Mengecek...
                                    </div>
                                    <div
                                        class="text-xs text-gray-500 flex flex-col gap-0 py-0.5"
                                        v-else-if="
                                            product.temp.product_value.code ===
                                                'AIR_PDAM' &&
                                            product.ppobkey_inquiry_data &&
                                            product.ppobkey_inquiry_data.tr_name
                                        "
                                    >
                                        <div>
                                            ✅
                                            {{
                                                product.ppobkey_inquiry_data
                                                    .tr_name
                                            }}
                                        </div>
                                        <div
                                            v-for="bill in product
                                                .ppobkey_inquiry_data.desc.bill
                                                .detail"
                                        >
                                            <div
                                                class="w-full border-b border-gray-300 flex items-center font-bold"
                                                v-if="bill.bill_amount"
                                            >
                                                📅 Tagihan
                                                {{
                                                    formatYearMonth(
                                                        bill.period
                                                    )
                                                }}:
                                                <span class="ml-auto"
                                                    >Rp{{
                                                        bill.bill_amount.toLocaleString(
                                                            "id"
                                                        )
                                                    }}</span
                                                >
                                            </div>
                                            <div
                                                class="w-full border-b border-gray-300 flex items-center font-bold"
                                                v-if="bill.misc_amount"
                                            >
                                                🗓 Retribusi
                                                {{
                                                    formatYearMonth(
                                                        bill.period
                                                    )
                                                }}:
                                                <span class="ml-auto"
                                                    >Rp{{
                                                        bill.misc_amount.toLocaleString(
                                                            "id"
                                                        )
                                                    }}</span
                                                >
                                            </div>
                                        </div>
                                        <div
                                            class="w-full border-b font-bold border-gray-300 flex items-center"
                                        >
                                            🪙 Biaya Admin:
                                            <span class="ml-auto"
                                                >Rp
                                                {{
                                                    (
                                                        parseInt(
                                                            product.temp
                                                                .product_value
                                                                .price
                                                        ) +
                                                        parseInt(
                                                            product
                                                                .ppobkey_inquiry_data
                                                                .admin
                                                        )
                                                    ).toLocaleString("id")
                                                }}</span
                                            >
                                        </div>
                                        <div
                                            class="w-full border-b border-blue-200 flex items-center font-bold"
                                        >
                                            💰 TOTAL Tagihan:
                                            <span class="ml-auto"
                                                >Rp{{
                                                    (
                                                        parseInt(
                                                            product.temp
                                                                .product_value
                                                                .price
                                                        ) +
                                                        parseInt(
                                                            product
                                                                .ppobkey_inquiry_data
                                                                .price
                                                        )
                                                    ).toLocaleString("id")
                                                }}</span
                                            >
                                        </div>
                                        <details class="mt-1">
                                            <summary
                                                class="text-blue-600 cursor-pointer"
                                            >
                                                Lihat Detail
                                            </summary>
                                            <pre
                                                class="text-xs overflow-x-auto w-full font-normal my-0.5 rounded bg-gray-100 border border-gray-300"
                                            >
                                        {{
                                                    JSON.stringify(
                                                        product.ppobkey_inquiry_data,
                                                        null,
                                                        1
                                                    )
                                                }}
                                        </pre
                                            >
                                        </details>
                                    </div>
                                    <div
                                        class="text-xs text-gray-500 py-1"
                                        v-else-if="
                                            product.ppobkey_inquiry_data &&
                                            product.ppobkey_inquiry_data.name
                                        "
                                    >
                                        ✅
                                        {{ product.ppobkey_inquiry_data.name }}
                                    </div>
                                    <div
                                        class="text-xs text-red-600 py-0.5"
                                        v-else-if="
                                            product.ppobkey_inquiry_data &&
                                            product.ppobkey_inquiry_data.message
                                        "
                                    >
                                        ❌
                                        {{
                                            product.ppobkey_inquiry_data.message
                                        }}
                                    </div>
                                </div>

                                <!-- LAST PURCHASE -->
                                <div
                                    class="mt-1.5 border-2 border-blue-200 bg-blue-100 rounded-md pt-1.5 px-2 pb-2"
                                >
                                    <h5 class="font-bold text-blue-600 text-xs">
                                        Last Purchase
                                    </h5>
                                    <template v-if="product.temp.loading_ppob">
                                        <div
                                            class="flex items-center mt-1 text-blue-500 font-semibold text-xs gap-1"
                                        >
                                            <svg
                                                class="animate-spin h-3 w-3"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                            >
                                                <circle
                                                    class="opacity-25"
                                                    cx="12"
                                                    cy="12"
                                                    r="10"
                                                    stroke="currentColor"
                                                    stroke-width="4"
                                                ></circle>
                                                <path
                                                    class="opacity-75"
                                                    fill="currentColor"
                                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                ></path>
                                            </svg>
                                            Loading...
                                        </div>
                                    </template>
                                    <template
                                        v-else-if="
                                            product.temp.ppobkey_options.filter(
                                                (ppobkey) =>
                                                    ppobkey.ppob_product_code
                                                        ? ppobkey.ppob_product_code ===
                                                          product.ppob_product_code
                                                        : true
                                            ).length == 0
                                        "
                                    >
                                        <div
                                            class="flex items-center mt-1 text-blue-500 font-semibold text-xs gap-1"
                                        >
                                            Belum pernah ada transaksi.
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div
                                            class="flex flex-col gap-1.5 mt-1.5 text-sm opacity-75 w-full"
                                        >
                                            <label
                                                class="inline-flex items-center w-full pr-6"
                                                v-for="ppobkey in product.temp.ppobkey_options.filter(
                                                    (ppobkey) =>
                                                        ppobkey.ppob_product_code
                                                            ? ppobkey.ppob_product_code ===
                                                              product.ppob_product_code
                                                            : true
                                                )"
                                            >
                                                <input
                                                    class=""
                                                    type="radio"
                                                    :disabled="
                                                        temp.isLoadingSubmit
                                                    "
                                                    :name="
                                                        'ppobkey-' + product.id
                                                    "
                                                    :id="
                                                        'ppobkey-' + product.id
                                                    "
                                                    :value="ppobkey.key"
                                                    v-model="
                                                        product.temp
                                                            .ppobkey_lastpurchase
                                                    "
                                                    @input="
                                                        () => {
                                                            onSelectPpobKeyLastPurchase(
                                                                product,
                                                                ppobkey.key
                                                            );
                                                            removeError(
                                                                `products.${index}.ppob_key`
                                                            );
                                                        }
                                                    "
                                                />
                                                <div
                                                    class="ml-2 flex flex-col w-full"
                                                >
                                                    <span
                                                        class="text-sm font-bold break-words"
                                                        >{{ ppobkey.key }}</span
                                                    >
                                                    <span
                                                        v-if="
                                                            product.temp
                                                                .product_value
                                                                .code ===
                                                            'PLN_TKN'
                                                        "
                                                        class="text-xs font-semibold break-words text-gray-500 -mt-0.5"
                                                        >{{
                                                            ppobkey.inquiry_data
                                                                .name
                                                                ? ppobkey
                                                                      .inquiry_data
                                                                      .name
                                                                : "ERROR"
                                                        }}</span
                                                    >
                                                    <span
                                                        v-else-if="
                                                            product.temp
                                                                .product_value
                                                                .code ===
                                                            'AIR_PDAM'
                                                        "
                                                        class="text-xs font-semibold break-words text-gray-500 -mt-0.5"
                                                        >{{
                                                            ppobkey.inquiry_data
                                                                .tr_name
                                                                ? ppobkey
                                                                      .inquiry_data
                                                                      .tr_name
                                                                : "ERROR"
                                                        }}</span
                                                    >
                                                </div>
                                            </label>
                                        </div>
                                    </template>
                                </div>
                            </label>

                            <!-- PLN -->
                            <template
                                v-if="
                                    product.temp.product_value.code ===
                                    'PLN_TKN'
                                "
                            >
                                <!-- NOMINAL (PPOB_PRODUCT_CODE) -->
                                <label
                                    class="block mt-2 mb-2 relative"
                                    for="ppob_product_code"
                                >
                                    <div
                                        :ref="`scrollview-products.${index}.ppob_product_code`"
                                        class="absolute left-0 -top-16"
                                    />
                                    <div class="font-bold text-gray-900">
                                        Nominal
                                    </div>
                                    <select
                                        :ref="`input-products.${index}.ppob_product_code`"
                                        id="ppob_product_code"
                                        name="ppob_product_code"
                                        required
                                        :disabled="
                                            temp.isLoadingSubmit ||
                                            product.temp.loading_pricelist ||
                                            product.temp
                                                .loading_ppobkey_inquiry ||
                                            !product.ppobkey_inquiry_data ||
                                            (product.ppobkey_inquiry_data &&
                                                parseInt(
                                                    product.ppobkey_inquiry_data
                                                        .status
                                                ) > 1)
                                        "
                                        :value="product.ppob_product_code"
                                        :class="[
                                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                            {
                                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                                    !temp.errors.hasOwnProperty(
                                                        `products.${index}.ppob_product_code`
                                                    ),
                                            },
                                            {
                                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                                    temp.errors.hasOwnProperty(
                                                        `products.${index}.ppob_product_code`
                                                    ),
                                            },
                                            {
                                                'opacity-50':
                                                    product.temp
                                                        .loading_ppobkey_inquiry ||
                                                    (product.ppobkey_inquiry_data &&
                                                        parseInt(
                                                            product
                                                                .ppobkey_inquiry_data
                                                                .status
                                                        ) > 1),
                                            },
                                        ]"
                                        @input="
                                            (e) => {
                                                onSelectPpobProduct(e, product);
                                                removeError(
                                                    `products.${index}.ppob_product_code`
                                                );
                                            }
                                        "
                                    >
                                        <template
                                            v-if="
                                                product.temp.loading_pricelist
                                            "
                                        >
                                            <option value="">Loading..</option>
                                        </template>
                                        <template
                                            v-else-if="
                                                product.temp
                                                    .loading_ppobkey_inquiry ||
                                                !product.ppobkey_inquiry_data ||
                                                (product.ppobkey_inquiry_data &&
                                                    product.ppobkey_inquiry_data
                                                        .status > 1)
                                            "
                                        >
                                            <option value="">
                                                Isi "No. Meter" dahulu!
                                            </option>
                                        </template>
                                        <template v-else>
                                            <option value="">
                                                Pilih nominal..
                                            </option>
                                            <option
                                                v-for="option in product.temp
                                                    .options_pricelist"
                                                :value="option.product_code"
                                            >
                                                {{
                                                    parseInt(
                                                        option.product_nominal /
                                                            1000
                                                    ).toLocaleString("id")
                                                }}rb (Rp{{
                                                    (
                                                        parseInt(
                                                            option.product_price
                                                        ) +
                                                        parseInt(
                                                            product.temp
                                                                .product_value
                                                                .price
                                                        )
                                                    ).toLocaleString("id")
                                                }})
                                            </option>
                                        </template>
                                    </select>
                                    <p
                                        v-if="
                                            temp.errors.hasOwnProperty(
                                                `products.${index}.ppob_product_code`
                                            )
                                        "
                                        class="text-sm text-red-600"
                                    >
                                        {{
                                            temp.errors[
                                                `products.${index}.ppob_product_code`
                                            ][0]
                                        }}
                                    </p>
                                </label>
                            </template>
                        </template>

                        <!-- QTY -->
                        <template v-else>
                            <label class="block mt-2 mb-2 relative" for="qty">
                                <div
                                    :ref="`scrollview-products.${index}.qty`"
                                    class="absolute left-0 -top-16"
                                />
                                <div class="font-bold text-gray-900">
                                    Qty <span class="text-red-600">*</span>
                                </div>
                                <input
                                    :ref="`input-products.${index}.qty`"
                                    type="number"
                                    min="1"
                                    name="qty"
                                    required
                                    :class="[
                                        'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                        {
                                            'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                                !temp.errors.hasOwnProperty(
                                                    `products.${index}.qty`
                                                ),
                                        },
                                        {
                                            'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                                temp.errors.hasOwnProperty(
                                                    `products.${index}.qty`
                                                ),
                                        },
                                    ]"
                                    @input="
                                        () =>
                                            removeError(`products.${index}.qty`)
                                    "
                                    v-model="product.qty"
                                    placeholder="0"
                                    :disabled="temp.isLoadingSubmit"
                                />
                                <p
                                    v-if="
                                        temp.errors.hasOwnProperty(
                                            `products.${index}.qty`
                                        )
                                    "
                                    class="text-sm text-red-600"
                                >
                                    {{
                                        temp.errors[`products.${index}.qty`][0]
                                    }}
                                </p>
                            </label>
                        </template>
                    </div>

                    <!-- BUTTON ADD PRODUCT -->
                    <button
                        type="button"
                        :disabled="temp.isLoadingSubmit"
                        :class="[
                            'w-full mt-1.5 px-4 py-2 text-sm font-bold tracking-widest text-white uppercase bg-blue-500 rounded-md shadow-lg focus:outline-none',
                            {
                                'opacity-50 pointer-events-none':
                                    temp.isLoadingSubmit,
                            },
                        ]"
                        @click="onClickAddProduct"
                    >
                        + Tambah Produk
                    </button>
                </div>
            </div>

            <template v-if="isShowNoteEtc">
                <hr />

                <!-- RECEIVER_PHONE (ASDT) -->
                <label
                    class="block"
                    for="receiver_phone"
                    v-if="['asdt'].includes(input.order_type)"
                >
                    <div class="font-bold text-gray-900">
                        No. WhatsApp Pemesan
                    </div>
                    <input
                        type="tel"
                        v-model="input.receiver_phone"
                        name="receiver_phone"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        placeholder=""
                        :disabled="temp.isLoadingSubmit"
                    />
                </label>

                <!-- NOTE -->
                <label
                    for="note"
                    class="block"
                    v-if="['old', 'new', 'asdt'].includes(input.order_type)"
                >
                    <div class="font-bold text-gray-700">
                        Catatan
                        <span class="text-sm text-red-500"
                            >by Pelanggan: (KELUAR DI NOTIF)</span
                        >
                    </div>
                    <textarea
                        id="note"
                        name="note"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        rows="2"
                        v-model="input.note"
                        :disabled="temp.isLoadingSubmit"
                    ></textarea>
                </label>

                <!-- DRIVER_ID -->
                <label
                    class="relative block mb-1"
                    for="driver_id"
                    v-if="input.order_type === 'marketing'"
                >
                    <div
                        ref="scrollview-driver_id"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-700">
                        Delman <span class="text-red-600">*</span>
                    </div>
                    <select
                        ref="input-driver_id"
                        id="driver_id"
                        name="driver_id"
                        v-model="input.driver_id"
                        required
                        @change="() => removeError('driver_id')"
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                    !temp.errors.hasOwnProperty('driver_id'),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty('driver_id'),
                            },
                        ]"
                        :disabled="temp.isLoadingSubmit"
                    >
                        <option value="">Pilih delman...</option>
                        <option
                            v-for="(driver, index) in drivers"
                            :value="driver.id"
                        >
                            {{ driver.name }}
                        </option>
                    </select>
                    <p
                        v-if="temp.errors.hasOwnProperty('driver_id')"
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors.driver_id[0] }}
                    </p>
                </label>

                <!-- NOTE_FOR_DRIVER -->
                <label
                    for="note_for_driver"
                    class="block"
                    v-if="
                        ['old', 'new', 'marketing', 'asdt'].includes(
                            input.order_type
                        )
                    "
                >
                    <div class="font-bold text-gray-700">
                        Catatan
                        <span class="text-sm text-blue-500"
                            >untuk Delman / Internal</span
                        >
                    </div>
                    <textarea
                        id="note_for_driver"
                        name="note_for_driver"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        rows="2"
                        v-model="input.note_for_driver"
                        :disabled="temp.isLoadingSubmit"
                    ></textarea>
                </label>

                <!-- IS_URGENT -->
                <div
                    class="block"
                    v-if="['old', 'new'].includes(input.order_type)"
                >
                    <div class="font-bold text-gray-700">Segera?</div>
                    <label class="inline-flex items-center mt-1">
                        <input
                            type="checkbox"
                            name="is_urgent"
                            id="is_urgent"
                            value="1"
                            v-model="input.is_urgent"
                            class="text-red-600 border-gray-300 rounded shadow-sm focus:border-red-300 focus:ring focus:ring-offset-0 focus:ring-red-200 focus:ring-opacity-50"
                            :disabled="temp.isLoadingSubmit"
                        />
                        <span class="ml-2">Ya, segera!</span>
                    </label>
                </div>

                <!-- PAYMENT -->
                <div
                    class="block"
                    v-if="
                        ['old', 'new', 'asdt', 'konsumsitoko'].includes(
                            input.order_type
                        )
                    "
                >
                    <div class="font-bold text-gray-700">
                        Pembayaran <span class="text-red-600">*</span>
                    </div>
                    <div class="mt-2">
                        <div>
                            <label class="inline-flex items-center">
                                <input
                                    type="radio"
                                    required
                                    name="payment"
                                    id="payment"
                                    v-model="input.payment"
                                    :disabled="temp.isLoadingSubmit"
                                    value="cash"
                                />
                                <span class="ml-2">Cash</span>
                            </label>
                        </div>
                        <div>
                            <label class="inline-flex items-center">
                                <input
                                    type="radio"
                                    required
                                    name="payment"
                                    id="payment"
                                    v-model="input.payment"
                                    :disabled="temp.isLoadingSubmit"
                                    value="transfer"
                                />
                                <span class="ml-2">Transfer</span>
                            </label>
                        </div>
                        <div>
                            <label class="inline-flex items-center">
                                <input
                                    type="radio"
                                    required
                                    name="payment"
                                    id="payment"
                                    v-model="input.payment"
                                    :disabled="temp.isLoadingSubmit"
                                    value="qris"
                                />
                                <span class="ml-2">QRIS</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- PELANGGAN LAMA -->
                <template v-if="input.order_type === 'old'">
                    <template v-if="input.address_id">
                        <hr class="" />

                        <!-- IS_SECONDFLOOR (PELANGGAN LAMA) -->
                        <div class="">
                            <div class="font-bold text-gray-700">
                                Catatan Tambahan
                            </div>
                            <div class="flex flex-col gap-2 mt-1.5">
                                <div>
                                    <label class="inline-flex items-center">
                                        <input
                                            type="checkbox"
                                            value="1"
                                            name="is_secondfloor"
                                            v-model="input.is_secondfloor"
                                            class="text-red-600 border-gray-300 rounded shadow-sm _js-additionalcost-secondfloor focus:border-red-300 focus:ring focus:ring-offset-0 focus:ring-red-200 focus:ring-opacity-50"
                                            :disabled="temp.isLoadingSubmit"
                                        />
                                        <span
                                            class="ml-2 font-semibold text-red-600"
                                            >Jasa lantai 2 @Rp3.000</span
                                        >
                                    </label>
                                    <p class="-mt-1 text-sm text-gray-400">
                                        Tambah Biaya setiap ORDER
                                    </p>
                                </div>
                            </div>
                        </div>
                    </template>

                    <hr class="" />

                    <!-- TAMBAH ALAMAT (PELANGGAN LAMA) -->
                    <label class="inline-flex items-center">
                        <h3 class="mr-2 text-lg font-bold text-black">
                            ➕ Tambah Alamat Lain
                        </h3>
                        <input
                            type="checkbox"
                            value="1"
                            name="is_add_another_address"
                            v-model="input.is_add_another_address"
                            class="border-gray-300 rounded shadow-sm _js-add-other-address"
                            :disabled="temp.isLoadingSubmit"
                        />
                    </label>

                    <!-- ALAMAT BARU -->
                    <template v-if="input.is_add_another_address">
                        <!-- STORE_ID_ANOTHER_ADDRESS -->
                        <label
                            class="block relative"
                            for="store_id_another_address"
                        >
                            <div
                                ref="scrollview-store_id_another_address"
                                class="absolute left-0 -top-16"
                            />
                            <div class="font-bold text-gray-700">
                                Toko <span class="text-red-600">*</span>
                            </div>
                            <select
                                ref="input-store_id_another_address"
                                id="store_id_another_address"
                                name="store_id_another_address"
                                :required="input.is_add_another_address"
                                v-model="input.store_id_another_address"
                                @input="
                                    () =>
                                        removeError('store_id_another_address')
                                "
                                :class="[
                                    'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                    {
                                        'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                            !temp.errors.hasOwnProperty(
                                                'store_id_another_address'
                                            ),
                                    },
                                    {
                                        'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                            temp.errors.hasOwnProperty(
                                                'store_id_another_address'
                                            ),
                                    },
                                ]"
                                :disabled="temp.isLoadingSubmit"
                            >
                                <option value="">Pilih toko..</option>
                                <option
                                    v-for="option in stores"
                                    :value="option.id.toString()"
                                >
                                    {{ option.name }}
                                </option>
                            </select>
                            <p
                                v-if="
                                    temp.errors.hasOwnProperty(
                                        'store_id_another_address'
                                    )
                                "
                                class="text-sm text-red-600"
                            >
                                {{ temp.errors.store_id_another_address[0] }}
                            </p>
                        </label>

                        <!-- ANOTHER_ADDRESS -->
                        <label class="block relative" for="another_address">
                            <div
                                ref="scrollview-another_address"
                                class="absolute left-0 -top-16"
                            />
                            <div class="font-bold text-gray-700">
                                Alamat Lain Pelanggan
                                <span class="text-red-600">*</span>
                            </div>
                            <textarea
                                ref="input-another_address"
                                id="another_address"
                                name="another_address"
                                v-model="input.another_address"
                                @input="() => removeError('another_address')"
                                :required="input.is_add_another_address"
                                :class="[
                                    'block w-full mt-1 rounded-md shadow-sm focus:ring',
                                    {
                                        'border-gray-300 focus:border-blue-300 focus:ring-blue-200 focus:ring-opacity-50':
                                            !temp.errors.hasOwnProperty(
                                                'another_address'
                                            ),
                                    },
                                    {
                                        'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                            temp.errors.hasOwnProperty(
                                                'another_address'
                                            ),
                                    },
                                ]"
                                rows="2"
                                :disabled="temp.isLoadingSubmit"
                            ></textarea>
                            <p
                                v-if="
                                    temp.errors.hasOwnProperty(
                                        'another_address'
                                    )
                                "
                                class="text-sm text-red-600"
                            >
                                {{ temp.errors.another_address[0] }}
                            </p>
                        </label>

                        <!-- ANOTHER_ADDRESS_IS_SECONDFLOOR -->
                        <div class="flex flex-col gap-2">
                            <div>
                                <label class="inline-flex items-center">
                                    <input
                                        type="checkbox"
                                        value="1"
                                        name="another_address_is_secondfloor"
                                        v-model="
                                            input.another_address_is_secondfloor
                                        "
                                        class="text-red-600 border-gray-300 rounded shadow-sm _js-another-address-additionalcost-secondfloor focus:border-red-300 focus:ring focus:ring-offset-0 focus:ring-red-200 focus:ring-opacity-50"
                                        :disabled="temp.isLoadingSubmit"
                                    />
                                    <span
                                        class="ml-2 font-semibold text-red-600"
                                        >Jasa lantai 2 alamat di atas
                                        @Rp3.000</span
                                    >
                                </label>
                                <p class="-mt-1 text-sm text-gray-400">
                                    Tambah Biaya setiap ORDER
                                </p>
                            </div>
                        </div>

                        <!-- ANOTHER_ADDRESS_LATLNG -->
                        <label class="block" for="another_address_latlng">
                            <div class="font-bold text-gray-700">
                                Koordinat (Latitude,Longitude)
                            </div>
                            <input
                                type="text"
                                id="another_address_latlng"
                                name="another_address_latlng"
                                v-model="input.another_address_latlng"
                                class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-new-customer-latlng focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                value=""
                                placeholder="e.g. -7.123456,110.123456"
                                :disabled="temp.isLoadingSubmit"
                            />
                        </label>
                        <hr class="mt-2" />
                    </template>
                </template>

                <!-- DEPOSIT -->
                <label
                    v-if="
                        current_role_id <= 3 &&
                        ['old', 'new'].includes(input.order_type)
                    "
                    class="block _js-wrp-old-new"
                    for="receiver_phone"
                >
                    <h3 class="mb-3 text-lg font-bold text-black">
                        ➕ Tambah Deposit
                    </h3>
                    <money
                        placeholder="0"
                        :disabled="temp.isLoadingSubmit"
                        name="deposit"
                        class="block w-full mt-1 text-right border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        v-model="input.deposit"
                        v-bind="money"
                    />
                    <p class="mt-2 text-sm text-gray-400">
                        Bisa minus atau plus
                    </p>
                </label>

                <!-- BUTTON SUBMIT -->
                <button
                    @click="onClickSubmit"
                    :disabled="temp.isLoadingSubmit"
                    type="button"
                    :class="[
                        'px-4 py-3 rounded-md font-bold text-sm shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none transition ease-in-out duration-150 w-full mt-3 mb-5 flex items-center justify-center',
                        { 'bg-green-600': !current_store_is_notif_wa },
                        { 'bg-red-600': current_store_is_notif_wa },
                        {
                            'opacity-50 pointer-events-none':
                                temp.isLoadingSubmit,
                        },
                    ]"
                >
                    <svg
                        v-if="temp.isLoadingSubmit"
                        class="text-white animate-spin w-7 h-7 mr-2.5"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                    >
                        <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                        ></circle>
                        <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    <svg
                        v-if="!temp.isLoadingSubmit"
                        class="w-7 h-7 mr-2.5"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        stroke-width="1.5"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5"
                        />
                    </svg>
                    {{
                        temp.isLoadingSubmit
                            ? "SAVING..."
                            : `SAVE JOB & ${
                                  parseInt(current_store_is_notif_wa)
                                      ? "AUTO"
                                      : ""
                              } SEND NOTIF`
                    }}
                </button>
            </template>
            <pre
                v-if="[1].includes(current_role_id)"
                class="text-xs overflow-x-auto w-full"
            >
                {{ JSON.stringify(input, null, 1) }}
            </pre>
        </div>
    </div>
</template>

<script>
import "vue-select/dist/vue-select.css";
import { _ } from "vue-underscore";
import { Money } from "v-money";
import axios from "axios";
import CustomerAddressItem from "./CustomerAddressItem";
import AreaItem from "./AreaItem";
import ProductItem from "./ProductItem";
export default {
    components: {
        CustomerAddressItem,
        AreaItem,
        ProductItem,
        Money,
    },
    props: {
        url_jobs_temp: String,
        stores: Array,
        current_store_open_hour: String,
        current_store_close_hour: String,
        current_store_is_notif_wa: Number,
        current_store_id: String,
        current_role_id: Number,
        drivers: Array,
    },
    data: function () {
        return {
            document: document,
            money: {
                decimal: ",",
                thousands: ".",
                prefix: "Rp",
                suffix: "",
                precision: 0,
                masked: false /* doesn't work with directive */,
            },
            input: {
                order_type: this.getParamValue("t") ?? "old",
                ...this.getDefaultInput(),
            },
            temp: this.getDefaultTemp(),
            test: {
                current_role_id: this.current_role_id,
            },
        };
    },
    mounted() {
        //? Remove loading after mounted
        const elements = document.querySelectorAll("._js-loading");
        elements.forEach((element) => {
            element.remove();
        });
        this.getOptionsProducts();
    },
    methods: {
        moment: moment,
        formatYearMonth(yearMonth) {
            const year = yearMonth.substring(0, 4);
            const month = yearMonth.substring(4, 6);
            const date = new Date(year, month - 1);
            const options = { year: "numeric", month: "long" };
            return date.toLocaleDateString("id-ID", options);
        },
        removeError(key) {
            // console.log("🚀 ~ removeError:", key);
            delete this.temp.errors[key];
        },
        getUrlOnly(input) {
            if (!input) return "";
            const urlRegex = /(https?:\/\/[^ ]*)/;
            // console.log("🚀 ~ urlRegex:", urlRegex);
            // console.log("🚀 ~ match:", input.match(urlRegex));
            return input.match(urlRegex) ? input.match(urlRegex)[1] : input;
        },
        toPhone(number) {
            if (!number) return number;
            let num = number;
            if (
                num.trim().startsWith() == "0" ||
                num.trim().startsWith() == "(" ||
                num.trim().startsWith() == "+62" ||
                num.trim().startsWith() == "62"
            ) {
                num = num.replaceAll(" ", "");
                num = num.replaceAll("-", "");
                num = num.replaceAll("+", "");
                num = num.replaceAll("(", "");
                num = num.replaceAll(")", "");
                if (num.substr(0, 1) == "0") {
                    num = "62" + num.substr(1);
                }
            }
            return num;
        },
        getDefaultTemp() {
            return {
                optionsCustomerAddress: [],
                keywordCustomerAddress: "",
                valueCustomerAddress: "",
                optionsArea: [],
                keywordArea: "",
                valueArea: "",
                optionsProducts: [],
                isLoadingLatlng: false,
                isLoadingProducts: false,
                isLoadingSubmit: false,
                errors: {},
            };
        },
        getDefaultInput() {
            return {
                store_id: this.current_store_id,
                created_at: this.generateDateTime(),
                address_id: "",
                area_id: "",
                is_hide_deposit: false,
                is_send_notif: true,
                receiver_phone: "",
                store_id_old: "",
                store_id_new: this.current_store_id,
                customer_id: "",
                customer_name: "",
                customer_phone: "",
                customer_address: "",
                customer_latlng: "",
                is_secondfloor: false,
                is_add_another_address: false,
                store_id_marketing: this.current_store_id,
                latlng: "",
                products: [this.getDefaultInputProduct()],
                note: "",
                driver_id: "",
                note_for_driver: "",
                is_urgent: false,
                payment: "cash",
                store_id_another_address: "",
                another_address: "",
                another_address_is_secondfloor: false,
                another_address_latlng: "",
                deposit: 0,
            };
        },
        getDefaultInputProduct() {
            return {
                id: this.generateRandomId(),
                product_id: "",
                is_ppob: false,
                ppob_key: "",
                ppobkey_inquiry_data: "",
                ppob_product_code: "",
                ppob_tr_id: "",
                ppob_nominal: 0,
                ppob_price: 0,
                ppob_fee: 0,
                ppob_komisi: 0,
                qty: 1,
                temp: {
                    product_keyword: "",
                    product_value: "",
                    loading_ppob: true,
                    loading_ppobkey_inquiry: false,
                    // is_show_check_inquiry: false,
                    ppobkey_options: [],
                    ppobkey_lastpurchase: "",
                    loading_pricelist: false,
                    options_pricelist: [],
                    pricelist_value: "",
                },
            };
        },
        onSelectPpobProduct(e, product) {
            const val = e.target.value;
            const options_pricelist = product.temp.options_pricelist;
            const pricelistSelected = options_pricelist.find(
                (p) => p.product_code === val
            );
            if (pricelistSelected) {
                // this.$nextTick(() => {
                product.ppob_product_code = pricelistSelected.product_code;
                product.ppob_nominal = pricelistSelected.product_nominal;
                product.ppob_price = pricelistSelected.product_price;
                // });
            }
            // console.log("🚀 ~ pricelistSelected:", pricelistSelected);
        },
        onInputLatlng: _.debounce(function (e) {
            const val = e.target.value;
            const regexLatLng =
                /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/g;
            const passLatLng = regexLatLng.test(val);
            if (!passLatLng) {
                const url = this.getUrlOnly(val);
                const regexGmapUrl = /(\google.com+)/g;
                const regexGmapShare = /(\goo.gl+)/g;
                const regexGPage = /(\g.page+)/g;
                const passGmapShare = regexGmapShare.test(url);
                const passGmapUrl = regexGmapUrl.test(url);
                const passGPage = regexGPage.test(url);

                if (passGmapUrl || passGmapShare) {
                    this.temp.isLoadingLatlng = true;
                    this.input.latlng = val;
                    axios
                        .get(`/manager/ajax/get-coordinate-gmap?url=${url}`)
                        .then((res) => {
                            console.log("get-coordinate-gmap res", res.data);
                            this.input.latlng = res.data;
                        })
                        .catch((err) => {
                            mdtoast("TERJADI KESALAHAN", {
                                duration: 1500,
                                type: mdtoast.ERROR,
                            });
                        })
                        .finally(() => {
                            this.temp.isLoadingLatlng = false;
                        });
                } else {
                    // setInputError($this);
                    mdtoast("INPUT TIDAK VALID", {
                        duration: 1500,
                        type: mdtoast.ERROR,
                    });
                }
            } else {
                // setInputDefault($this);
            }
        }, 1000),
        generateDateTime(days = 0) {
            return moment().add(days, "days").format("YYYY-MM-DD HH:mm");
        },
        generateRandomId() {
            return Math.random().toString(36).substr(2, 9);
        },
        onSearchArea(search, loading) {
            this.temp.keywordArea = search;
            if (this.temp.keywordArea.length >= 2) {
                loading(true);
                this.searchArea(loading, search, this);
            }
            if (this.temp.keywordArea.length <= 2 && !this.input.area_id) {
                this.temp.optionsArea = [];
            }
        },
        searchArea: _.debounce((loading, search, vm) => {
            let src = search;
            vm.temp.keywordArea = src;
            // ? Fetch search
            axios
                .get(
                    `/manager/ajax/search-area?store_id=${
                        vm.current_store_id
                    }&query=${escape(src)}`
                )
                .then((res) => {
                    console.log("🚀 ~ res:", res);
                    vm.temp.optionsArea = res.data;
                    loading(false);
                })
                .catch((error) => {
                    console.error("🚀 ~ error:", error);
                    mdtoast("Oops... Something went wrong!", {
                        duration: 3000,
                        type: mdtoast.ERROR,
                    });
                    loading(false);
                })
                .finally(() => {
                    loading(false);
                });
        }, 350),
        onSelectArea(selectedValue) {
            if (selectedValue?.latlng) {
                this.input.latlng = selectedValue.latlng;
            }
        },
        onSearchCustomerAddress(search, loading) {
            this.temp.keywordCustomerAddress = search;
            if (this.temp.keywordCustomerAddress.length >= 2) {
                loading(true);
                this.searchCustomerAddress(loading, search, this);
            }
            if (
                this.temp.keywordCustomerAddress.length <= 2 &&
                !this.input.address_id
            ) {
                this.temp.optionsCustomerAddress = [];
            }
        },
        searchCustomerAddress: _.debounce((loading, search, vm) => {
            // ? Convert search keyword
            let src = vm.toPhone(search);
            // if (
            //     src.trim().startsWith() == "0" ||
            //     src.trim().startsWith() == "(" ||
            //     src.trim().startsWith() == "+62" ||
            //     src.trim().startsWith() == "62"
            // ) {
            //     src = src.replaceAll(" ", "");
            //     src = src.replaceAll("-", "");
            //     src = src.replaceAll("+", "");
            //     src = src.replaceAll("(", "");
            //     src = src.replaceAll(")", "");
            //     if (src.substr(0, 1) == "0") {
            //         src = "62" + src.substr(1);
            //     }
            vm.temp.keywordCustomerAddress = src;
            // vm.temp.valueCustomerAddress = src; // TODO: Should chanage input text
            // }
            // ? Fetch search
            axios
                .get(
                    `/manager/ajax/search-customer?store_id=${
                        vm.current_store_id
                    }&query=${escape(src)}`
                )
                .then((res) => {
                    console.log("🚀 ~ res:", res);
                    vm.temp.optionsCustomerAddress = res.data.map((obj) => {
                        obj["name"] = obj.customer.name;
                        obj["phone"] = obj.customer.phone;
                        obj["note_special"] = obj.customer.note_special
                            ? obj.customer.note_special
                            : "";
                        obj["deposit"] = obj.customer.deposit_amount
                            ? obj.customer.deposit_amount
                            : null;
                        obj["store_name"] = obj.store.name
                            ? obj.store.name
                            : null;
                        obj["store_id"] = obj.store.id ? obj.store.id : null;
                        obj["addresses_count"] = obj.customer.addresses_count
                            ? obj.customer.addresses_count
                            : 0;
                        if (obj.customer.merger) {
                            const customer = obj.customer.merger.maincustomer;
                            obj["name"] = customer.name;
                            obj["id"] = customer.addresses[0].id;
                            obj["label"] = customer.addresses[0].label;
                            obj["address"] = customer.addresses[0].address;
                            obj["is_secondfloor"] =
                                customer.addresses[0].is_secondfloor;
                            obj["note_special"] = customer.note_special
                                ? customer.note_special
                                : "";
                            obj["deposit"] = customer.deposit_amount
                                ? customer.deposit_amount
                                : null;
                            obj["addresses_count"] = customer.addresses_count
                                ? customer.addresses_count
                                : 0;
                        }
                        return obj;
                    });
                    loading(false);
                })
                .catch((error) => {
                    console.error("🚀 ~ error:", error);
                    mdtoast("Oops... Something went wrong!", {
                        duration: 3000,
                        type: mdtoast.ERROR,
                    });
                    loading(false);
                })
                .finally(() => {
                    loading(false);
                });
        }, 350),
        productFilterBy(option, label, search) {
            return (
                option.code.toLowerCase().includes(search.toLowerCase()) ||
                option.name.toLowerCase().includes(search.toLowerCase())
            );
        },
        emoneyFilterBy(option, label, search) {
            return (
                option.product_description
                    .toLowerCase()
                    .includes(search.toLowerCase()) ||
                option.product_nominal
                    .toLowerCase()
                    .includes(search.toLowerCase())
            );
        },
        onSearchProduct(product, search) {
            product.temp.product_keyword = search;
        },
        getOptionsProducts(address_id = "") {
            this.temp.isLoadingProducts = true;
            const store_id = this.current_store_id;
            axios
                .get(
                    `/manager/ajax/search-product?store_id=${store_id}&address_id=${address_id}`
                )
                .then((res) => {
                    console.log("🚀 ~ res products:", res);
                    this.temp.optionsProducts = res.data.map((obj) => {
                        const local_price =
                            obj.local_price && parseInt(obj.local_price) > 0
                                ? obj.local_price
                                : obj.price;
                        obj["price"] =
                            obj.special_price && parseInt(obj.special_price) > 0
                                ? obj.special_price
                                : local_price;
                        obj["label"] = obj.code;
                        obj["is_available"] =
                            obj.special_is_available ?? obj.is_available;
                        return obj;
                    });
                    this.temp.isLoadingProducts = false;
                });
        },
        updateProductValue(product, selectedValue) {
            console.log("🚀 ~ selectedValue:", selectedValue);
            product.temp.product_value = selectedValue;
            product.product_id = selectedValue?.id;
            product.ppob_key = "";
            product.ppobkey_inquiry_data = "";
            product.ppob_product_code = "";
            product.ppob_tr_id = "";
            product.ppob_nominal = 0;
            product.ppob_price = 0;
            product.ppob_komisi = 0;
            product.ppob_fee = 0;
            product.qty = 1;
            product.temp.ppobkey_options = [];
            product.temp.ppobkey_lastpurchase = "";
            product.temp.options_pricelist = [];
            const selectedValueIsPpob = selectedValue?.is_ppob;
            if (selectedValueIsPpob) {
                product.is_ppob = true;
                this.getOptionsPpobKeys(product);
                switch (selectedValue?.code) {
                    case "EMONEY":
                        if (selectedValue.ppobpricelist) {
                            product.temp.options_pricelist =
                                selectedValue?.ppobpricelist?.data?.sort(
                                    (a, b) => {
                                        if (
                                            a.product_description <
                                            b.product_description
                                        ) {
                                            return -1;
                                        }
                                        if (
                                            a.product_description >
                                            b.product_description
                                        ) {
                                            return 1;
                                        }
                                        if (a.product_price < b.product_price) {
                                            return -1;
                                        }
                                        if (a.product_price > b.product_price) {
                                            return 1;
                                        }
                                        return 0;
                                    }
                                );
                            product.temp.loading_pricelist = false;
                        } else {
                            product.temp.loading_pricelist = true;
                        }
                        axios
                            .get(
                                `/iak/prepaid/pricelist/etoll?product_id=${product.product_id}`
                            )
                            .then((res) => {
                                console.log("🚀 ~ res options_pricelist:", res);
                                product.temp.options_pricelist =
                                    res?.data?.pricelist
                                        ?.sort((a, b) => {
                                            if (
                                                a.product_description <
                                                b.product_description
                                            ) {
                                                return -1;
                                            }
                                            if (
                                                a.product_description >
                                                b.product_description
                                            ) {
                                                return 1;
                                            }
                                            if (
                                                a.product_price <
                                                b.product_price
                                            ) {
                                                return -1;
                                            }
                                            if (
                                                a.product_price >
                                                b.product_price
                                            ) {
                                                return 1;
                                            }
                                            return 0;
                                        })
                                        .map((item) => {
                                            if (
                                                item.product_nominal &&
                                                !isNaN(item.product_nominal)
                                            ) {
                                                item.product_nominal =
                                                    "Rp " +
                                                    parseInt(
                                                        item.product_nominal
                                                    ).toLocaleString("id");
                                            }
                                            return item;
                                        });
                            })
                            .catch((error) => {
                                console.error(
                                    "🚀 ~ error get options_pricelist:",
                                    error
                                );
                                mdtoast("Oops... Something went wrong!", {
                                    duration: 3000,
                                    type: mdtoast.ERROR,
                                });
                            })
                            .finally(() => {
                                product.temp.loading_pricelist = false;
                            });
                        break;

                    case "PLN_TKN":
                        if (selectedValue.ppobpricelist) {
                            product.temp.options_pricelist =
                                selectedValue?.ppobpricelist?.data?.sort(
                                    (a, b) =>
                                        parseInt(a.product_price) -
                                        parseInt(b.product_price)
                                );
                            product.temp.loading_pricelist = false;
                        } else {
                            product.temp.loading_pricelist = true;
                        }
                        axios
                            .get(
                                `/iak/prepaid/pricelist/pln?product_id=${product.product_id}`
                            )
                            .then((res) => {
                                console.log("🚀 ~ res options_pricelist:", res);
                                product.temp.options_pricelist =
                                    res?.data?.pricelist?.sort(
                                        (a, b) =>
                                            parseInt(a.product_price) -
                                            parseInt(b.product_price)
                                    );
                            })
                            .catch((error) => {
                                console.error(
                                    "🚀 ~ error get options_pricelist:",
                                    error
                                );
                                mdtoast("Oops... Something went wrong!", {
                                    duration: 3000,
                                    type: mdtoast.ERROR,
                                });
                            })
                            .finally(() => {
                                product.temp.loading_pricelist = false;
                            });
                        break;

                    case "AIR_PDAM":
                        if (selectedValue.ppobpricelist) {
                            product.temp.options_pricelist =
                                selectedValue?.ppobpricelist?.data?.sort(
                                    (a, b) => {
                                        if (a.province < b.province) {
                                            return -1;
                                        }
                                        if (a.province > b.province) {
                                            return 1;
                                        }
                                        if (a.name < b.name) {
                                            return -1;
                                        }
                                        if (a.name > b.name) {
                                            return 1;
                                        }
                                        return 0;
                                    }
                                );
                            product.temp.loading_pricelist = false;
                        } else {
                            product.temp.loading_pricelist = true;
                        }
                        axios
                            .get(
                                `/iak/postpaid/pricelist/pdam?product_id=${product.product_id}`
                            )
                            .then((res) => {
                                console.log("🚀 ~ res options_pricelist:", res);
                                product.temp.options_pricelist =
                                    res?.data?.sort((a, b) => {
                                        if (a.province < b.province) {
                                            return -1;
                                        }
                                        if (a.province > b.province) {
                                            return 1;
                                        }
                                        if (a.name < b.name) {
                                            return -1;
                                        }
                                        if (a.name > b.name) {
                                            return 1;
                                        }
                                        return 0;
                                    });
                            })
                            .catch((error) => {
                                console.error(
                                    "🚀 ~ error get options_pricelist:",
                                    error
                                );
                                mdtoast("Oops... Something went wrong!", {
                                    duration: 3000,
                                    type: mdtoast.ERROR,
                                });
                            })
                            .finally(() => {
                                product.temp.loading_pricelist = false;
                            });
                        break;
                    default:
                        break;
                }
            } else {
                product.is_ppob = false;
            }
        },
        updateWilayahValue(product, selectedValue) {
            product.ppobkey_inquiry_data = "";
            product.ppob_key = "";
            product.ppob_tr_id = "";
            product.temp.loading_ppobkey_inquiry = false;
            if (selectedValue) {
                product.temp.pricelist_value = selectedValue;
                product.ppob_product_code = selectedValue?.code;
                product.ppob_komisi = selectedValue.komisi;
                product.ppob_fee = selectedValue.fee;
            } else {
                product.temp.pricelist_value = "";
                product.ppob_product_code = "";
                product.ppob_komisi = 0;
                product.ppob_fee = 0;
            }
        },
        updateEmoneyProviderValue(product, selectedValue) {
            console.log("🚀 ~ selectedValue:", selectedValue);
            // product.ppobkey_inquiry_data = "";
            // product.ppob_key = "";
            // product.ppob_tr_id = "";
            // product.temp.loading_ppobkey_inquiry = false;
            if (selectedValue) {
                product.temp.pricelist_value = selectedValue;
                product.ppob_product_code = selectedValue?.product_code;
                product.ppob_nominal = selectedValue?.product_price;
                product.ppob_price = selectedValue?.product_price;
                product.ppobkey_inquiry_data = selectedValue;
                // product.ppob_komisi = selectedValue.komisi;
                // product.ppob_fee = selectedValue.fee;
            } else {
                product.temp.pricelist_value = "";
                product.ppob_product_code = "";
                product.ppob_nominal = "";
                product.ppob_price = "";
                product.ppobkey_inquiry_data = "";
                // product.ppob_komisi = 0;
                // product.ppob_fee = 0;
            }
        },
        getOptionsPpobKeys(product) {
            product.temp.loading_ppob = true;
            axios
                .get(
                    `/search/customer/${this.temp.valueCustomerAddress.customer_id}/product/${product.product_id}/ppobkey`
                )
                .then((res) => {
                    console.log("🚀 ~ res ppobkeys:", res);
                    product.temp.ppobkey_options = res.data;
                    product.temp.loading_ppob = false;
                })
                .catch((error) => {
                    console.error("🚀 ~ error ppobkeys:", error);
                    mdtoast("Oops... Something went wrong!", {
                        duration: 3000,
                        type: mdtoast.ERROR,
                    });
                    product.temp.loading_ppob = false;
                })
                .finally(() => {
                    product.temp.loading_ppob = false;
                });
        },
        doCheckInquiry(product, ppobkey) {
            const customer_id = this.input.customer_id ?? "xxx";
            if (
                product.temp.ppobkey_options.some(
                    (item) => item.key === ppobkey
                )
            ) {
                product.temp.ppobkey_lastpurchase = ppobkey;
            } else {
                product.temp.ppobkey_lastpurchase = "";
            }
            // ? PLN
            if (product.temp.product_value.code === "PLN_TKN") {
                product.ppob_product_code = "";
                if (ppobkey.length >= 11) {
                    product.temp.loading_ppobkey_inquiry = true;
                    axios
                        .get(
                            `/iak/prepaid/inquiry/pln/${ppobkey}/${customer_id}/${product.product_id}`
                        )
                        .then((res) => {
                            console.log("🚀 ~ res ppobkey inquiry:", res);
                            if (res.data.status === 2) {
                                mdtoast(res.data.message, {
                                    duration: 3000,
                                    type: mdtoast.ERROR,
                                });
                            }
                            product.ppobkey_inquiry_data = res.data;
                            product.temp.loading_ppobkey_inquiry = false;
                        })
                        .catch((error) => {
                            console.error("🚀 ~ error ppobkey inquiry:", error);
                            mdtoast("Oops... Something went wrong!", {
                                duration: 3000,
                                type: mdtoast.ERROR,
                            });
                            product.ppobkey_inquiry_data = "";
                            product.temp.loading_ppobkey_inquiry = false;
                        })
                        .finally(() => {
                            product.temp.loading_ppobkey_inquiry = false;
                        });
                } else {
                    product.ppobkey_inquiry_data = {
                        message: "Belum lengkap! Minimal 11 digit.",
                        status: 2,
                    };
                }

                // ? Air PDAM
            } else if (product.temp.product_value.code === "AIR_PDAM") {
                if (ppobkey.length >= 7) {
                    product.temp.loading_ppobkey_inquiry = true;
                    axios
                        .get(
                            `/iak/postpaid/inquiry/pdam/${ppobkey}/${product.ppob_product_code}/${product.id}/${customer_id}/${product.product_id}`
                        )
                        .then((res) => {
                            console.log("🚀 ~ res ppobkey inquiry:", res);
                            if (res.data.status === 2) {
                                mdtoast(res.data.message, {
                                    duration: 3000,
                                    type: mdtoast.ERROR,
                                });
                            }
                            product.ppobkey_inquiry_data = res.data;
                            product.ppob_tr_id = res.data.tr_id;
                            product.temp.loading_ppobkey_inquiry = false;
                        })
                        .catch((error) => {
                            console.error("🚀 ~ error ppobkey inquiry:", error);
                            mdtoast("Oops... Something went wrong!", {
                                duration: 3000,
                                type: mdtoast.ERROR,
                            });
                            product.ppobkey_inquiry_data = "";
                            product.temp.loading_ppobkey_inquiry = false;
                        })
                        .finally(() => {
                            product.temp.loading_ppobkey_inquiry = false;
                        });
                } else {
                    product.ppobkey_inquiry_data = {
                        message: "Belum lengkap! Minimal 8 digit.",
                        status: 2,
                    };
                }
            }
        },
        onInputPpobKey: _.debounce(function (product, e) {
            const ppobkey = e.target.value;
            if (product.temp.product_value.code === "EMONEY") {
                if (
                    product.temp.ppobkey_options.some(
                        (item) => item.key === ppobkey
                    )
                ) {
                    product.temp.ppobkey_lastpurchase = ppobkey;
                } else {
                    product.temp.ppobkey_lastpurchase = "";
                }
                if (ppobkey.length < 11) {
                    product.ppobkey_inquiry_data = {
                        ...product.ppobkey_inquiry_data,
                        message: "Belum lengkap! Minimal 11 digit.",
                        status: 2,
                    };
                } else {
                    product.ppobkey_inquiry_data = {
                        ...product.ppobkey_inquiry_data,
                        status: 1,
                        name: "Checked",
                    };
                }
            } else {
                this.doCheckInquiry(product, e.target.value);
            }
        }, 1000),
        onSelectPpobKeyLastPurchase(product, selectedValue) {
            if (product.temp.product_value.code === "PLN_TKN") {
                product.ppob_key = selectedValue;
                product.ppob_product_code = "";
                const ppobkeyLastPurchase = product.temp.ppobkey_options.find(
                    (item) => item.key === selectedValue
                );
                // console.log("🚀 ~ ppobkeyLastPurchase:", ppobkeyLastPurchase);
                if (ppobkeyLastPurchase) {
                    product.ppobkey_inquiry_data =
                        ppobkeyLastPurchase.inquiry_data;
                }
            } else if (product.temp.product_value.code === "EMONEY") {
                product.ppob_key = selectedValue;
                product.ppobkey_inquiry_data = {
                    ...product.ppobkey_inquiry_data,
                    status: 1,
                    name: "Checked",
                };
            } else if (product.temp.product_value.code === "AIR_PDAM") {
                product.ppob_key = selectedValue;
                this.doCheckInquiry(product, selectedValue);
                // product.ppob_product_code = "";
                // const ppobkeyLastPurchase = product.temp.ppobkey_options.find(
                //     (item) => item.key === selectedValue
                // );
                // console.log("🚀 ~ ppobkeyLastPurchase:", ppobkeyLastPurchase);
                // if (ppobkeyLastPurchase) {
                //     product.ppobkey_inquiry_data = ppobkeyLastPurchase.inquiry_data;
                //     product.ppob_tr_id = ppobkeyLastPurchase.tr_id;
                // }
            }
        },
        onClickAddProduct() {
            this.input.products.push(this.getDefaultInputProduct());
        },
        onClickRemoveProduct(index) {
            if (confirm("Hapus produk dari keranjang?")) {
                this.input.products.splice(index, 1);
            }
        },
        getParamValue(key) {
            return new URLSearchParams(window.location.search).get(key);
        },
        doSaveJob() {
            this.temp.isLoadingSubmit = true;
            const ini = this;
            axios
                .post("/manager/order-post-vue", {
                    ...this.input,
                })
                // .get("/manager/order-post-vue", {
                //     params: {
                //         ...this.input,
                //     },
                // })
                .then((response) => {
                    console.log("🚀 ~ response:", response);
                    if (response?.data?.url_redirect) {
                        Swal.fire({
                            title: "Job Saved!",
                            text: "Redirecting...",
                            icon: "success",
                            showConfirmButton: false,
                            // confirmButtonText: "Tutup",
                        });
                        window.location.href = response?.data?.url_redirect;
                    } else {
                        mdtoast("Error, something went wrong!", {
                            duration: 1500,
                            type: mdtoast.ERROR,
                        });
                        this.temp.isLoadingSubmit = false;
                    }
                })
                .catch((error) => {
                    console.log("🚀 ~ error:", error);
                    const errorsData = error?.response?.data;
                    console.log("🚀 ~ errorsData:", errorsData);
                    const errors = error?.response?.data?.errors;
                    // console.log("🚀 ~ errors inputs:", errors);
                    if (!errors) {
                        mdtoast(errorsData?.message ?? "TERJADI KESALAHAN", {
                            duration: 1500,
                            type: mdtoast.ERROR,
                        });
                    }
                    this.temp.errors = errors;
                    const firstErrorKey = Object.keys(errors)[0];
                    // console.log("🚀 ~ firstErrorKey:", firstErrorKey);
                    const firstErrorMessage = errors[firstErrorKey][0];
                    ini.$nextTick(() => {
                        let firstErrorScrollView =
                            ini.$refs[`scrollview-${firstErrorKey}`];
                        if (Array.isArray(firstErrorScrollView)) {
                            firstErrorScrollView = firstErrorScrollView[0];
                        }
                        // console.log(
                        //     "🚀 ~ firstErrorScrollView:",
                        //     firstErrorScrollView
                        // );
                        let firstErrorInput =
                            ini.$refs[`input-${firstErrorKey}`];
                        if (Array.isArray(firstErrorInput)) {
                            firstErrorInput = firstErrorInput[0];
                        }
                        if (firstErrorInput?.$el) {
                            firstErrorInput = firstErrorInput.$el;
                        }
                        // console.log("🚀 ~ firstErrorInput:", firstErrorInput);
                        if (firstErrorScrollView) {
                            firstErrorScrollView.scrollIntoView();
                        }
                        if (firstErrorInput) {
                            setTimeout(() => {
                                firstErrorInput.focus();
                            }, 60);
                        }
                        if (!firstErrorScrollView || !firstErrorInput) {
                            mdtoast(firstErrorMessage, {
                                duration: 1500,
                                type: mdtoast.ERROR,
                            });
                        }
                    });
                    this.temp.isLoadingSubmit = false;
                });
        },
        onClickSubmit(e) {
            const oh = moment(this.current_store_open_hour, "hh:mm:ss");
            // console.log("🚀 ~ oh:", oh);
            const ch = moment(this.current_store_close_hour, "hh:mm:ss");
            // console.log("🚀 ~ ch:", ch);
            if (!moment(this.input.created_at).isBetween(oh, ch)) {
                if (
                    confirm(
                        `Maaf, jam operasional toko ini:\n${oh.format(
                            "hh:mm A"
                        )} - ${ch.format("hh:mm A")}\nLanjutkan SAVE JOB?`
                    )
                ) {
                    this.doSaveJob();
                } else {
                    e.preventDefault();
                }
            } else {
                this.doSaveJob();
            }
        },
    },
    computed: {
        isShowInputProducts() {
            let isShow = false;
            if (this.input.order_type === "old") {
                isShow = this.input.address_id ? true : false;
                // isShow = true;
            }
            if (
                ["new", "asdt", "konsumsitoko"].includes(this.input.order_type)
            ) {
                isShow = true;
            }
            return isShow;
        },
        isShowNoteEtc() {
            let isShow = false;
            if (this.input.order_type === "old") {
                isShow = this.input.address_id ? true : false;
                // isShow = true;
            }
            if (["asdt", "konsumsitoko"].includes(this.input.order_type)) {
                const isProductIsset = this.input.products.some(
                    (product) => product.product_id
                );
                isShow = isProductIsset;
            }
            if (["new", "marketing"].includes(this.input.order_type)) {
                isShow = true;
            }
            return isShow;
        },
        countProductVariants() {
            const uniqueProductIds = new Set(
                this.input.products.map((product) => product.product_id)
            );
            return uniqueProductIds.size;
        },
        countProductQty() {
            return this.input.products.reduce(
                (acc, item) => acc + (parseInt(item.qty) || 0),
                0
            );
        },
    },
    watch: {
        "input.order_type": function (newValue, oldValue) {
            const url = new URL(window.location.href);
            url.searchParams.set("t", newValue);
            window.history.replaceState({}, "", url);
            this.input = {
                order_type: newValue,
                ...this.getDefaultInput(),
            };
            this.temp = this.getDefaultTemp();
            if (["new", "asdt"].includes(newValue)) {
                this.getOptionsProducts();
            }
        },
        "input.store_id": function (newValue, oldValue) {
            const store = this.stores.find((s) => s.id === parseInt(newValue));
            const url = new URL(
                this.url_jobs_temp.replace("xxx", store.slug),
                window.location.origin
            );
            const params = new URLSearchParams(window.location.search);
            url.search = params.toString();
            window.location.replace(url.toString());
        },
        "input.store_id_new": function (newValue, oldValue) {
            const store = this.stores.find((s) => s.id === parseInt(newValue));
            const url = this.url_jobs_temp.replace("xxx", store.slug);
            window.location.replace(url + "?t=new");
        },
        "input.store_id_marketing": function (newValue, oldValue) {
            const store = this.stores.find((s) => s.id === parseInt(newValue));
            const url = this.url_jobs_temp.replace("xxx", store.slug);
            window.location.replace(url + "?t=marketing");
        },
        "temp.valueArea": function (newValue, oldValue) {
            console.log("🚀 ~ newValue:", newValue);
            // Empty
            if (!newValue) {
                this.input.area_id = "";

                // Isset
            } else {
                this.input.area_id = newValue ? newValue.id : "";
            }
        },
        "temp.valueCustomerAddress": function (newValue, oldValue) {
            // Empty
            if (!newValue) {
                this.input.address_id = "";
                this.input.customer_id = "";
                this.input.is_secondfloor = false;
                this.input.store_id_old = "";
                this.getOptionsProducts();

                // Isset
            } else {
                this.input.address_id = newValue ? newValue.id : "";
                this.input.customer_id = newValue ? newValue.customer_id : "";
                this.input.is_secondfloor = newValue?.is_secondfloor ?? false;
                this.input.store_id_old = newValue?.store.id ?? "";
                this.getOptionsProducts(newValue?.id);
            }
            // Default
            this.input.products = [this.getDefaultInputProduct()];
            this.input.is_add_another_address = false;
        },
        // "input.address_id": function (newValue, oldValue) {
        //     // Default
        //     this.input.products = [this.getDefaultInputProduct()];
        //     this.input.is_add_another_address = false;

        //     // Empty
        //     if (!newValue) {
        //         this.temp.optionsProducts = [];

        //         // Isset
        //     } else {
        //         this.getOptionsProducts(newValue);
        //     }
        // },
        "input.is_add_another_address": function (newValue, oldValue) {
            if (newValue) {
                // this.input.address_id = "";
                // this.input.customer_name = "";
                // this.input.customer_phone = "";
                // this.input.customer_address = "";
                // this.input.customer_latlng = "";
                // this.input.is_secondfloor = false;
                // this.input.is_add_another_address = false;
            }
        },
        // input: {
        //     handler(newVal, oldVal) {
        //         console.log("🚀 ~ oldVal:", oldVal);
        //         console.log("🚀 ~ newVal:", newVal);
        //         for (const key in oldVal) {
        //             if (oldVal[key] !== newVal[key]) {
        //                 console.log(
        //                     `Value of ${key} changed from ${oldVal[key]} to ${newVal[key]}`
        //                 );
        //                 delete this.temp.errors[key];
        //             }
        //         }
        //     },
        //     deep: true,
        // },
        // "input.store_id": function (newValue, oldValue) {
        //     console.log("newValue", newValue);
        //     this.input.address_id = null;
        //     this.input.discount = 0;
        //     this.orders = [];
        //     const ini = this;
        //     ini.selectizeCustomerId.clear(true);
        //     ini.selectizeCustomerId.clearOptions(true);
        //     ini.selectizeCustomerId.disable();
        //     ini.selectizeCustomerId.settings.placeholder = "Loading...";
        //     ini.selectizeCustomerId.updatePlaceholder();
        //     axios
        //         .get("/manager/invoice-get-customers/" + newValue)
        //         .then((response) => {
        //             // handle success
        //             console.log("response", response);
        //             if (
        //                 response &&
        //                 response.data &&
        //                 response.data.customers &&
        //                 response.data.customers.length > 0
        //             ) {
        //                 const dataFiltered = response.data.customers.map(
        //                     (customer) => {
        //                         if (
        //                             parseInt(customer.merger_id) &&
        //                             !parseInt(customer.is_main)
        //                         ) {
        //                             return customer.merger.maincustomer;
        //                         } else {
        //                             return customer;
        //                         }
        //                     }
        //                 );
        //                 const dataUnique = [
        //                     ...new Map(
        //                         dataFiltered.map((m) => [m.id, m])
        //                     ).values(),
        //                 ];
        //                 console.log("dataUnique", dataUnique);
        //                 console.log("dataFiltered", dataUnique);
        //                 ini.selectizeCustomerId.settings.placeholder =
        //                     "Cari nama / no.whatsapp ...";
        //                 ini.selectizeCustomerId.updatePlaceholder();
        //                 ini.selectizeCustomerId.addOption(dataFiltered);
        //                 ini.selectizeCustomerId.refreshOptions(false);
        //                 ini.selectizeCustomerId.enable();
        //             } else {
        //                 console.log("masuk data kosong");
        //                 ini.selectizeCustomerId.settings.placeholder =
        //                     "Cari nama / no.whatsapp ...";
        //                 ini.selectizeCustomerId.updatePlaceholder();
        //                 // ini.selectizeCustomerId.enable();
        //                 mdtoast("TIDAK ADA PELANGGAN BER-AR", {
        //                     duration: 1500,
        //                     type: mdtoast.INFO,
        //                 });
        //             }
        //         })
        //         .catch((error) => {
        //             // handle error
        //             console.log(error);
        //             ini.selectizeCustomerId.settings.placeholder =
        //                 "Cari nama / no.whatsapp ...";
        //             ini.selectizeCustomerId.updatePlaceholder();
        //             // ini.selectizeCustomerId.enable();
        //             mdtoast("TERJADI KESALAHAN", {
        //                 duration: 1500,
        //                 type: mdtoast.ERROR,
        //             });
        //         });
        // },
        // orders: function (newValue, oldValue) {
        //     if (newValue && newValue.length > 0) {
        //         const ttlBill = newValue.reduce(
        //             (acc, item) =>
        //                 parseInt(acc) + parseInt(item.total_after_deposit),
        //             0
        //         );
        //         this.input.total_bill = ttlBill;
        //         this.input.total_after_discount = ttlBill - this.input.discount;
        //     } else {
        //         this.input.total_bill = 0;
        //         this.input.total_after_discount = 0;
        //     }
        // },
    },
};
</script>
