<template>
    <div>
        <p class="px-4 py-0.5 text-xs border-b bg-gray-400 text-white">
            Menampilkan
            <span class="font-bold"
                >{{ operatingcosts.length }} Pengeluaran</span
            >
        </p>
        <div
            v-for="(operatingcost, index) in operatingcosts"
            :key="operatingcost.id"
            :class="[
                'bg-white px-4 py-4 border-solid border-gray-300 border-b-2 relative flex flex-col',
            ]"
        >
            <!-- <div class="break-words">
                {{ JSON.stringify(operatingcost) }}
            </div> -->
            <h4 class="text-lg font-bold">
                <a
                    class="underline text-blue-700"
                    :href="'/cs/form/operating_cost/' + operatingcost.id"
                    >NO-{{ index + 1 }} ↗</a
                >
                <span class="ml-1 text-xs text-gray-400"
                    >🕐
                    {{
                        moment(operatingcost.submitted_at).format("HH:mm")
                    }}</span
                >
            </h4>
            <div class="flex gap-1">
                <span
                    :class="[
                        'px-2 py-0.5 mb-1 text-xs font-bold rounded inline-block text-white uppercase bg-yellow-700',
                    ]"
                >
                    KATEGORI:
                    <span class="capitalize">{{
                        operatingcost.costcategory.title
                    }}</span>
                </span>
                <span
                    v-if="operatingcost.armada"
                    :class="[
                        'px-2 py-0.5 mb-1 ml-0.5 text-xs bg-blue-500 font-bold rounded inline-block text-white',
                    ]"
                >
                    🛵
                    {{ operatingcost.armada.licence_number }}
                </span>
            </div>
            <div
                class="mb-1.5"
                v-for="(
                    photo, index
                ) in operatingcost.operatingcostreceiptphotos"
                :key="photo.id"
            >
                <div
                    class="flex items-center h-8 mt-1 font-bold text-gray-500 uppercase"
                >
                    <p class="whitespace-nowrap">🧾</p>
                    <div
                        class="h-8 w-full ml-1.5 rounded overflow-hidden flex justify-center items-center relative shadowborder-gray-500 border"
                    >
                        <img
                            :src="photo.custom_properties.url"
                            data-zoomable
                            class="object-cover w-full h-auto pointer-events-auto"
                        />
                        <span
                            class="absolute pointer-events-none flex items-center justify-center w-full h-full text-white text-shadow-xl"
                            >LIHAT FOTO</span
                        >
                    </div>
                </div>
            </div>
            <div
                v-for="(item, indexItem) in operatingcost.items"
                :key="item.id"
                class="font-bold flex flex-col gap-0.5"
            >
                <p class="flex">
                    🔵 {{ item.costcategory.title }}
                    <span class="ml-auto"
                        >Rp{{ item.price.toLocaleString("id") }}</span
                    >
                </p>
                <p
                    v-if="item.note"
                    class="text-xs ml-5 text-gray-500 font-semibold"
                >
                    📝 {{ item.note }}
                </p>
            </div>
            <template v-if="operatingcost.note">
                <hr class="mt-3 mb-2" />
                <p class="font-bold text-gray-700">
                    📝 {{ operatingcost.note }}
                </p>
            </template>
            <span
                class="absolute index-10 top-1 right-2 opacity-40 text-xs font-bold text-gray-400"
                >{{ operatingcost.user.name }}</span
            >
        </div>
    </div>
</template>

<script>
// import OrderItem from "./OrderItem";

function parseJson(str) {
    try {
        const value = JSON.parse(str);
        return value;
    } catch (e) {
        return str;
    }
}
export default {
    // components: {
    //     OrderItem,
    // },
    props: {
        operatingcosts_original: String,
        role_id: Number,
        user_id: Number,
        username: String,
        base_url: String,
        date_now: String,
    },
    data: function () {
        return {
            operatingcosts: [],
            images: null,
        };
    },
    created() {
        this.operatingcosts = this.operatingcosts_original
            ? parseJson(this.operatingcosts_original)
            : [];
        console.log("🚀 ~ this.operatingcosts:", this.operatingcosts);
    },
    mounted() {
        this.images = mediumZoom("[data-zoomable]");
        // this.images = mediumZoom("._js-image-zoomable");
    },
    // updated() {
    //     this.images.detach();
    //     // this.images = mediumZoom("[data-zoomable]");
    //     this.images = mediumZoom("[data-zoomable]");
    // },
    // computed: {
    //     // dateNow: function () {
    //     //     return this.date_now
    //     //         ? this.date_now
    //     //         : moment().format("YYYY-MM-DD");
    //     // },
    // },
    methods: {
        moment: moment,
        // toWa(number) {
        //   if (!number) return number;
        //   let num = number;
        //   num = num.replace(/[^0-9]/g, "");
        //   // num.replace(' ', '');
        //   // num.replace('-', '');
        //   // num.replace('+', '');
        //   // num.replace('(', '');
        //   // num.replace(')', '');
        //   if (num.substr(0, 1) == "0") {
        //     num = "62" + num.substr(1);
        //   } else if (num.substr(0, 1) == "8") {
        //     num = "62" + num;
        //   }
        //   return num;
        // },
    },
    // watch: {
    //   filter_payment_method: function (newValue, oldValue) {
    //     // console.log("watch");
    //     // console.log(oldValue);
    //     // console.log(newValue);
    //   },
    // },
};
</script>
