<template>
    <div class="flex flex-col gap-5">
        <input type="hidden" :value="this.current_store_id" name="store_id" />
        <label class="relative block" for="cost_category_id">
            <div id="input_cost_category_id" class="absolute -top-16"></div>
            <div class="font-bold text-gray-700">
                Type <span class="text-red-600">*</span>
            </div>
            <select
                id="cost_category_id"
                name="cost_category_id"
                required
                class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                v-model="costCategoryId"
            >
                <option value="" selected>Pilih tipe biaya..</option>
                <option
                    v-for="costCategory in costCategories"
                    :key="parseInt(costCategory.id)"
                    :value="parseInt(costCategory.id)"
                >
                    Biaya {{ costCategory.title }}
                </option>
            </select>
        </label>

        <!-- Biaya SDM -->
        <!-- ============================= -->
        <template v-if="costCategoryId === 1">
            <div class="relative block">
                <div id="input_list_item" class="absolute -top-16"></div>
                <label for="list_item" class="font-bold text-gray-700">
                    List SDM <span class="text-red-600">*</span>
                </label>
                <div class="mt-1 flex flex-col gap-2.5">
                    <div
                        v-for="(costItem, index) in costItems"
                        :key="costItem.id"
                        class="flex flex-col w-full gap-2.5 p-1.5 bg-gray-100 border-2 border-gray-200 rounded-lg"
                    >
                        <div class="relative block">
                            <div
                                id="input_employee_id"
                                class="absolute -top-16"
                            ></div>
                            <label
                                for="employee_id"
                                class="block font-bold text-gray-700"
                            >
                                Nama SDM <span class="text-red-600">*</span>
                            </label>
                            <div class="flex items-center w-full gap-1">
                                <select
                                    id="employee_id"
                                    name="employee_id[]"
                                    required
                                    @change="onClearCostItems(index)"
                                    v-model="costItem.employeeId"
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                >
                                    <option value="" selected>
                                        Pilih sdm..
                                    </option>
                                    <option
                                        v-for="sdm in sdmList"
                                        :key="sdm.id"
                                        :value="parseInt(sdm.employee[0].id)"
                                    >
                                        {{ sdm.name }}
                                    </option>
                                </select>
                                <button
                                    v-if="index > 0"
                                    @click="onClickRemoveItem(index)"
                                    type="button"
                                    :class="[
                                        'text-red-500',
                                        index === 0 &&
                                            'opacity-25 pointer-events-none',
                                    ]"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                        class="w-6 h-6"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="relative block">
                            <div
                                id="input_receipt_photo"
                                class="absolute -top-16"
                            ></div>
                            <label
                                for="receipt_photo"
                                class="block font-bold text-gray-700"
                            >
                                Upload Nota <span class="text-red-600">*</span>
                            </label>
                            <input
                                :data-index="index"
                                class="mt-1"
                                required
                                id="receipt_photo"
                                name="receipt_photo[]"
                                type="file"
                                accept="image/*"
                                @change="onChangeInputFile"
                            />
                            <img
                                class="w-full mt-2.5"
                                :src="costItem.previewNota"
                            />
                        </div>
                        <div
                            v-if="costItem.employeeId"
                            class="relative block rounded-lg p-1.5 bg-yellow-200 border-2 border-yellow-300"
                        >
                            <div
                                id="input_list_item"
                                class="absolute -top-16"
                            ></div>
                            <div class="flex">
                                <label
                                    for="list_item"
                                    class="font-bold text-gray-700"
                                >
                                    List Item
                                    <span class="text-red-600">*</span>
                                </label>
                                <span
                                    class="pr-8 ml-auto text-sm font-black text-right"
                                    >TOTAL (Rp{{
                                        totalCost(costItem).toLocaleString(
                                            "id"
                                        )
                                    }})</span
                                >
                            </div>
                            <div class="mt-1 flex flex-col gap-2.5">
                                <div
                                    v-for="(
                                        subItem, subIndex
                                    ) in costItem.subItems"
                                    :key="subItem.id"
                                    class="flex flex-col w-full gap-1"
                                >
                                    <div class="flex items-center w-full gap-1">
                                        <select
                                            id="item_cost_category_id"
                                            :name="`item_cost_category_id[${index}][]`"
                                            required
                                            class="w-2/5 px-2 py-1 text-sm font-semibold border border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                            v-model="subItem.subCategoryId"
                                        >
                                            <option value="" selected>
                                                Pilih..
                                            </option>
                                            <option
                                                v-for="costCategory in itemCostCategories"
                                                :key="parseInt(costCategory.id)"
                                                :value="
                                                    parseInt(costCategory.id)
                                                "
                                            >
                                                {{ costCategory.title }}
                                            </option>
                                        </select>
                                        <money
                                            id="item_cost"
                                            :name="`item_cost[${index}][]`"
                                            :disabled="subIndex.isLoadingCost"
                                            value=""
                                            placeholder="0"
                                            :class="[
                                                'w-3/5 px-2 py-1 text-sm font-semibold text-right border border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                                                subItem.isLoadingCost &&
                                                    'bg-gray-100 text-gray-400 cursor-wait',
                                            ]"
                                            v-bind="money"
                                            v-model="subItem.cost"
                                        ></money>
                                        <button
                                            @click="
                                                onClickRemoveSubItem(
                                                    index,
                                                    subIndex
                                                )
                                            "
                                            type="button"
                                            :class="[
                                                'text-red-500',
                                                subIndex === 0 &&
                                                    'opacity-25 pointer-events-none',
                                            ]"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                                class="w-6 h-6"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                    <div
                                        v-if="subItem.subCategoryId === 4"
                                        class="flex items-center w-full gap-1 pr-7"
                                    >
                                        <input
                                            type="date"
                                            :name="`date_start[${index}][]`"
                                            v-model="subItem.dateStart"
                                            @change="
                                                onChangeDateRange(
                                                    index,
                                                    subIndex,
                                                    costItem.employeeId,
                                                    subItem
                                                )
                                            "
                                            class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                        <span class="text-yellow-700">to</span>
                                        <input
                                            type="date"
                                            :name="`date_end[${index}][]`"
                                            v-model="subItem.dateEnd"
                                            @change="
                                                onChangeDateRange(
                                                    index,
                                                    subIndex,
                                                    costItem.employeeId,
                                                    subItem
                                                )
                                            "
                                            class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                    </div>
                                </div>
                                <button
                                    class="w-full px-4 py-2 text-sm font-bold tracking-widest text-white uppercase bg-yellow-500 rounded-md shadow-lg _js-btn-add-product focus:outline-none"
                                    type="button"
                                    @click="onClickAddSubItem(costItem)"
                                >
                                    + Item Biaya
                                </button>
                            </div>
                        </div>
                    </div>
                    <button
                        class="w-full px-4 py-2 text-sm font-bold tracking-widest text-white uppercase bg-blue-500 rounded-md shadow-lg _js-btn-add-product focus:outline-none"
                        type="button"
                        @click="onClickAddItem"
                    >
                        + SDM
                    </button>
                </div>
            </div>
        </template>

        <!-- Biaya Armada -->
        <!-- ============================= -->
        <template v-if="costCategoryId === 5">
            <div class="relative block">
                <div id="input_armada_id" class="absolute -top-16"></div>
                <label for="armada_id" class="block font-bold text-gray-700">
                    Armada <span class="text-red-600">*</span>
                </label>
                <select
                    id="armada_id"
                    name="armada_id"
                    required
                    class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                >
                    <option value="" selected>Pilih armada..</option>
                    <option
                        v-for="armada in this.armadas"
                        :key="armada.id"
                        :value="parseInt(armada.id)"
                        :selected="
                            currentUser.employee.length > 0 &&
                            [5, 6].includes(currentRoleId) &&
                            currentUser.employee.includes(armada.employee_id)
                        "
                    >
                        {{ armada.licence_number }}
                    </option>
                </select>
            </div>
            <div class="relative block">
                <div id="input_receipt_photo" class="absolute -top-16"></div>
                <label
                    for="receipt_photo"
                    class="block font-bold text-gray-700"
                >
                    Upload Nota <span class="text-red-600">*</span>
                </label>
                <input
                    class="mt-1"
                    id="receipt_photo"
                    name="receipt_photo"
                    type="file"
                    required
                    accept="image/*"
                    @change="onChangeInputFile"
                />
                <img
                    class="w-full mt-2.5"
                    v-if="previewNotaBulananToko"
                    :src="previewNotaBulananToko"
                />
            </div>
            <div class="relative block">
                <div id="input_list_item" class="absolute -top-16"></div>
                <div class="flex">
                    <label for="list_item" class="font-bold text-gray-700">
                        List Item <span class="text-red-600">*</span>
                    </label>
                    <span class="pr-8 ml-auto text-sm font-black text-right"
                        >TOTAL (Rp{{ totalCost().toLocaleString("id") }})</span
                    >
                </div>
                <div class="mt-1 flex flex-col gap-2.5">
                    <div
                        v-for="(costItem, index) in costItems"
                        :key="costItem.id"
                        class="flex items-center w-full gap-1"
                    >
                        <select
                            id="item_cost_category_id"
                            name="item_cost_category_id[]"
                            required
                            class="w-3/5 px-2 py-1 text-sm font-semibold border border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        >
                            <option value="" selected>Pilih category..</option>
                            <option
                                v-for="costCategory in itemCostCategories"
                                :key="parseInt(costCategory.id)"
                                :value="parseInt(costCategory.id)"
                            >
                                {{ costCategory.title }}
                            </option>
                        </select>
                        <money
                            id="item_cost"
                            name="item_cost[]"
                            value=""
                            placeholder="0"
                            class="w-2/5 px-2 py-1 text-sm font-semibold text-right border border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            v-bind="money"
                            v-model="costItem.cost"
                        ></money>
                        <button
                            @click="onClickRemoveItem(index)"
                            type="button"
                            :class="[
                                'text-red-500',
                                index === 0 && 'opacity-25 pointer-events-none',
                            ]"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                class="w-6 h-6"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                />
                            </svg>
                        </button>
                    </div>
                    <button
                        class="w-full px-4 py-2 text-sm font-bold tracking-widest text-white uppercase bg-blue-500 rounded-md shadow-lg _js-btn-add-product focus:outline-none"
                        type="button"
                        @click="onClickAddItem"
                    >
                        + Item Biaya
                    </button>
                </div>
            </div>
        </template>

        <!-- Biaya Bulanan Toko -->
        <!-- ============================= -->
        <template v-if="costCategoryId === 12">
            <div class="relative block">
                <div id="input_receipt_photo" class="absolute -top-16"></div>
                <label
                    for="receipt_photo"
                    class="block font-bold text-gray-700"
                >
                    Upload Nota <span class="text-red-600">*</span>
                </label>
                <input
                    class="mt-1"
                    id="receipt_photo"
                    name="receipt_photo"
                    type="file"
                    accept="image/*"
                    required
                    @change="onChangeInputFile"
                />
                <img
                    class="w-full mt-2.5"
                    v-if="previewNotaBulananToko"
                    :src="previewNotaBulananToko"
                />
            </div>
            <div class="relative block">
                <div id="input_list_item" class="absolute -top-16"></div>
                <div class="flex">
                    <label for="list_item" class="font-bold text-gray-700">
                        List Item <span class="text-red-600">*</span>
                    </label>
                    <span class="pr-8 ml-auto text-sm font-black text-right"
                        >TOTAL (Rp{{ totalCost().toLocaleString("id") }})</span
                    >
                </div>
                <div class="mt-1 flex flex-col gap-2.5">
                    <div
                        v-for="(costItem, index) in costItems"
                        :key="costItem.id"
                        class="flex items-center w-full gap-1"
                    >
                        <select
                            id="item_cost_category_id"
                            name="item_cost_category_id[]"
                            required
                            class="w-3/5 px-2 py-1 text-sm font-semibold border border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        >
                            <option value="" selected>Pilih category..</option>
                            <option
                                v-for="costCategory in itemCostCategories"
                                :key="parseInt(costCategory.id)"
                                :value="parseInt(costCategory.id)"
                            >
                                {{ costCategory.title }}
                            </option>
                        </select>
                        <money
                            id="item_cost"
                            name="item_cost[]"
                            value=""
                            placeholder="0"
                            class="w-2/5 px-2 py-1 text-sm font-semibold text-right border border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            v-bind="money"
                            v-model="costItem.cost"
                        ></money>
                        <button
                            @click="onClickRemoveItem(index)"
                            type="button"
                            :class="[
                                'text-red-500',
                                index === 0 && 'opacity-25 pointer-events-none',
                            ]"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                                class="w-6 h-6"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                />
                            </svg>
                        </button>
                    </div>
                    <button
                        class="w-full px-4 py-2 text-sm font-bold tracking-widest text-white uppercase bg-blue-500 rounded-md shadow-lg _js-btn-add-product focus:outline-none"
                        type="button"
                        @click="onClickAddItem"
                    >
                        + Item Biaya
                    </button>
                </div>
            </div>
        </template>
        <tempate v-if="costCategoryId">
            <label for="note" class="block">
                <div class="font-bold text-gray-700">Catatan</div>
                <textarea
                    id="note"
                    name="note"
                    class="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    rows="2"
                ></textarea>
            </label>
            <button
                type="submit"
                @click="onClickSubmit"
                :class="[
                    'px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none transition ease-in-out duration-150 w-full bg-red-600 relative mt-5 mb-5',
                    (isLoadingSubmit || costItems.length == 0) &&
                        'opacity-50 pointer-events-none',
                ]"
            >
                <svg
                    v-if="isLoadingSubmit"
                    class="absolute top-0 h-full ml-auto text-white animate-spin w-7 left-5"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                    ></circle>
                    <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                </svg>
                <svg
                    v-if="!isLoadingSubmit"
                    class="w-7 h-full ml-auto absolute -top-0.5 left-5 _js-btn-icon"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                    />
                </svg>
                {{ isLoadingSubmit ? "SUBMITTING" : "SUBMIT" }}
            </button>
        </tempate>
    </div>
</template>

<script>
import { Money } from "v-money";
export default {
    components: { Money },
    props: {
        current_user: Object,
        sdm: Array,
        armadas: Array,
        stores: Array,
        role_id: Number,
        current_store_id: Number,
        cost_categories: Array,
    },
    data: function () {
        return {
            isLoadingSubmit: false,
            money: {
                decimal: ",",
                thousands: ".",
                prefix: "Rp",
                suffix: "",
                precision: 0,
                masked: false /* doesn't work with directive */,
            },
            currentRoleId: null,
            currentUser: null,
            costCategories: [],
            sdmList: [],
            costCategoryId: "",
            costItems: [
                {
                    id: Date.now(),
                    cost: 0,
                },
            ],
            previewNotaBulananToko: null,
        };
    },
    computed: {
        itemCostCategories() {
            const costCategoryId = this.costCategoryId;
            return this.cost_categories.filter(
                (item) => item.parent_id === costCategoryId
            );
        },
    },
    created() {
        this.currentRoleId = this.role_id;
        this.currentUser = this.current_user;
        this.sdmList = this.sdm.filter((item) => item.employee.length > 0);
        console.log("🚀 ~ sdm:", this.sdm);
        this.costCategories = this.cost_categories.filter((item) => {
            const notParent = !item.parent_id;
            const sdmOnlyForOwner =
                item.id === 1 ? (this.role_id <= 2 ? true : false) : true;
            const driverOnlyShowArmada =
                this.role_id === 5 ? (item.id === 5 ? true : false) : true;
            return notParent && sdmOnlyForOwner && driverOnlyShowArmada;
        });
    },
    mounted() {},
    methods: {
        moment: moment,
        onClearCostItems(index) {
            const newObj = {
                id: Date.now(),
                subCategoryId: "",
                isLoadingCost: false,
                dateStart: "",
                dateEnd: "",
                cost: 0,
            };
            this.costItems[index].subItems = [newObj];
        },
        onChangeDateRange(index, subIndex, employeeId, subItem) {
            // console.log("🚀 ~ employeeId:", employeeId);
            // console.log("🚀 ~ subItem:", subItem);
            const ini = this;
            if (subItem.dateStart && subItem.dateEnd) {
                Vue.set(
                    ini.costItems[parseInt(index)].subItems[parseInt(subIndex)],
                    "isLoadingCost",
                    true
                );
                axios
                    .get("/manager/insentif/driver", {
                        params: {
                            store_id: this.current_store_id,
                            employee_id: employeeId,
                            date_start: subItem.dateStart,
                            date_end: subItem.dateEnd,
                        },
                    })
                    .then((res) => {
                        console.log("res", res);
                        if (res && res.data) {
                            const cost = res.data * 1000;
                            console.log("🚀 ~ cost:", cost);
                            console.log(
                                "ini.costItems[parseInt(index)].subItems[parseInt(subIndex)]",
                                ini.costItems[parseInt(index)].subItems[
                                    parseInt(subIndex)
                                ]
                            );
                            Vue.set(
                                ini.costItems[parseInt(index)].subItems[
                                    parseInt(subIndex)
                                ],
                                "cost",
                                cost
                            );
                        }
                        Vue.set(
                            ini.costItems[parseInt(index)].subItems[
                                parseInt(subIndex)
                            ],
                            "isLoadingCost",
                            false
                        );
                    })
                    .catch((error) => {
                        console.log("error", error);
                        Vue.set(
                            ini.costItems[parseInt(index)].subItems[
                                parseInt(subIndex)
                            ],
                            "isLoadingCost",
                            false
                        );
                        // mdtoast("TERJADI KESALAHAN", {
                        //     duration: 1500,
                        //     type: mdtoast.ERROR,
                        // });
                    });
            }
        },
        totalCost(item = null) {
            const costCategoryId = this.costCategoryId;
            if ([5, 12].includes(costCategoryId)) {
                return this.costItems.reduce((accumulator, currentObject) => {
                    return accumulator + currentObject.cost;
                }, 0);
            } else if ([1].includes(costCategoryId)) {
                if (!item) return 0;
                return item.subItems.reduce((accumulator, currentObject) => {
                    return accumulator + currentObject.cost;
                }, 0);
            } else {
                return 0;
            }
        },
        onChangeInputFile(e) {
            const input = e.target;
            const file = e.target.files[0];
            const index = input.getAttribute("data-index");
            console.log("🚀 ~ index:", index);
            if (index) {
                const preview = file ? URL.createObjectURL(file) : null;
                Vue.set(
                    this.costItems[parseInt(index)],
                    "previewNota",
                    preview
                );
            } else {
                this.previewNotaBulananToko = file
                    ? URL.createObjectURL(file)
                    : null;
            }
        },
        onClickSubmit() {
            if ($("._js-form")[0].checkValidity()) {
                this.isLoadingSubmit = true;
                // $("._js-form").submit();
            }
        },
        onClickAddItem() {
            const costCategoryId = this.costCategoryId;
            let newObj;
            if ([5, 12].includes(costCategoryId)) {
                newObj = {
                    id: Date.now(),
                    cost: 0,
                };
            } else if ([1].includes(costCategoryId)) {
                newObj = {
                    id: Date.now(),
                    previewNota: null,
                    employeeId: "",
                    subItems: [
                        {
                            id: Date.now(),
                            subCategoryId: "",
                            isLoadingCost: false,
                            dateStart: "",
                            dateEnd: "",
                            cost: 0,
                        },
                    ],
                };
            }
            this.costItems.push(newObj);
        },
        onClickAddSubItem(child) {
            const newObj = {
                id: Date.now(),
                subCategoryId: "",
                isLoadingCost: false,
                dateStart: "",
                dateEnd: "",
                cost: 0,
            };
            child.subItems.push(newObj);
        },
        onClickRemoveItem(index) {
            if (confirm("Hapus item ini?")) {
                this.costItems.splice(index, 1);
            }
        },
        onClickRemoveSubItem(index, subIndex) {
            if (confirm("Hapus item ini?")) {
                this.costItems[index].subItems.splice(subIndex, 1);
            }
        },
    },
    watch: {
        // costItems: {
        //     handler: function (newValue, oldValue) {
        //         console.log("🚀 ~ newValue:", newValue);
        //     },
        //     deep: true,
        // },
        costCategoryId: function (newValue, oldValue) {
            if (newValue === 1) {
                this.costItems = [
                    {
                        id: Date.now(),
                        previewNota: null,
                        employeeId: "",
                        subItems: [
                            {
                                id: Date.now(),
                                subCategoryId: "",
                                isLoadingCost: false,
                                dateStart: "",
                                dateEnd: "",
                                cost: 0,
                            },
                        ],
                    },
                ];
            } else {
                this.costItems = [
                    {
                        id: Date.now(),
                        subCategoryId: "",
                        isLoadingCost: false,
                        dateStart: "",
                        dateEnd: "",
                        cost: 0,
                    },
                ];
            }
        },
    },
};
</script>
