<template>
    <div>
        <p class="px-4 py-0.5 text-xs border-b bg-gray-400 text-white">
            Menampilkan
            <span class="font-bold">{{ ordersFiltered.length }} Jobs</span>
        </p>
        <div
            v-for="(order, index) in ordersFiltered"
            :key="order.id"
            :class="[
                'bg-white px-4 py-4 border-solid border-gray-300 border-b-2 relative animate__animated',
                '_js-' + order.type + '-item-' + order.id,
            ]"
        >
            <template v-if="order.type == 'order'">
                <order-item
                    :orderRaw="order"
                    :roleId="roleId"
                    :userId="userId"
                    :baseUrl="baseUrl"
                    :dateNow="dateNow"
                    :status="status"
                    :username="username"
                    :driversParsed="driversParsed"
                />
            </template>
            <template v-if="order.type == 'marketing'">
                <marketing-item
                    :order="order"
                    :roleId="roleId"
                    :baseUrl="baseUrl"
                    :dateNow="dateNow"
                    :driversParsed="driversParsed"
                />
            </template>
            <template v-if="order.type == 'invoice'"
                ><invoice-item
                    :order="order"
                    :roleId="roleId"
                    :baseUrl="baseUrl"
                    :dateNow="dateNow"
                    :banks="order.store.banks"
            /></template>
        </div>
    </div>
</template>

<script>
// import OrderReturn from "./OrderReturn";
// import OrderFinish from "./OrderFinish";
// import OrderConfirm from "./OrderConfirm";
import OrderItem from "./OrderItem";
import MarketingItem from "./MarketingItem";
import InvoiceItem from "./InvoiceItem";

function parseJson(str) {
    try {
        const value = JSON.parse(str);
        return value;
    } catch (e) {
        return str;
    }
}
export default {
    components: {
        // OrderReturn,
        // OrderFinish,
        // OrderConfirm,
        OrderItem,
        MarketingItem,
        InvoiceItem,
    },
    props: {
        orders: String,
        marketings: String,
        invoices: String,
        search: String,
        role_id: Number,
        user_id: Number,
        username: String,
        base_url: String,
        date_now: String,
        drivers: String,
        filter_payment_method: String,
        status: String,
        job_type: String,
    },
    data: function () {
        return {
            // popupBatal: null,
            offlineData: null,
            // images: null,
            // color: {
            //   order: "bg-yellow-500",
            //   assigned: "bg-blue-500",
            //   progress: "bg-blue-500",
            //   completed: "bg-blue-700",
            //   canceled: "bg-red-500",
            //   paid: "bg-green-500",
            // },
            // label: {
            //   order: "bebas",
            //   assigned: "diambil",
            //   progress: "diambil",
            //   completed: "terkirim",
            //   canceled: "batal",
            //   paid: "selesai",
            // },
        };
    },
    created() {
        const gp_user_role_id = localStorage.getItem("gp_user_role_id");
        const gp_user_id = localStorage.getItem("gp_user_id");
        const gp_base_url = localStorage.getItem("gp_base_url");
        const gp_drivers = localStorage.getItem("gp_drivers");
        const gp_current_orders = localStorage.getItem("gp_current_orders");
        const gp_orders_finished = localStorage.getItem("gp_orders_finished");
        const gp_orders_canceled = localStorage.getItem("gp_orders_canceled");
        const gp_current_orders_parsed = gp_current_orders
            ? parseJson(parseJson(gp_current_orders))
            : [];
        const gp_orders_finished_parsed = gp_orders_finished
            ? parseJson(parseJson(gp_orders_finished))
            : [];
        const gp_orders_canceled_parsed = gp_orders_canceled
            ? parseJson(parseJson(gp_orders_canceled))
            : [];
        this.offlineData = {
            roleId: gp_user_role_id ? this.safeParseInt(gp_user_role_id) : null,
            userId: gp_user_id ? this.safeParseInt(gp_user_id) : null,
            baseUrl: gp_base_url ? gp_base_url : null,
            drivers: gp_drivers ? parseJson(parseJson(gp_drivers)) : [],
            orders: gp_current_orders_parsed.filter((obj) => {
                const findFinished = gp_orders_finished_parsed.findIndex(
                    (finished) =>
                        this.safeParseInt(finished.order_id) ===
                        this.safeParseInt(obj.id)
                );
                const findCanceled = gp_orders_canceled_parsed.findIndex(
                    (canceled) =>
                        this.safeParseInt(canceled.order_id) ===
                        this.safeParseInt(obj.id)
                );
                return findFinished < 0 && findCanceled < 0;
            }),
        };
    },
    mounted() {
        // console.log("Example component mounted");
        // this.images = mediumZoom("[data-zoomable]");
        // this.popupBatal = tippy("._js-btn-popup-batal", {
        //   content(reference) {
        //     const id = reference.getAttribute("data-popup-batal");
        //     const template = document.getElementById(id);
        //     return template.innerHTML;
        //   },
        //   allowHTML: true,
        //   trigger: "click",
        // });
        // $("body").on("click", "._js-btn-close-popup-batal", () => {
        //   this.popupBatal.forEach((el) => {
        //     console.log("close tippy");
        //     el.hide();
        //   });
        // });
    },
    updated() {
        // this.images.detach();
        // this.images = mediumZoom("[data-zoomable]");
        // if (this.popupBatal) {
        //   this.popupBatal.forEach((el) => {
        //     el.destroy();
        //   });
        //   this.popupBatal = tippy("._js-btn-popup-batal", {
        //     content(reference) {
        //       const id = reference.getAttribute("data-popup-batal");
        //       const template = document.getElementById(id);
        //       return template.innerHTML;
        //     },
        //     allowHTML: true,
        //     trigger: "click",
        //   });
        // }
    },
    computed: {
        roleId: function () {
            return this.role_id ? this.role_id : this.offlineData.roleId;
        },
        userId: function () {
            return this.user_id ? this.user_id : this.offlineData.userId;
        },
        baseUrl: function () {
            return this.base_url ? this.base_url : this.offlineData.baseUrl;
        },
        dateNow: function () {
            return this.date_now
                ? this.date_now
                : moment().format("YYYY-MM-DD");
        },
        ordersFiltered: function () {
            const orders = this.orders
                ? parseJson(this.orders)
                : this.offlineData.orders;

            const dataMerger = [
                ...parseJson(this.invoices),
                ...parseJson(this.marketings),
                ...orders,
            ];

            console.log("dataMerger", dataMerger);

            const data = dataMerger.filter((order) => {
                // console.log("this.filter_payment_method", this.filter_payment_method);
                // console.log(
                //   "order.payment_method_confirmed",
                //   order.payment_method_confirmed
                // );
                const word = this.search ? this.search.toLowerCase() : "";
                const words = word.split(",");
                const customerName =
                    order && order.customer && order.customer.name
                        ? order.customer.name.toLowerCase()
                        : null;
                const driverName =
                    order && order.driver && order.driver.name
                        ? order.driver.name.toLowerCase()
                        : null;
                const passSearchCustomer = customerName
                    ? customerName.includes(word)
                    : false;
                const passSearchDriver = driverName
                    ? driverName.includes(word)
                    : false;
                const passSearchOrderCode = order.code.includes(
                    this.search.toUpperCase()
                );

                const filterSearchWord = word
                    ? passSearchCustomer ||
                      passSearchDriver ||
                      passSearchOrderCode
                    : true;

                const filterSearch =
                    word === ">1jam"
                        ? this.timeDiff(order).hours > 0 ||
                          this.timeDiff(order).minutes > 60
                        : filterSearchWord;

                let passFilterPaymentMethod = true;
                if (this.status === "selesai") {
                    const paymentMethod =
                        order && order.payment_method_confirmed
                            ? order.payment_method_confirmed
                            : null;
                    passFilterPaymentMethod =
                        paymentMethod === this.filter_payment_method
                            ? true
                            : false;
                } else if (
                    this.status === "bebas" ||
                    this.status === "batal" ||
                    this.status === "diambil"
                ) {
                    const paymentMethod =
                        order && order.payment ? order.payment : "cash";
                    passFilterPaymentMethod =
                        paymentMethod === this.filter_payment_method
                            ? true
                            : false;
                } else if (
                    this.status === "terkirim" ||
                    this.status === "total"
                ) {
                    let paymentMethod =
                        order && order.payment_method_ask
                            ? order.payment_method_ask
                            : null;
                    paymentMethod =
                        paymentMethod !== "cash" ? "non-tunai" : "cash";
                    switch (this.filter_payment_method) {
                        case "unpaid":
                            passFilterPaymentMethod =
                                paymentMethod === "non-tunai" &&
                                !order.payment_proof_url;
                            break;
                        case "non-tunai":
                        case "cash":
                        default:
                            passFilterPaymentMethod =
                                paymentMethod === this.filter_payment_method
                                    ? true
                                    : false;
                            break;
                    }
                }

                const filterPayment = this.filter_payment_method
                    ? passFilterPaymentMethod
                    : true;

                const filterStatus =
                    !this.status || this.status === "total"
                        ? this.safeParseInt(order.status_id) !== 5
                        : true;

                let filterType = this.job_type
                    ? this.job_type === order.type
                    : order.type !== "invoice";

                if (this.job_type == "order" && this.status == "selesai") {
                    filterType = true;
                }

                return (
                    filterSearch && filterPayment && filterStatus && filterType
                );
            });

            if (!this.status || this.status === "total") {
                data.sort(
                    (a, b) => new Date(b.created_at) - new Date(a.created_at)
                );
            }

            console.log("data orders", data);
            return data;
        },
        driversParsed: function () {
            return this.drivers
                ? parseJson(this.drivers)
                : this.offlineData.drivers;
        },
    },
    methods: {
        // moment: moment,
        // toWa(number) {
        //   if (!number) return number;
        //   let num = number;
        //   num = num.replace(/[^0-9]/g, "");
        //   // num.replace(' ', '');
        //   // num.replace('-', '');
        //   // num.replace('+', '');
        //   // num.replace('(', '');
        //   // num.replace(')', '');
        //   if (num.substr(0, 1) == "0") {
        //     num = "62" + num.substr(1);
        //   } else if (num.substr(0, 1) == "8") {
        //     num = "62" + num;
        //   }
        //   return num;
        // },
        // timeDiff: function (order) {
        //   // const date1 = new Date(order.created_at); // 9:00 AM
        //   // const date2 = new Date(order.received_at); // 5:00 PM
        //   // // the following is to handle cases where the times are on the opposite side of
        //   // // midnight e.g. when you want to get the difference between 9:00 PM and 5:00 AM
        //   // if (date2 < date1) {
        //   //     date2.setDate(date2.getDate() + 1);
        //   // }
        //   // const diff = date2 - date1;
        //   // console.log("diff", diff);
        //   // let msec = diff;
        //   // const hh = Math.floor(msec / 1000 / 60 / 60);
        //   // msec -= hh * 1000 * 60 * 60;
        //   // const mm = Math.floor(msec / 1000 / 60);
        //   // msec -= mm * 1000 * 60;
        //   // const ss = Math.floor(msec / 1000);
        //   // msec -= ss * 1000;
        //   const start = moment(order.created_at);
        //   if (start.hours() < 7) {
        //     start.hours(7);
        //   }
        //   const end = moment(order.received_at);
        //   const diff = end.diff(start);
        //   // console.log("diff", diff);
        //   if (diff > 0) {
        //     let msec = diff;
        //     const hh = Math.floor(msec / 1000 / 60 / 60);
        //     msec -= hh * 1000 * 60 * 60;
        //     const mm = Math.floor(msec / 1000 / 60);
        //     msec -= mm * 1000 * 60;
        //     const ss = Math.floor(msec / 1000);
        //     msec -= ss * 1000;
        //     return {
        //       hours: hh,
        //       minutes: mm,
        //       seconds: ss,
        //     };
        //   } else {
        //     return {
        //       hours: 0,
        //       minutes: 0,
        //       seconds: 0,
        //     };
        //   }
        // },
    },
    // watch: {
    //   filter_payment_method: function (newValue, oldValue) {
    //     // console.log("watch");
    //     // console.log(oldValue);
    //     // console.log(newValue);
    //   },
    // },
};
</script>
