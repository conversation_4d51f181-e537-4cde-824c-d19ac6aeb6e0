<template>
  <div class="d-flex align-items-center">
    <div class="SharpTextPrepend__text" v-if="prepend" v-html="prepend"></div>
    <input
      :class="['SharpText hide-controls', { SharpTextPrepend: prepend }]"
      :type="input_type"
      :value="value"
      @input="handleChanged"
    />
  </div>
</template>

<script>
export default {
  props: {
    value: String,
    prepend: String,
    inputType: String,
    slugify: Boolean,
  },
  template: `
  <div class="d-flex align-items-center">
    <div class="SharpTextPrepend__text" v-if="prepend" v-html="prepend"></div>
    <input
      :class="['SharpText hide-controls', { SharpTextPrepend: prepend }]"
      :type="input_type"
      :value="value"
      @input="handleChanged"
    />
  </div>
  `,
  methods: {
    handleChanged(e) {
      const toKebabCase = (str) =>
        str &&
        str
          .toLowerCase()
          .replace(/ /g, "-")
          .replace(/[^\w-]+/g, "");
      const val = this.slugify ? toKebabCase(e.target.value) : e.target.value;
      this.$emit("input", val); // emit input when the value change, form data is updated
    },
  },
};
</script>

<style type="text/css" scoped>
.SharpTextPrepend {
  flex-grow: 1;
  padding-left: 0.5em;
}
.SharpTextPrepend__text {
  font-size: 14px;
  padding: 0 0.5em;
  background-color: #e0e4e8;
  line-height: 40px;
  border: 1px solid transparent;
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.1);
}
</style>
