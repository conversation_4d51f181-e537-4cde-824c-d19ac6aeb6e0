<template>
    <div
        :class="[
            'text-black w-full whitespace-normal py-1 border-gray-300',
            { 'border-t': withBorder },
        ]"
    >
        <div
            :class="[
                'flex',
                {
                    'opacity-50 pointer-events-none': !parseInt(
                        option.is_available
                    ),
                },
            ]"
        >
            <div class="mt-1 mb-1 ml-1 mr-1">
                <img
                    class="object-contain w-11 h-11"
                    :src="option.featurephoto_url"
                    :alt="option.name"
                />
            </div>
            <div class="mt-1 mb-1 mr-1">
                <div class="mb-1">
                    <span
                        class="font-bold"
                        v-html="highlightKeyword(option.code, keyword)"
                    />
                    <span
                        class="ml-1"
                        v-html="highlightKeyword(option.name, keyword)"
                    />
                    <span
                        v-if="!parseInt(option.is_available)"
                        class="ml-1 text-xs text-red-500"
                        >Tidak Tersedia
                    </span>
                </div>
                <div class="text-xs text-gray-500">
                    <span v-if="!option.is_ppob">Rp</span
                    >{{ option.price.toLocaleString("id") }}
                    <span class="ml-1" v-if="parseInt(option.stock) !== 999"
                        >(Stok {{ option.stock.toLocaleString("id") }})</span
                    >
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        option: Object,
        keyword: String,
        withBorder: Boolean,
    },
    // data: function () {
    //     return {
    //         xxx:false
    //     };
    // },
    methods: {
        highlightKeyword(words, keyword) {
            if (!keyword) {
                return words;
            }

            const escapedKeyword = keyword.replace(
                /[-\/\\^$*+?.()|[\]{}]/g,
                "\\$&"
            );
            const regex = new RegExp(`(${escapedKeyword})`, "gi");

            return words.replace(
                regex,
                '<span class="bg-yellow-200">$1</span>'
            );
        },
    },
};
</script>
