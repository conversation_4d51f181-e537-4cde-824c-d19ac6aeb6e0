<template>
    <div class="d-flex align-items-center">
        <div
            class="SharpTextPrepend__text"
            v-if="prepend"
            v-html="prepend"
        ></div>
        <money
            v-if="inputType === 'money'"
            :class="[
                'SharpText hide-controls',
                { SharpTextPrepend: prepend },
                { SharpTextReadOnly: readonly },
            ]"
            :placeholder="placeholder"
            v-model="price"
            v-bind="money"
            :readonly="readonly"
        ></money>
        <input
            v-else
            :class="[
                'SharpText hide-controls',
                { SharpTextPrepend: prepend },
                { SharpTextReadOnly: readonly },
            ]"
            :placeholder="placeholder"
            :type="type[inputType]"
            :value="value"
            @input="handleChanged"
            @change="handleChanged"
            @blur="handleChanged"
            :readonly="readonly"
        />
    </div>
</template>
<script>
import { Money } from "v-money";

export default {
    components: { Money },
    props: {
        value: String,
        prepend: String,
        inputType: String, // custom added props (given in field definition)
        placeholder: String, // custom added props (given in field definition)
        textCase: String, // custom added props (given in field definition)
        slugify: Boolean,
        readonly: Boolean,
    },
    data() {
        return {
            type: {
                text: "text",
                phone: "tel",
                color: "color",
                money: "number",
            },
            price: this.value,
            money: {
                decimal: "",
                thousands: ".",
                prefix: "Rp",
                suffix: "",
                precision: 0,
                masked: false,
            },
        };
    },
    // template: `
    //   <div class="d-flex align-items-center">
    //     <div class="SharpTextPrepend__text" v-if="prepend" v-html="prepend"></div>
    //     <money v-if="inputType === 'money'" :class="['form-control', { SharpTextPrepend: prepend }]" :placeholder="placeholder" v-model="price" v-bind="money"></money>
    //     <input v-else :class="['form-control', { SharpTextPrepend: prepend }]" :placeholder="placeholder" :type="type[inputType]" :value="value" @input="handleChanged" @change="handleChanged" @blur="handleChanged" />
    //   </div>
    // `,
    methods: {
        handleChanged(e) {
            let val = e.target.value;
            if (this.inputType === "phone") {
                val = val.replace(/[^0-9]/g, "");
                // val.replace(' ', '');
                // val.replace('-', '');
                // val.replace('+', '');
                // val.replace('(', '');
                // val.replace(')', '');
                if (val.substr(0, 1) == "0") {
                    val = "62" + val.substr(1);
                }
            }
            if (this.textCase === "uppercase") {
                val = val.toUpperCase();
            }
            if (this.slugify) {
                const toKebabCase = (str) =>
                    str &&
                    str
                        .toLowerCase()
                        .replace(/ /g, "-")
                        .replace(/[^\w-]+/g, "");
                val = toKebabCase(e.target.value);
            }
            this.$emit("input", val); // emit input when the value change, form data is updated
        },
    },
    watch: {
        price: function (val, oldVal) {
            // console.log('new: %s, old: %s', val, oldVal);
            this.$emit("input", val);
        },
    },
};
</script>

<style type="text/css" scoped>
.SharpTextPrepend {
    flex-grow: 1;
    padding-left: 0.5em;
}
.SharpTextReadOnly {
    opacity: 0.5;
}
.SharpTextPrepend__text {
    font-size: 14px;
    padding: 0 0.5em;
    background-color: #e0e4e8;
    line-height: 40px;
    border: 1px solid transparent;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.1);
}
</style>
