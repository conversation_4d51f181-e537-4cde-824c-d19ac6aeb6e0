<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap">

    <!-- Styles -->
    <link rel="stylesheet" href="{{ url('/css/mdtoast.min.css') }}" />
    <link rel="stylesheet" href="{{ url('/css/animate.min.css') }}" />
    <link rel="stylesheet" href="{{ url('/css/selectize.bootstrap3.css') }}" />
    <link rel="stylesheet" href="{{ url('/css/leaflet.awesome-markers.css') }}" />
    <link rel="stylesheet" href="{{ mix('css/app.css') }}">
    <link rel="stylesheet" href="{{ mix('css/manager.css') }}">
    <script src="{{ url('/js/webcam-easy.min.js') }}"></script>

    <!-- Matomo -->
    <script>
        var _paq = window._paq = window._paq || [];
    /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
    _paq.push(['trackPageView']);
    _paq.push(['enableLinkTracking']);
    (function() {
      var u="//stat.ordergasplus.online/";
      _paq.push(['setTrackerUrl', u+'matomo.php']);
      _paq.push(['setSiteId', '2']);
      var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
      g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
    })();
    </script>
    <!-- End Matomo Code -->

    <!-- Hotjar Tracking Code for Gasplus CS -->
    <script>
        (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:6373432,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>


    @laravelPWA

    @if (isset($head))
    {{ $head }}
    @endif
</head>

<body class="font-sans antialiased bg-gray-300">
    <div id="app">

        {{-- IAK Balance (Saldo) --}}
        @if(in_array(auth()->user()->role_id, [1,2]))
        <div class="fixed top-0 left-0 right-0 z-50">
            <div class="max-w-xl mx-auto relative">
                <button type="button" style="top: 53px; right: 3px;"
                    class="_js-refresh-iak-balance shadow flex absolute z-50 rounded justify-center items-center gap-1 flex-row-reverse px-1 py-0.5  bg-red-900 text-white">
                    <div class="flex items-center justify-center text-xs font-black ">
                        Rp<span class="_js-iak-balance">{{ number_format($iak_balance, 0, '', '.') }}</span> <svg
                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                            class="w-3 h-3 ml-0.5 stroke-2 _js-loading-spinner">
                            <path fill-rule="evenodd"
                                d="M15.312 11.424a5.5 5.5 0 0 1-9.201 2.466l-.312-.311h2.433a.75.75 0 0 0 0-1.5H3.989a.75.75 0 0 0-.75.75v4.242a.75.75 0 0 0 1.5 0v-2.43l.31.31a7 7 0 0 0 11.712-*********** 0 0 0-1.449-.39Zm1.23-3.723a.75.75 0 0 0 .219-.53V2.929a.75.75 0 0 0-1.5 0V5.36l-.31-.31A7 7 0 0 0 3.239 8.188a.75.75 0 1 0 1.448.389A5.5 5.5 0 0 1 13.89 6.11l.311.31h-2.432a.75.75 0 0 0 0 1.5h4.243a.75.75 0 0 0 .53-.219Z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <span class="text-white opacity-75 _js-iak-balance-updated" style="font-size:9px;">{{
                        $iak_balance_updated_at ?? '-' }}</span>
                </button>
            </div>
        </div>
        @endif

        <!-- Page Heading -->
        <header class="fixed top-0 left-0 right-0 z-30">
            <div class="max-w-xl mx-auto bg-white shadow">
                <div class="relative flex items-center px-4 text-white bg-red-700 h-14">
                    {{ $header ?? '' }}
                </div>
                @if (isset($subheader))
                {{ $subheader }}
                @endif
            </div>
        </header>

        <!-- Page Content -->
        <main class="relative max-w-xl min-h-screen mx-auto bg-white">
            <div style="display: none"
                class="fixed left-0 right-0 z-50 flex items-center justify-center _js-loading-submit-offline-job top-14 bottom-16">
                <div
                    class="flex items-center justify-center w-full h-full max-w-xl text-xl font-bold text-gray-300 bg-white">
                    Uploading Offline Jobs...
                </div>
            </div>
            {{ $slot }}
        </main>

        <!-- Navigation -->
        <nav class="fixed bottom-0 left-0 right-0 z-30 h-16">
            <div
                class="flex items-center justify-around max-w-xl min-h-full mx-auto bg-white border-t-2 border-gray-300 border-solid shadow">
                <a href="{{ route('calendar', ['store_slug' => $store_slug]) }}"
                    class="{{ Route::currentRouteName() == 'calendar' ? 'text-red-600' : 'text-gray-600' }} flex flex-col items-center justify-center">
                    @if (Route::currentRouteName() == 'calendar')
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                            clip-rule="evenodd" />
                    </svg>
                    @else
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    @endif
                    <span class="mt-1 text-xs font-bold">Calendar</span>
                </a>
                <a href="{{ route('jobs', ['store_slug' => $store_slug]) }}"
                    class="{{ in_array(Route::currentRouteName(), ['jobs', 'job-list', 'order-add', 'order-edit']) ? 'text-red-600' : 'text-gray-600' }} flex flex-col items-center justify-center">
                    @if (in_array(Route::currentRouteName(), ['jobs', 'job-list', 'order-add', 'order-edit']))
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                            clip-rule="evenodd" />
                        <path
                            d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                    </svg>
                    @else
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    @endif
                    <span class="mt-1 text-xs font-bold">Jobs</span>
                </a>
                @if (in_array(Auth::user()->role_id, [1,2,3]) || (is_array(Auth::user()->options) &&
                array_key_exists('is_show_ltt',
                Auth::user()->options) && (int) Auth::user()->options['is_show_ltt'])) <a
                    href="{{ route('report', ['store_slug' => $store_slug]) }}" class="{{ in_array(Route::currentRouteName(), ['report', 'report.driver', 'report.product', 'report.deposit', 'report.stockopname.detail']) ? 'text-yellow-600' : 'text-gray-600' }}
                flex flex-col items-center justify-center">
                    @if (in_array(Route::currentRouteName(), ['report', 'report.driver', 'report.product',
                    'report.deposit', 'report.stockopname.detail']))
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd" />
                    </svg>
                    @else
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                    </svg>
                    @endif
                    <span class="mt-1 text-xs font-bold">Performa</span>
                </a>
                @endif
                <a href="{{ route('account', ['store_slug' => $store_slug]) }}"
                    class="{{ in_array(Route::currentRouteName(), ['account']) ? 'text-red-600' : 'text-gray-600' }} flex flex-col items-center justify-center">
                    @if (in_array(Route::currentRouteName(), ['account']))
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                            clip-rule="evenodd" />
                    </svg>
                    @else
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    @endif
                    <span class="mt-1 text-xs font-bold">{{ Auth::user() ? Auth::user()->name : ''}}</span>
                </a>
            </div>
        </nav>

    </div>

    <script src="{{ url('/js/jquery.min.js') }}"></script>
    <script src="{{ url('/js/moment.min.js') }}"></script>
    {{-- <script src="https://cdn.jsdelivr.net/npm/vue@2.6.12/dist/vue.js"></script> --}}
    <script src="{{ url('/js/mdtoast.min.js') }}"></script>
    <script src="{{ url('/js/axios.min.js') }}"></script>
    <script src="{{ url('/js/selectize.min.js') }}"></script>
    <script src="{{ url('/js/medium-zoom.min.js') }}"></script>
    {{-- <script src="https://cdn.jsdelivr.net/npm/leaflet.awesome-markers@2.0.5/dist/leaflet.awesome-markers.min.js"
        integrity="sha256-IqiRR5X1QtAdcq5lG4vBB1/WxwrRCkkjno4pfvWyag0=" crossorigin="anonymous"></script> --}}
    <script src="{{ url('/js/leaflet.awesome-markers.js') }}"></script>
    <script src="{{ url('/js/popper.min.js') }}"></script>
    <script src="{{ url('/js/tippy-bundle.umd.min.js') }}"></script>
    <script src="{{ url('/js/sweetalert2.all.min.js') }}"></script>
    {{-- <script type="module">
        import ThermalPrinterEncoder from "https://cdn.jsdelivr.net/npm/thermal-printer-encoder@2.0.0/+esm";
        const encoder = new ThermalPrinterEncoder({
                language: "esc-pos",
            });
            console.log("🚀 ~ encoder:", encoder);

            const result = encoder
                .initialize()
                .codepage("auto")
                .text("The quick brown fox jumps over the lazy dog")
                // .newline()
                // .qrcode("https://nielsleenheer.com")
                .encode();
                console.log("🚀 ~ result:", result)
    </script> --}}
    {{-- <script src="https://cdn.jsdelivr.net/npm/js-cookie@rc/dist/js.cookie.min.js"></script> --}}
    {{-- <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.full.min.js"
        integrity="sha256-gtZlnMWqbrBdDWvmCQCgfiA3kq8J4FMqQ4a8Tvmgopk=" crossorigin="anonymous"></script> --}}
    <!-- Scripts -->
    <script src="{{ mix('js/app.js') }}" defer></script>
    <script>
        function parseJson(str) {
            try {
                const value = JSON.parse(str);
                return value;
            } catch (e) {
                return str;
            }
        }
        // function successApp(pos) {
        //     var crd = pos.coords;
        //     console.log("Your current position is:");
        //     console.log(`Latitude : ${crd.latitude}`);
        //     console.log(`Longitude: ${crd.longitude}`);
        //     console.log(`More or less ${crd.accuracy} meters.`);
        //     latlng = crd.latitude+','+crd.longitude;
        //     latlng_accuracy = crd.accuracy;
        // }
        // function errorApp(err) {
        //     console.warn("ERROR(" + err.code + "): " + err.message);
        // }
        // const optionsApp = {
        //     enableHighAccuracy: true,
        //     timeout: 5000,
        //     maximumAge: 0
        // };
        // function getLocationApp() {
        //     navigator.geolocation.getCurrentPosition(
        //         successApp,
        //         errorApp,
        //         optionsApp
        //     );
        // }
        $( document ).ready(function() {

            // IAK Balance (Saldo)
            $('._js-refresh-iak-balance').on('click', function() {
                const $loadingSpinner = $(this).find('._js-loading-spinner');
                const $iakBalance = $(this).find('._js-iak-balance');
                const $iakBalanceUpdated = $(this).find('._js-iak-balance-updated');
                $loadingSpinner.addClass('animate-spin');
                console.log("🚀 ~ js-refresh-iak-balance:", $loadingSpinner, $iakBalance);
                axios.get("{{ route('iak.prepaid.check-balance') }}")
                .then(function(response) {
                    console.log('refresh iak balance:', response);
                    if (response.data.rc === "00") {
                            mdtoast('REFRESH SALDO BERHASIL', { duration: 3000, type: mdtoast.SUCCESS });
                            $iakBalance.text(new Intl.NumberFormat('id-ID').format(response.data.balance));
                            $iakBalanceUpdated.text(moment().format('YYYY-MM-DD HH:mm:ss'));
                    } else {
                        console.log('error refresh iak balance:', response);
                        mdtoast('REFRESH SALDO GAGAL', { duration: 3000, type: mdtoast.ERROR });
                    }
                })
                .catch(function (error) {
                    console.log('error refresh iak balance:', error);
                    mdtoast('REFRESH SALDO GAGAL', { duration: 3000, type: mdtoast.ERROR });
                })
                .finally(function () {
                    $loadingSpinner.removeClass('animate-spin');
                });
            });


            // getLocationApp();
            @if(session('success'))
            mdtoast("{{session('success')}}", { duration: 3000, type: mdtoast.SUCCESS });
            @endif
            @if (currentOrders())
                console.log('Set localStorage for Driver ONLY');
                localStorage.setItem('gp_user_id', '{{ Auth::user()->id }}');
                localStorage.setItem('gp_user_role_id', '{{ auth()->user()->role_id }}');
                localStorage.setItem('gp_base_url', '{{ URL::to("/") }}');
                // localStorage.setItem('gp_drivers', '{{ URL::to("/")."/cs/form/order/" }}');
                const current_orders = @json(currentOrders()->toJson());
                localStorage.setItem('gp_current_orders', JSON.stringify(current_orders));
                localStorage.setItem('gp_orders_saved_at', moment().format('YYYY-MM-DD HH:mm:ss'));
                console.log('gp_orders_finished', localStorage.getItem('gp_orders_finished'));
                const gp_orders_finished = localStorage.getItem('gp_orders_finished') ? parseJson(parseJson(localStorage.getItem('gp_orders_finished'))) : null;
                const gp_orders_canceled = localStorage.getItem('gp_orders_canceled') ? parseJson(parseJson(localStorage.getItem('gp_orders_canceled'))) : null;
                console.log('gp_orders_finished', gp_orders_finished);
                console.log('gp_orders_canceled', gp_orders_canceled);
                if (gp_orders_finished || gp_orders_canceled) {
                    $('._js-loading-submit-offline-job').fadeIn('fast');
                    $.ajax({
                        headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                        url: "{{ route('submit-offline-jobs') }}",
                        type: 'POST',
                        data: {
                            gp_orders_finished: gp_orders_finished,
                            gp_orders_canceled: gp_orders_canceled,
                        },
                        success: function(res) {
                            console.log('submit offline job:', res);
                            if (res.status) {
                                localStorage.removeItem('gp_orders_canceled');
                                localStorage.removeItem('gp_orders_finished');
                                mdtoast('SUBMIT PENYELESAIAN OFFLINE BERHASIL', { 
                                    duration: 3000, 
                                    type: mdtoast.SUCCESS,
                                    interaction: true, 
                                    actionText: 'TUTUP', 
                                    action: function(){
                                        this.hide();
                                        window.location.reload();
                                    }
                                });
                                setTimeout(() => {
                                    window.location.reload();
                                }, 3000);
                            } else {
                                $('._js-loading-submit-offline-job').fadeOut('fast');
                                mdtoast('SUBMIT PENYELESAIAN OFFLINE GAGAL', { 
                                    duration: 3000, 
                                    type: mdtoast.ERROR,
                                    interaction: true, 
                                    actionText: 'LIHAT ERROR', 
                                    action: function(){
                                        alert(JSON.stringify(res))
                                        this.hide();
                                    }
                                });
                            }
                        },
                        error: function(error) {
                            console.log('error submit offline job:', error);
                            $('._js-loading-submit-offline-job').fadeOut('fast');
                            mdtoast('SUBMIT PENYELESAIAN OFFLINE GAGAL', { 
                                    duration: 3000, 
                                    type: mdtoast.ERROR,
                                    interaction: true, 
                                    actionText: 'LIHAT ERROR', 
                                    action: function(){
                                        alert(JSON.stringify(error))
                                        this.hide();
                                    }
                                });
                        },
                    });
                }
            @endif
        });
    </script>
    @if (isset($js))
    {{ $js }}
    @endif
</body>

</html>