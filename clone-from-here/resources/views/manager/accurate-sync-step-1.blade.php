<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>Accurate Sync - Step 1</title>

  <!-- Fonts -->
  {{--
  <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet"> --}}

  <!-- Styles -->
  <link rel="stylesheet" href="{{ mix('css/app.css') }}">

  <!-- Matomo -->
  <script>
    var _paq = window._paq = window._paq || [];
  /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
  _paq.push(['trackPageView']);
  _paq.push(['enableLinkTracking']);
  (function() {
    var u="//stat.ordergasplus.online/";
    _paq.push(['setTrackerUrl', u+'matomo.php']);
    _paq.push(['setSiteId', '2']);
    var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
    g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
  })();
  </script>
  <!-- End Matomo Code -->

  <!-- Hotjar Tracking Code for Gasplus CS -->
  <script>
    (function(h,o,t,j,a,r){
      h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
      h._hjSettings={hjid:6373432,hjsv:6};
      a=o.getElementsByTagName('head')[0];
      r=o.createElement('script');r.async=1;
      r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
      a.appendChild(r);
  })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
  </script>

</head>

<body class="antialiased py-4 px-5">
  <h1 class="text-2xl font-bold">Sync Accurate (Step 1 - Upload)</h1>
  <form action="{{ route('accurate-sync-process-1')}}" method="POST" enctype="multipart/form-data"
    class="flex flex-col gap-4 max-w-md mt-5">
    @csrf
    <label class="block">
      <span class="text-gray-700">Toko</span>
      <select name="store_id" required class="form-select block w-full mt-1">
        <option value="" selected>Pilih toko...</option>
        @foreach ($stores as $store)
        <option value="{{ $store->id }}">{{ $store->name }}</option>
        @endforeach
      </select>
    </label>
    <label class="block">
      <span class="text-gray-700">File Excel</span>
      <input type="file" name="file" required class="mt-1 block w-full" accept=".xlsx">
      <p class="text-xs mt-0.5 text-red-600">*only .xlsx file & max 1.000 row</p>
    </label>
    {{-- <label class="inline-flex items-center">
      <input type="checkbox" name="is_exclude_synced">
      <span class="ml-2">Exclude synced</span>
    </label> --}}
    <button type="submit"
      class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">Upload
      & Match Data</button>
  </form>
</body>

</html>