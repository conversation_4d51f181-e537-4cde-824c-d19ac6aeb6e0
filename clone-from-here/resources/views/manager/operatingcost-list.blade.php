<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <div class="absolute text-green-900 bg-pink-500 border-green-900 pointer-events-none"></div>

    <x-slot name="header">
        <a class="flex items-center -ml-3"
            href="{{ route('jobs', ['store_slug' => $store_slug, 'date' => $date_now]) }}"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                {{-- {{ __('Job List') }} --}}
                <span class="capitalize">{{ $store_slug }}</span>
            </h2>
        </a>
    </x-slot>

    <x-slot name="subheader">
        <div class="flex w-full h-14">
            <div class="flex flex-grow flex-col p-4 justify-center h-14 text-white bg-green-700">
                <p class="text-xs font-bold capitalize">{{ date('d F Y', strtotime($date_now)) }}: Pengeluaran</p>
                @if ($operatingcosts->count())
                <p class="text-xs font-light">{{ $operatingcosts[0]->updated_at }}</p>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="pb-20 pt-28">
        <div
            class="fixed top-0 left-0 right-0 z-auto flex items-center justify-center h-screen text-5xl font-bold text-gray-300 _js-loading">
            Loading...
        </div>
        <operatingcost-list operatingcosts_original="{{ $operatingcosts->toJson() }}"
            :role_id="{{ auth()->user()->role_id }}" :user_id="{{ auth()->user()->id }}"
            username="{{ auth()->user()->name }}" base_url="{{ URL::to('/') }}" date_now="{{ $date_now }}">
        </operatingcost-list>
        {{-- @foreach ($operatingcosts as $order)
        @include('components.order-item', ['order'=> $order, 'date_now' => $date_now])
        @endforeach --}}
    </div>

    <x-slot name="js">
        <script>
            $(document).ready(function() {
                setTimeout(() => {
                    $('._js-loading').fadeOut('fast');
                }, 500);
            })
        </script>
    </x-slot>

</x-app-layout>