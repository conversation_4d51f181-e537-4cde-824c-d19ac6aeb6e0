<x-app-layout>
  <x-slot name="store_slug">
    {{ $store_slug }}
  </x-slot>

  <x-slot name="header">
    <div class="absolute inset-0 flex items-center px-4 bg-yellow-500">
      <h3 class="flex items-center text-base font-bold">
        <a href="{{ route('report.driver.list', ['store_slug' => $store_slug]) }}" class="flex items-center -ml-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          In-Job
        </a>
      </h3>
    </div>
  </x-slot>

  <x-slot name="subheader">
    <div class="flex items-center justify-center h-10 mt-1 text-yellow-500">
      <a class="w-6 h-6 _js-btn-month"
        href="{{ route('report.driver', ['ids'=> $ids, 'store_slug' => $store_slug, 'month' => $month_prev]) }}"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
            clip-rule="evenodd" />
        </svg></a>
      <input type="month" class="w-56 p-0 mx-3 text-lg font-bold text-center border-none focus:ring-0 _js-input-month"
        max="{{ date('Y-m') }}" value="{{ $month_now }}">
      @if ($month_next)
      <a class="w-6 h-6 _js-btn-month"
        href="{{ route('report.driver', ['ids'=> $ids, 'store_slug' => $store_slug, 'month' => $month_next]) }}"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
            clip-rule="evenodd" />
        </svg>
      </a>
      @endif
    </div>
    <div
      class="pt-2.5 pb-2.5 mt-0 border-t border-b border-gray-300 flex justify-center items-center text-gray-400 text-base">
      <input type="date" class="w-32 p-0 mx-3 font-bold text-center border-none focus:ring-0 _js-input-date-start"
        min="{{ $date_min }}" max="{{ $date_max }}" value="{{ $date_start }}">
      <span class="text-gray-300">to</span>
      <input type="date" class="w-32 p-0 mx-3 font-bold text-center border-none focus:ring-0 _js-input-date-end"
        min="{{ $date_min }}" max="{{ $date_max }}" value="{{ $date_end }}">
    </div>
    <div class="flex items-center text-xs font-bold text-center">
      <span class="w-16 py-2 pl-1 text-left text-blue-500 capitalize">Delman</span>
      <span class="w-10 py-2 text-blue-600">GPS</span>
      <span class="w-11 py-2 text-blue-600">JOB</span>
      <span class="w-10 py-2 text-red-600">>1jam</span>
      <div
        class="flex items-center flex-1 overflow-x-auto border-l-2 border-gray-200 scrollbar-hide bg-yellow-50 _js-scroll">
        @foreach ($categories as $category)
        <span class="flex-shrink-0 w-14 px-1 py-2 overflow-hidden text-gray-400 overflow-ellipsis whitespace-nowrap">{{
          $category->name }}</span>
        @endforeach
      </div>
    </div>
    <div class="flex items-center font-bold text-xs text-center -mt-1.5">
      <div class="w-16 py-2 pl-1 text-left text-blue-500 capitalize">TOTAL</div>
      {{-- <span class="w-12 py-2 text-blue-700">{{ $sum_total_all }}</span> --}}
      <div class="flex flex-col w-10 py-2 text-blue-600"></div>
      <div class="flex flex-col w-11 py-2 text-blue-600">
        {{ number_format($sum_all_delivered, 0, '', '.') }}
        <span class="text-blue-700">{{ number_format($sum_all_insentive, 0, '', '.').'k' }}</span>
      </div>
      {{-- <span class="w-12 py-2 text-red-700">{{ $sum_total_all_overtime }}</span> --}}
      <div class="flex flex-col w-10 py-2 text-red-600">
        {{ number_format($sum_all_delivered_overtime, 0, '', '.') }}
        <span class="text-transparent"></span>
      </div>
      <div
        class="flex items-center flex-1 overflow-x-auto border-l-2 border-gray-200 scrollbar-hide bg-yellow-50 _js-scroll">
        @foreach ($categories as $category)
        @if (array_key_exists($category->id, $sum_category_qty))
        <div class="flex flex-col flex-shrink-0 w-14 px-1 py-2 text-gray-400">
          {{ '@'.number_format($sum_category_qty[$category->id], 0, '', '.') }}
          <span class="text-gray-500">{{ number_format($sum_category_insentive[$category->id], 0, '', '.').'k' }}</span>
        </div>
        @endif
        @endforeach
      </div>
    </div>
  </x-slot>

  <div class="pt-56 pb-20">
    @foreach ($data as $driver_group)
    @if ($driver_group['sum_store_delivered'] > 0)
    <div
      class="flex items-center gap-2 px-1 py-1 text-xs font-bold text-blue-800 bg-blue-100 border-b border-gray-200 border-solid">
      <span class="text-sm">{{$driver_group['store']['name']}}</span>
      <span class="text-blue-500">
        {{ number_format($driver_group['sum_store_delivered'], 0, '', '.') }}
      </span>
      <span class="text-blue-700">
        {{ number_format($driver_group['sum_store_insentive'], 0, '', '.').'k' }}
      </span>
    </div>
    @foreach ($driver_group['driver_list'] as $driver)
    <a href="{{ url('cs/list/order?'.
      'filter_date='.date('Ymd', strtotime($date_start)).'..'.date('Ymd', strtotime($date_end)).'&'.
      'filter_status%5B0%5D=4&filter_status%5B1%5D=6&filter_status%5B2%5D=7&'.
      'filter_store%5B0%5D='.$driver_group['store']['id'].'&'.
      'filter_drivers%5B0%5D='.$driver['id']
    ) }}" target="_blank"
      class="flex items-center text-xs font-bold text-center border-b border-gray-200 border-solid">
      <div class="flex items-center justify-start w-16 pl-1 leading-none text-left text-blue-500 break-normal h-20">
        {{ $driver['name'] }}
      </div>
      <div class="flex flex-col items-center justify-center w-10 py-2 text-green-600 bg-white bg-opacity-50 h-20">
        {{ number_format($driver['count_delivered'] - $driver['count_delivered_gpsoff'], 0, '', '.') }}
        <span class="text-green-700 mb-1">{{ number_format($driver['sum_insentive'] - $driver['sum_insentive_gpsoff'],
          0,
          '', '.').'k' }}</span>
        <span class="text-red-700" style="font-size: 7px;">GPSOFF</span>
      </div>
      <div class="flex flex-col items-center justify-center w-11 py-2 text-blue-600 bg-white bg-opacity-50 h-20">
        {{ number_format($driver['count_delivered'], 0, '', '.') }}
        <span class="text-blue-700 mb-1">{{ number_format($driver['sum_insentive'], 0, '', '.').'k' }}</span>
        <span class="text-red-700">{{ number_format($driver['count_delivered_gpsoff'], 0, '',
          '.') }}</span>
        <span class="text-red-900">{{ number_format($driver['sum_insentive_gpsoff'], 0, '',
          '.').'k' }}</span>
      </div>
      <div class="flex flex-col items-center justify-center w-10 py-2 text-red-600 h-20">
        {{-- <a
          href="{{ route('job-list', ['store_slug' => $driver_group['store']['slug'], 'date' => $item['datetime'], 'status' => 'total', 'search' => '>1jam']) }}"
          class=" bg-red-100 rounded" target="_blank"> --}}
          {{ number_format($driver['count_delivered_overtime'], 0, '', '.') }}
          <span class="text-transparent mb-1"></span>
          <span class="text-red-900">{{ number_format($driver['count_delivered_overtime_gpsoff'], 0,
            '', '.')
            }}</span>
          <span class="text-transparent"></span>
          {{-- </a> --}}
      </div>
      <div class="flex items-center flex-1 overflow-x-auto border-l-2 border-gray-200 scrollbar-hide _js-scroll">
        @foreach ($driver['categories'] as $category)
        <div class="flex flex-col items-center justify-center flex-shrink-0 w-14 px-1 py-2 text-gray-400 h-20">
          {{ '@'.number_format($category['qty'], 0, '', '.') }}
          <span class="text-gray-500 mb-1">{{ number_format($category['insentive'], 0, '', '.').'k' }}</span>
          <span class="text-red-700">{{ '@'.number_format($category['qty_gpsoff'], 0, '', '.')
            }}</span>
          <span class="text-red-900">{{ number_format($category['insentive_gpsoff'], 0, '',
            '.').'k' }}</span>
        </div>
        @endforeach
      </div>
    </a>
    @endforeach
    @endif
    @endforeach
  </div>

  <div id="loading"
    class="fixed inset-0 z-50 flex items-center justify-center text-4xl font-bold text-gray-500 bg-white bg-opacity-70"
    style="display: none;">
    Loading...
  </div>



  <x-slot name="js">
    <script>
      $(document).ready(function() {
        // Change Date
        // --------------------------------------------------
        $('._js-btn-month').click(function() {
          $('#loading').show();
        });
        $('._js-input-month').change(function() {
          const url = "{{ url()->current() }}";
          const month = this.value;
          console.log(url + '?month=' + month);
          $('#loading').show();
          window.location.replace(url + '?month=' + month);
        });
        $('._js-input-date-start').change(function() {
          // const url = new URL('{{ url()->full() }}');
          const url = new URL(window.location.href);
          const datestart = this.value;
          url.searchParams.set('datestart', datestart);
          console.log("🚀 ~ url:", url)
          $('#loading').show();
          window.location.replace(url.href);
        });
        $('._js-input-date-end').change(function() {
          // const url = new URL('{{ url()->full() }}');
          const url = new URL(window.location.href);
          const dateend = this.value;
          url.searchParams.set('dateend', dateend);
          console.log("🚀 ~ url:", url)
          $('#loading').show();
          window.location.replace(url.href);
        });

        // Bind Multiple Scroll
        // --------------------------------------------------
        // $('._js-scroll').scrollsync()
        $('._js-scroll').scroll(function(e) {
          $('._js-scroll').scrollLeft(e.target.scrollLeft);
        });
        // var scrollers = document.getElementsByClassName('_js-scroll');
        // var scrollerDivs = Array.prototype.filter.call(scrollers, function(testElement) {
        //   return testElement.nodeName === 'DIV';
        // });
        // function scrollAll(scrollLeft) {
        //   scrollerDivs.forEach(function(element, index, array) {
        //     element.scrollLeft = scrollLeft;
        //   });
        // }
        // scrollerDivs.forEach(function(element, index, array) {
        //   element.addEventListener('scroll', function(e) {
        //     scrollAll(e.target.scrollLeft);
        //   });
        // });
      });

    </script>
  </x-slot>
</x-app-layout>