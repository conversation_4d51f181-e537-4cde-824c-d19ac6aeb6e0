<div role="alert" class="SharpNotification SharpNotification--info">
    <div class="SharpNotification__details">
        <div class="SharpNotification__text-wrapper">
            <div class="SharpNotification__title mb-3">
                Use these accounts to login:
            </div>
            <div class="SharpNotification__subtitle">
                <ul>
                    <li class="mb-2">
                        <input class="SharpButton SharpButton--sm SharpButton--secondary" type="button"
                            onclick="this.form.elements.login.value='<EMAIL>';this.form.elements.password.value='123123'"
                            value="Super Admin account">
                    </li>
                    <li class="mb-2">
                        <input class="SharpButton SharpButton--sm SharpButton--secondary" type="button"
                            onclick="this.form.elements.login.value='<EMAIL>';this.form.elements.password.value='123123'"
                            value="Owner account">
                    </li>
                    <li class="mb-2">
                        <input class="SharpButton SharpButton--sm SharpButton--secondary" type="button"
                            onclick="this.form.elements.login.value='<EMAIL>';this.form.elements.password.value='123123'"
                            value="CS account">
                    </li>
                    <li class="mb-2">
                        <input class="SharpButton SharpButton--sm SharpButton--secondary" type="button"
                            onclick="this.form.elements.login.value='<EMAIL>';this.form.elements.password.value='123123'"
                            value="Admin Toko account">
                    </li>
                    <li class="mb-2">
                        <input class="SharpButton SharpButton--sm SharpButton--secondary" type="button"
                            onclick="this.form.elements.login.value='<EMAIL>';this.form.elements.password.value='123123'"
                            value="Driver account">
                    </li>
                    
                </ul>
            </div>
        </div>
    </div>
</div><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/sharp/_login-page-message.blade.php ENDPATH**/ ?>