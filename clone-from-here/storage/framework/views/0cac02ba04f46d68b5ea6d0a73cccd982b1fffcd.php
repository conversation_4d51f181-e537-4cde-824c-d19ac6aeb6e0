<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <h2 class="text-lg font-bold">
            <?php echo e(__('Account')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="px-5 pt-20 pb-20">
        <div class="grid grid-cols-1 gap-6">
            <h2 class="text-2xl font-bold text-gray-700"><?php echo e(Auth::user()->name); ?></h2>
            
            <?php if(Auth::user()->role_id <= 3): ?> <label class="block">
                <span class="font-bold text-gray-700">Toko</span>
                <select
                    class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-store focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50">
                    <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e(route('account', ['store_slug' => $store->slug])); ?>" <?php echo e($store->slug ==
                        $store_slug ? "selected" : null); ?>>
                        <?php echo e($store->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                </label>
                <?php endif; ?>
        </div>
        <?php if(Auth::user()->role_id == 3): ?>
        <hr class="my-10">
        
        <?php endif; ?>
        <hr class="my-10">
        <?php if(Auth::user()->role_id <= 2 || Auth::user()->email === '<EMAIL>' || Auth::user()->email ===
            '<EMAIL>'): ?> <button type="button"
                class="px-4 py-3 inline-block text-center rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150 relative w-full mb-5 bg-blue-500 _js-btn-sync-stock">
                Update Stock
            </button>
            <?php endif; ?>
            <?php if(Auth::user()->role_id <= 1): ?> <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['class' => 'relative w-full mb-5 bg-blue-400','target' => '_blank','href' => '/accurate-auth']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['class' => 'relative w-full mb-5 bg-blue-400','target' => '_blank','href' => '/accurate-auth']); ?>
                1. Get Accurate API Access
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['class' => 'relative w-full mb-5 bg-blue-500','target' => '_blank','href' => '/accurate-sync-init']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['class' => 'relative w-full mb-5 bg-blue-500','target' => '_blank','href' => '/accurate-sync-init']); ?>
                    2. Sync Accurate Initial DB
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['class' => 'relative w-full mb-5 bg-blue-600','target' => '_blank','href' => '/accurate-sync']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['class' => 'relative w-full mb-5 bg-blue-600','target' => '_blank','href' => '/accurate-sync']); ?>
                    3. Sync Accurate Customer
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                <?php endif; ?>
                <?php if(Auth::user()->role_id <= 3): ?> <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['class' => 'relative w-full bg-green-600','href' => '/cs/list/customer?filter_stores%5B0%5D='.e($store_id).'&page=1&filter_last_order=%3E3']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['class' => 'relative w-full bg-green-600','href' => '/cs/list/customer?filter_stores%5B0%5D='.e($store_id).'&page=1&filter_last_order=%3E3']); ?>
                    List Pelanggan Tidak Aktif
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                    <hr class="my-10">
                    <?php endif; ?>
                    <?php if(Auth::user()->role_id <= 4 && $sdms): ?> <div class="flex flex-col gap-2">
                        <h2 class="text-lg font-bold text-gray-700">SDM 👉 Toko</h2>
                        <?php $__currentLoopData = $sdms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sdm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <form method="POST" action="<?php echo e(route('change-sdm-store')); ?>"
                            class="flex items-center _js-form-change-sdm-store">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="user_id" value="<?php echo e($sdm->id); ?>" />
                            <?php if($sdm->employee->count() > 0): ?>
                            <a href="<?php echo e(Auth::user()->role_id <= 3 ? route('sdm', ['store_slug' => $store_slug, 'employee_id' => $sdm->employee[0]->id]) : '#'); ?>"
                                class="flex flex-col mr-1 text-sm font-semibold text-red-700">
                                <span class="underline"><?php echo e($sdm->name); ?> ↗︎</span>
                                <span class="text-xs text-gray-400"><?php echo e($sdm->role->title); ?></span>
                            </a>
                            <?php else: ?>
                            <a href="<?php echo e(Auth::user()->role_id <= 3 ? url('cs/form/employee?user_id='.$sdm->id) : '#'); ?>"
                                class="flex flex-col mr-1 text-sm font-semibold">
                                <p class="flex flex-col"><?php echo e($sdm->name); ?> <span class="text-yellow-500 underline">➕
                                        Data
                                        SDM</span>
                                </p>
                                <span class="text-xs text-gray-400"><?php echo e($sdm->role->title); ?></span>
                            </a>
                            <?php endif; ?>
                            <span class="ml-auto mr-2 text-sm">👉</span>
                            <select data-current_store_id="<?php echo e($sdm->store_id); ?>" data-sdm_id="<?php echo e($sdm->id); ?>"
                                class="block mt-1 text-sm border-gray-300 rounded-md shadow-sm _js-input-change-store focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                                name="store_id" id="store_id">
                                <?php if(Auth::user()->role_id <= 3): ?> <option value="0">Kosong</option>
                                    <?php endif; ?>
                                    <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($store->id); ?>" <?php echo e((int)$store->id === (int)$sdm->store_id ?
                                        'selected'
                                        :
                                        ''); ?>><?php echo e($store->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </form>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <hr class="my-10">
    <?php endif; ?>
    <?php if(Auth::user()->role_id <= 3 && $armadas): ?> <div class="flex flex-col gap-2">
        <h2 class="text-lg font-bold text-gray-700">Armada 👉 SDM</h2>
        <?php $__currentLoopData = $armadas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $armada): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <form method="POST" action="" class="flex items-center _js-form-change-armada-sdm">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="sdm_id" value="<?php echo e($armada->id); ?>" />
            <a href="<?php echo e(route('armada', ['store_slug' => $store_slug, 'armada_id' => $armada->id])); ?>"
                class="mr-1 text-sm font-semibold text-red-700 underline"><?php echo e($armada->licence_number); ?> ↗︎</a>
            <span class="ml-auto mr-2 text-sm">👉</span>
            <select data-current_store_id="<?php echo e($armada->store_id); ?>" data-armada_id="<?php echo e($armada->id); ?>"
                class="block mt-1 text-sm border-gray-300 rounded-md shadow-sm _js-input-change-employee focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                name="employee_id" id="store_id">
                <option value="0">Cadangan</option>
                <?php $__currentLoopData = $sdms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sdm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($sdm->employee->count() > 0): ?>
                <option value="<?php echo e($sdm->employee[0]->id); ?>" <?php echo e((int)$sdm->
                    employee[0]->id === (int)$armada->employee_id
                    ?
                    'selected' : ''); ?>><?php echo e($sdm->employee[0]->full_name); ?></option>
                <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </form>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <hr class="my-10">
        <?php endif; ?>
        <form method="POST" action="<?php echo e(route('logout')); ?>">
            <?php echo csrf_field(); ?>

            <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['class' => 'relative w-full bg-red-600','href' => route('logout'),'onclick' => 'event.preventDefault();
                                this.closest(\'form\').submit();']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['class' => 'relative w-full bg-red-600','href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('logout')),'onclick' => 'event.preventDefault();
                                this.closest(\'form\').submit();']); ?>
                Log Out
                <svg class="absolute top-0 bottom-0 w-6 h-full ml-auto right-2" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
        </form>
        <p class="w-full mt-3 text-xs italic text-center text-gray-400">Gasplus Manager
            v<?php echo e(env('APP_VERSION', '1.0')); ?>

        </p>
        </div>

         <?php $__env->slot('js', null, []); ?> 
            <script>
                $( document ).ready(function() {
                    $('._js-input-store').change(function() {
                        const url = this.value;
                        window.location.replace(url);
                    });

                    const stores = <?php echo json_encode($stores, 15, 512) ?>;
                    const sdms = <?php echo json_encode($sdms, 15, 512) ?>;
                    $('._js-input-change-store').change(function() {
                        console.log('change store');
                        const selectedStoreId = $(this).val() ? $(this).val() : 0;
                        const currentStoreId = $(this).data('current_store_id');
                        const sdmId = $(this).data('sdm_id');
                        const selectedStore = stores.find((item) => parseInt(item.id) === parseInt(selectedStoreId));
                        const currentStore = stores.find((item) => parseInt(item.id) === parseInt(currentStoreId));
                        const sdm = sdms.find((item) => parseInt(item.id) === parseInt(sdmId));
                        console.log('selectedStore', selectedStore);
                        console.log('currentStore', currentStore);
                        console.log('sdm', sdm);
                        if (confirm(`Pindah 🛵 ${sdm.name}: 🛍 ${currentStore ? currentStore.name : 'Kosong'} ➡️ ke toko 🛍 ${selectedStore ? selectedStore.name : 'Kosong'}?`)) {
                            // setTimeout(() => {
                                $(this).parent('._js-form-change-sdm-store').submit();
                            // }, 250);
                        } else {
                            $(this).val(currentStoreId);
                        }
                    });

                    $('._js-btn-sync-stock').click(function() {
                        Swal.fire({
                            // title: "Updating stock from Accurate",
                            text: "Updating stock from Accurate...",
                            // icon: "question"
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                                axios
                                    .get("/accurate-sync-stock")
                                    .then(function (res) {
                                        console.log("🚀 ~ res:", res)
                                        if (res.data) {
                                            Swal.fire(res.data, "", "success");
                                        } else {
                                            Swal.fire("Gagal sync", "", "error");    
                                        }
                                    })
                                    .catch(function (err) {
                                        console.log("err", err);
                                        Swal.fire("Gagal sync", "", "error");
                                    })
                                    .finally(function() {
                                        Swal.hideLoading();
                                    });
                            }
                        });
                    })
                });
            </script>
         <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/account.blade.php ENDPATH**/ ?>