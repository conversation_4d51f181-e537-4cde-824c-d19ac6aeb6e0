<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Map Pelanggan</title>

    <link rel="shortcut icon" type="image/x-icon" href="docs/images/favicon.ico" />

    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.8.0/dist/leaflet.css"
        integrity="sha512-hoalWLoI8r4UszCkZ5kL8vayOGVae1oxXe/2A4AO6J9+580uKHDO3JdHb7NzwwzK5xr/Fs0W40kiNHxM9vyTtQ=="
        crossorigin="" />
    
    <script src="https://unpkg.com/leaflet@1.8.0/dist/leaflet.js"
        integrity="sha512-BB3hKbKWOc9Ez/TAwyWxNXeoV9c1v6FIeYiBieIWkpLjauysF18NzgR1MBNBXf8/KABdlkX68nAhlwcDFLGPCQ=="
        crossorigin=""></script>

    <style>
        html,
        body {
            height: 100%;
            overflow: hidden;
            width: 100%;
            margin: 0;
            padding: 0;
        }

        .leaflet-container {
            width: 100vw;
            height: 100%;
        }

        img.huechange {
            filter: hue-rotate(130deg) contrast(1.5);
        }

        img.month1 {
            filter: hue-rotate(150deg) contrast(1.5) brightness(1);
        }

        img.month2 {
            filter: hue-rotate(150deg) contrast(1.5) brightness(0.7);
        }

        img.month3 {
            filter: hue-rotate(150deg) contrast(1.5) brightness(0.3);
        }

        img.month6 {
            filter: hue-rotate(150deg) contrast(1.5) brightness(0);
        }

        img.marketing {
            filter: hue-rotate(270deg);
        }
    </style>

    <!-- Matomo -->
    <script>
        var _paq = window._paq = window._paq || [];
    /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
    _paq.push(['trackPageView']);
    _paq.push(['enableLinkTracking']);
    (function() {
      var u="//stat.ordergasplus.online/";
      _paq.push(['setTrackerUrl', u+'matomo.php']);
      _paq.push(['setSiteId', '2']);
      var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
      g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
    })();
    </script>
    <!-- End Matomo Code -->

    <!-- Hotjar Tracking Code for Gasplus CS -->
    <script>
        (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:6373432,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>



</head>

<body>
    <div id="map" style="width: 100vw; height: 100%;"></div>
    <script>
        const addresses = <?php echo json_encode($addresses, 15, 512) ?>;
        const marketings = <?php echo json_encode($marketings, 15, 512) ?>;
        const store = <?php echo json_encode($store, 15, 512) ?>;
        const count = <?php echo json_encode($count, 15, 512) ?>;
        const count_bbro = <?php echo json_encode($count_bbro, 15, 512) ?>;
        const storeLatLng = store.latlng ? store.latlng.split(',') : [-7.4504896,110.1215301];
        const zoom = store.latlng ? 14 : 10;

    let group0month = [];
    let group1month = [];
    let group2month = [];
    let group3month = [];
    let group6month = [];
    let groupbbro = [];

    // const markers = [];
    const markerCustomer = {};
    const iconCustomer = L.icon({
        iconUrl: "<?php echo e(asset('public/images/marker-icon-2x.png')); ?>",
        iconSize:     [10, 16], // size of the icon
        // shadowSize:   [50, 64], // size of the shadow
        // iconAnchor:   [22, 94], // point of the icon which will correspond to marker's location
        // shadowAnchor: [4, 62],  // the same for the shadow
        // popupAnchor:  [-3, -76] // point from which the popup should open relative to the iconAnchor
    });
    addresses.forEach(address => {
        const passLatlng = /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/.test(address.latlng);
        if (passLatlng) {
            const latlng = address.latlng ? address.latlng.split(',') : null;
            // if (latlng) {
                if (address && address.customer) {
                    markerCustomer[address.id] = L.marker(latlng, {icon: iconCustomer})
                    // .addTo(map)
                    .bindPopup(`
                        <div style="margin-bottom: 2px;"><strong><a href="/cs/show/customer/${address.customer.id}" target="_blank">${address.customer.name}</a></strong></div>
                        <div style="margin-bottom: 0;">${address.address}</div>
                    `);
                    // console.log('Number(address.mm)', Number(address.mm));
                    if (Number(address.mm) == 6) {
                        // markerCustomer._icon.classList.add("month6");
                        group6month.push(markerCustomer[address.id]);
                    } else if (Number(address.mm) == 3) {
                        // markerCustomer[address.id]._icon.classList.add("month3");
                        group3month.push(markerCustomer[address.id]);
                    } else if (Number(address.mm) == 2) {
                        // markerCustomer[address.id]._icon.classList.add("month2");
                        group2month.push(markerCustomer[address.id]);
                    } else if (Number(address.mm) == 1) {
                        // markerCustomer[address.id]._icon.classList.add("month1");
                        group1month.push(markerCustomer[address.id]);
                    } else {
                        group0month.push(markerCustomer[address.id]);
                    }
                    // markerCustomer._icon.classList.add("month1");
                }
                // markers.push(latlng);
            // }
        }
    });

    const markerBbro = {};
    const iconBbro = L.icon({
        iconUrl: "<?php echo e(asset('public/images/marker-icon-2x.png')); ?>",
        iconSize:     [10, 16], // size of the icon
        // shadowSize:   [50, 64], // size of the shadow
        // iconAnchor:   [22, 94], // point of the icon which will correspond to marker's location
        // shadowAnchor: [4, 62],  // the same for the shadow
        // popupAnchor:  [-3, -76] // point from which the popup should open relative to the iconAnchor
    });
    marketings.forEach(marketing => {
        const passLatlng = /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/.test(marketing.received_latlng);
        if (passLatlng) {
            const latlng = marketing.received_latlng ? marketing.received_latlng.split(',') : null;
            if (latlng[1]) {
                // if (marketing && marketing.customer) {
                    markerBbro[marketing.id] = L.marker(latlng, {icon: iconBbro})
                    // .addTo(map)
                    .bindPopup(`
                        <div style="margin-bottom: 2px;"><strong>🛵 ${marketing.driver.name}</strong></div>
                        <div style="margin-bottom: 8px;">🕑 ${marketing.received_at}</div>
                    `);
                    // console.log('Number(address.mm)', Number(address.mm));
                    groupbbro.push(markerBbro[marketing.id]);
                    // markerCustomer._icon.classList.add("month1");
                }
                // markers.push(latlng);
            // }
        }
    });

    const layer0month = L.layerGroup(group0month);
    const layer1month = L.layerGroup(group1month);
    const layer2month = L.layerGroup(group2month);
    const layer3month = L.layerGroup(group3month);
    const layer6month = L.layerGroup(group6month);
    const layerbbro = L.layerGroup(groupbbro);
// console.log('group0month.length', group0month.length);
    const lastOrderMaps = {};
    lastOrderMaps[`< 1 bulan tidak order (${count.month0})`] = layer0month;
    lastOrderMaps[`1-2 bulan tidak order (${count.month1})`] = layer1month;
    lastOrderMaps[`2-3 bulan tidak order (${count.month2})`] = layer2month;
    lastOrderMaps[`3-6 bulan tidak order (${count.month3})`] = layer3month;
    lastOrderMaps[`> 6 bulan tidak order(${count.month6})`] = layer6month;
    lastOrderMaps[`B-Bro 1 bulan (${count_bbro.month0})`] = layerbbro;

    const map = L.map('map', {
        center: storeLatLng,
        zoom: zoom,
        layers: [
            layer0month, 
            layer1month, 
            layer2month, 
            layer3month, 
            layer6month, 
            layerbbro, 
        ]
    });
        // .fitWorld();
        // .setView(storeLatLng, zoom);
        // .setView([-7.4504896,110.1215301], 10);

        // console.log('addresses', addresses);

	const tiles = L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
		maxZoom: 19,
		attribution: store.name
	}).addTo(map);

    const layerControl = L.control.layers({}, lastOrderMaps).addTo(map);



    const iconStore = L.icon({
        iconUrl: "<?php echo e(asset('public/images/pin.png')); ?>",

        iconSize:     [50, 50], // size of the icon
        // shadowSize:   [50, 64], // size of the shadow
        // iconAnchor:   [22, 94], // point of the icon which will correspond to marker's location
        // shadowAnchor: [4, 62],  // the same for the shadow
        // popupAnchor:  [-3, -76] // point from which the popup should open relative to the iconAnchor
    });

    const markerStore = L.marker(storeLatLng
    , {icon: iconStore}
    )
                    .addTo(map)
                    .bindPopup(`
                        <div style="margin-bottom: 2px;"><strong>${store.name}</strong></div>
                        <div style="margin-bottom: 0;">${store.address}</div>
                    `);
                    // markerStore._icon.classList.add("huechange");

    addresses.forEach(address => {
        const passLatlng = /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/.test(address.latlng);
        if (passLatlng) {
                if (address && address.customer) {
                    if (Number(address.mm) == 6) {
                        markerCustomer[address.id]._icon.classList.add("month6");
                    } else if (Number(address.mm) == 3) {
                        markerCustomer[address.id]._icon.classList.add("month3");
                    } else if (Number(address.mm) == 2) {
                        markerCustomer[address.id]._icon.classList.add("month2");
                    } else if (Number(address.mm) == 1) {
                        markerCustomer[address.id]._icon.classList.add("month1");
                    } 
                }
        }
    });

    marketings.forEach(marketing => {
        const passLatlng = /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/.test(marketing.received_latlng);
        if (passLatlng) {
            const latlng = marketing.received_latlng ? marketing.received_latlng.split(',') : null;
            if (latlng[1]) {
                markerBbro[marketing.id]._icon.classList.add("marketing");
            }
        }
    });

    map.on("overlayadd", function(e){
        addresses.forEach(address => {
        const passLatlng = /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/.test(address.latlng);
        if (passLatlng) {
                if (address && address.customer) {
                    if (Number(address.mm) == 6) {
                        if (markerCustomer[address.id]._icon) {
                        markerCustomer[address.id]._icon.classList.add("month6");
                        }
                    } else if (Number(address.mm) == 3) {
                        if (markerCustomer[address.id]._icon) {
                        markerCustomer[address.id]._icon.classList.add("month3");
                        }
                    } else if (Number(address.mm) == 2) {
                        if (markerCustomer[address.id]._icon) {
                        markerCustomer[address.id]._icon.classList.add("month2");
                        }
                    } else if (Number(address.mm) == 1) {
                        if (markerCustomer[address.id]._icon) {
                        markerCustomer[address.id]._icon.classList.add("month1");
                        }
                    } 
                }
        }
        marketings.forEach(marketing => {
            const passLatlng = /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/.test(marketing.received_latlng);
            if (passLatlng) {
                const latlng = marketing.received_latlng ? marketing.received_latlng.split(',') : null;
                if (latlng[1]) {
                    markerBbro[marketing.id]._icon.classList.add("marketing");
                }
            }
        });
    });
        // console.log('e', e);
        // const title = e.name;
        // if (title.includes('< 1 bulan tidak order')) {

        // } else if (title.includes('> 1 bulan')) {

        // } else if (title.includes('> 2 bulan')) {

        // } else if (title.includes('> 3 bulan')) {

        // } else if (title.includes('> 6 bulan')) {
        // }
    });

    // const group = new L.featureGroup(markers);

    // map.fitBounds(group.getBounds());


    
		// .bindPopup('<b>Hello world!</b><br />I am a popup.').openPopup();

	// const circle = L.circle([51.508, -0.11], {
	// 	color: 'red',
	// 	fillColor: '#f03',
	// 	fillOpacity: 0.5,
	// 	radius: 500
	// }).addTo(map).bindPopup('I am a circle.');

	// const polygon = L.polygon([
	// 	[51.509, -0.08],
	// 	[51.503, -0.06],
	// 	[51.51, -0.047]
	// ]).addTo(map).bindPopup('I am a polygon.');


	// const popup = L.popup()
	// 	.setLatLng([51.513, -0.09])
	// 	.setContent('I am a standalone popup.')
	// 	.openOn(map);

	// function onMapClick(e) {
	// 	popup
	// 		.setLatLng(e.latlng)
	// 		.setContent('You clicked the map at ' + e.latlng.toString())
	// 		.openOn(map);
	// }

	// map.on('click', onMapClick);

    </script>



</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/sharp/customer-map.blade.php ENDPATH**/ ?>