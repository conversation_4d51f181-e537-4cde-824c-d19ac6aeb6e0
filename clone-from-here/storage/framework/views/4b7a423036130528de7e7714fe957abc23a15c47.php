<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3" href="<?php echo e(URL::previous()); ?>"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Data SDM
            </h2>
        </a>
        <a href="/cs/form/employee/<?php echo e($sdm->id); ?>" target="_blank" class="ml-auto font-bold">✏️ EDIT ↗︎</a>
     <?php $__env->endSlot(); ?>

    <div class="px-3 pt-16 pb-20 prose">
        <table class="w-full table-auto">
            <tbody>
                <tr>
                    <th class="text-left">Nama</th>
                    <td class="text-left"><?php echo e($sdm->full_name); ?></td>
                </tr>
                <tr>
                    <th class="text-left">Akun</th>
                    <td class="flex flex-col text-left"><?php echo e($sdm->user->name); ?>

                        <span class="text-xs text-gray-500"><?php echo e($sdm->user->email); ?></span>
                    </td>
                </tr>
                <?php if($sdm->dob): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Tgl. Lahir</th>
                    <td class="text-left"><?php echo e($sdm->dob); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->address): ?>
                <tr>
                    <th class="text-left">Alamat</th>
                    <td class="text-left"><?php echo e($sdm->address); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->profilephoto_url): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Foto Selfie</th>
                    <td class="text-left"><img data-zoomable src="<?php echo e($sdm->profilephoto_url); ?>" /></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->ktp_number): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">KTP Number</th>
                    <td class="text-left"><?php echo e($sdm->ktp_number); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->ktpphoto_url): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Foto KTP</th>
                    <td class="text-left"><img data-zoomable src="<?php echo e($sdm->ktpphoto_url); ?>" /></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->kk_number): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">KK Number</th>
                    <td class="text-left"><?php echo e($sdm->kk_number); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->kkphoto_url): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Foto KK</th>
                    <td class="text-left"><img data-zoomable src="<?php echo e($sdm->kkphoto_url); ?>" /></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->sim_number): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">SIM Number</th>
                    <td class="flex flex-col text-left"><?php echo e($sdm->sim_number); ?>

                        <?php if($sdm->sim_valid_until): ?>
                        <span class="text-xs text-gray-500">Valid until: <?php echo e($sdm->sim_valid_until); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->simphoto_url): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Foto SIM</th>
                    <td class="text-left"><img data-zoomable src="<?php echo e($sdm->simphoto_url); ?>" /></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->start_work_at): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Mulai Kerja</th>
                    <td class="text-left"><?php echo e($sdm->start_work_at); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->quit_work_at): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Berhenti Kerja</th>
                    <td class="text-left"><?php echo e($sdm->quit_work_at); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->quit_note): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Alasan Berhenti</th>
                    <td class="text-left"><?php echo e($sdm->quit_note); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->note): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Catatan Lain</th>
                    <td class="text-left"><?php echo e($sdm->note); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($sdm->armada): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Armada</th>
                    <td class="flex flex-col text-left"><?php echo e($sdm->armada->licence_number); ?>

                        <span class="text-xs text-gray-500">
                            <?php echo e($sdm->armada->brand ? $sdm->armada->brand->name : ''); ?>

                            <?php echo e($sdm->armada->model ? $sdm->armada->model->name : ''); ?>

                            <?php echo e($sdm->armada->year ? $sdm->armada->year : ''); ?>

                        </span>
                    </td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
     <?php $__env->slot('js', null, []); ?> 
        <script>
            $(document).ready(function() {
                mediumZoom('[data-zoomable]');
            });
        </script>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/sdm.blade.php ENDPATH**/ ?>