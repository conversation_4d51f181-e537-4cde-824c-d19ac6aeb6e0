<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>Accurate Sync - Step 2</title>

  <!-- Fonts -->
  

  <!-- Styles -->
  <link rel="stylesheet" href="<?php echo e(mix('css/app.css')); ?>">

  <!-- Matomo -->
  <script>
    var _paq = window._paq = window._paq || [];
  /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
  _paq.push(['trackPageView']);
  _paq.push(['enableLinkTracking']);
  (function() {
    var u="//stat.ordergasplus.online/";
    _paq.push(['setTrackerUrl', u+'matomo.php']);
    _paq.push(['setSiteId', '2']);
    var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
    g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
  })();
  </script>
  <!-- End Matomo Code -->


  <!-- Hotjar Tracking Code for Gasplus CS -->
  <script>
    (function(h,o,t,j,a,r){
      h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
      h._hjSettings={hjid:6373432,hjsv:6};
      a=o.getElementsByTagName('head')[0];
      r=o.createElement('script');r.async=1;
      r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
      a.appendChild(r);
  })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
  </script>

</head>

<body class="antialiased">
  <div class="px-5 py-4 flex flex-col gap-5 h-screen">
    <h1 class="text-2xl font-bold">Sync <span class="underline"><?php echo e($store_name); ?></span> (Step 2 - Select Data to Sync)
    </h1>
    <p class="text-yellow-600 text-xl font-bold">TOTAL: <?php echo e(count($data_to_sync)); ?> data to Sync</p>
    <form action="<?php echo e(route('accurate-sync-process-2')); ?>" method="POST"
      class="flex flex-1 overflow-auto border-b border-gray-200">
      <?php echo csrf_field(); ?>
      <input type="hidden" name="store_id" value="<?php echo e($store_id); ?>" />
      <table class="w-full border text-xs">
        <thead>
          <tr class="divide-x">
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Sync</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Code / ID</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Nama</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Kemiripan</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Data Accurate</th>
            <th class="sticky top-0 px-1.5 py-2  bg-green-300 text-green-900">Data GASPLUS</th>
          </tr>
        </thead>
        <?php
        $data_count = 0;
        ?>
        <tbody class="divide-y divide-x">
          <?php $__currentLoopData = $data_to_sync; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          
          <?php
          $data_count++;
          ?>
          
          <tr class="divide-x bg-blue-50 text-blue-900 hover:bg-blue-100">
            <td class="px-1.5 py-2 text-center">
              <input type="checkbox" name="is_sync_data[]"
                value="<?php echo e($data['accurate_customer_id'].';'.$data['accurate_customer_code'].';'.$data['address_id'].';'.$data['customer_id']); ?>"
                <?php echo e((int)$data['similarity_percentage']>= 80 ? 'checked'
              : ''); ?>>
            </td>
            <td class="px-1.5 py-2 text-left"><?php echo e($data['accurate_customer_code']); ?> / <?php echo e($data['accurate_customer_id']); ?>

            </td>
            <td class="px-1.5 py-2 text-left whitespace-nowrap"><?php echo e($data['name']); ?>

              <?php if(!empty($data['customer_id'])): ?>
              <br />
              <a class="text-blue-500 underline font-semibold" href="/cs/show/customer/<?php echo e($data['customer_id']); ?>"
                target="_blank">Detail Pelanggan ↗️</a>
              <?php endif; ?>
            </td>
            <td class="px-1.5 py-2 text-center"><?php echo e((int)$data['similarity_percentage']); ?>%</td>
            <td class="px-1.5 py-2 text-left align-top">
              <span
                class="<?php echo e(strtolower($data['name']) !== strtolower($data['customer_name']) ? 'text-red-500 font-bold' : ''); ?>"><strong>Nama:</strong>
                <?php echo e($data['name']); ?></span>
              <br />
              <span class="<?php echo e(empty($data['phone01']) && empty($data['phone02']) ? 'text-red-500 font-bold' : ''); ?>">
                <strong>No. WA:</strong> <?php echo e(join(' / ', [$data['phone01'],$data['phone02']])); ?>

              </span>
              <br />
              <strong>Alamat:</strong> <?php echo e($data['address']); ?>

              <br />
              <span
                class="<?php echo e(strtolower($data['store']) !== strtolower($data['customer_store']) ? 'text-red-500 font-bold' : ''); ?>">
                <strong>Toko:</strong> <?php echo e($data['store']); ?>

              </span>
            </td>
            <td class="px-1.5 py-2 text-left align-top bg-green-50">
              <strong>Nama:</strong> <?php echo e($data['customer_name']); ?>

              <br />
              <strong>No. WA:</strong> <?php echo e($data['customer_phone']); ?>

              <br />
              <strong>Alamat:</strong> <?php echo e($data['customer_address']); ?>

              <br />
              <strong>Toko:</strong> <?php echo e($data['customer_store']); ?>

              <br />
              <strong>Last Order:</strong> <?php echo e($data['customer_last_order'] ?? ''); ?>

            </td>
          </tr>

          <?php if(!empty($data['customer_id_alt']) && (int)$data['similarity_percentage']< 80): ?> <tr
            class="divide-x opacity-80 hover:bg-gray-100">
            <td class="px-1.5 py-2 text-center">
              <input type="checkbox" name="is_sync_data[]"
                value="<?php echo e($data['accurate_customer_id'].';'.$data['accurate_customer_code'].';'.$data['address_id_alt'].';'.$data['customer_id_alt']); ?>"
                <?php echo e((int)$data['similarity_percentage_alt']>= 80 ? 'checked'
              : ''); ?>>
            </td>
            <td class="px-1.5 py-2 text-left"><?php echo e($data['accurate_customer_code']); ?> / <?php echo e($data['accurate_customer_id']); ?>

            </td>
            <td class="px-1.5 py-2 text-left whitespace-nowrap"><?php echo e($data['name']); ?>

              <?php if(!empty($data['customer_id_alt'])): ?>
              <br />
              <a class="text-blue-500 underline font-semibold" href="/cs/show/customer/<?php echo e($data['customer_id_alt']); ?>"
                target="_blank">Detail Pelanggan ↗️</a>
              <?php endif; ?>
            </td>
            <td class="px-1.5 py-2 text-center"><?php echo e((int)$data['similarity_percentage_alt']); ?>%</td>
            <td class="px-1.5 py-2 text-left align-top">
              <span
                class="<?php echo e(strtolower($data['name']) !== strtolower($data['customer_name']) ? 'text-red-500 font-bold' : ''); ?>"><strong>Nama:</strong>
                <?php echo e($data['name']); ?></span>
              <br />
              <span class="<?php echo e(empty($data['phone01']) && empty($data['phone02']) ? 'text-red-500 font-bold' : ''); ?>">
                <strong>No. WA:</strong> <?php echo e(join(' / ', [$data['phone01'],$data['phone02']])); ?>

              </span>
              <br />
              <strong>Alamat:</strong> <?php echo e($data['address']); ?>

              <br />
              <span
                class="<?php echo e(strtolower($data['store']) !== strtolower($data['customer_store']) ? 'text-red-500 font-bold' : ''); ?>">
                <strong>Toko:</strong> <?php echo e($data['store']); ?>

              </span>
            </td>
            <td class="px-1.5 py-2 text-left align-top bg-green-50">
              <strong>Nama:</strong> <?php echo e($data['customer_name_alt']); ?>

              <br />
              <strong>No. WA:</strong> <?php echo e($data['customer_phone_alt']); ?>

              <br />
              <strong>Alamat:</strong> <?php echo e($data['customer_address_alt']); ?>

              <br />
              <strong>Toko:</strong> <?php echo e($data['customer_store_alt']); ?>

              <br />
              <strong>Last Order:</strong> <?php echo e($data['customer_last_order_alt']); ?>

            </td>
            </tr>
            <?php endif; ?>


            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
      </table>
      <div class="fixed bottom-0 left-0 right-0 p-5 shadow-2xl z-50 bg-white border-t border-gray-200">
        <button type="submit" <?php echo e($data_count===0 ? 'disabled' : ''); ?>

          class="rounded-md w-full <?php echo e($data_count===0 ? 'opacity-50' : ''); ?> bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">Confirm
          Data
          & Sync</button>
      </div>
    </form>
    <div class="h-20">&nbsp;</div>
  </div>
</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/accurate-sync-step-2.blade.php ENDPATH**/ ?>