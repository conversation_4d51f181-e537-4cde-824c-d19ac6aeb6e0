<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
   <?php $__env->slot('store_slug', null, []); ?> 
    <?php echo e($store_slug); ?>

   <?php $__env->endSlot(); ?>

   <?php $__env->slot('header', null, []); ?> 
    <div class="absolute inset-0 flex items-center px-4 bg-yellow-500">
      <h3 class="flex items-center text-base font-bold">
        <a href="<?php echo e(route('report.driver.list', ['store_slug' => $store_slug])); ?>" class="flex items-center -ml-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          In-Job
        </a>
      </h3>
    </div>
   <?php $__env->endSlot(); ?>

   <?php $__env->slot('subheader', null, []); ?> 
    <div class="flex items-center justify-center h-10 mt-1 text-yellow-500">
      <a class="w-6 h-6 _js-btn-month"
        href="<?php echo e(route('report.driver', ['ids'=> $ids, 'store_slug' => $store_slug, 'month' => $month_prev])); ?>"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
            clip-rule="evenodd" />
        </svg></a>
      <input type="month" class="w-56 p-0 mx-3 text-lg font-bold text-center border-none focus:ring-0 _js-input-month"
        max="<?php echo e(date('Y-m')); ?>" value="<?php echo e($month_now); ?>">
      <?php if($month_next): ?>
      <a class="w-6 h-6 _js-btn-month"
        href="<?php echo e(route('report.driver', ['ids'=> $ids, 'store_slug' => $store_slug, 'month' => $month_next])); ?>"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
            clip-rule="evenodd" />
        </svg>
      </a>
      <?php endif; ?>
    </div>
    <div
      class="pt-2.5 pb-2.5 mt-0 border-t border-b border-gray-300 flex justify-center items-center text-gray-400 text-base">
      <input type="date" class="w-32 p-0 mx-3 font-bold text-center border-none focus:ring-0 _js-input-date-start"
        min="<?php echo e($date_min); ?>" max="<?php echo e($date_max); ?>" value="<?php echo e($date_start); ?>">
      <span class="text-gray-300">to</span>
      <input type="date" class="w-32 p-0 mx-3 font-bold text-center border-none focus:ring-0 _js-input-date-end"
        min="<?php echo e($date_min); ?>" max="<?php echo e($date_max); ?>" value="<?php echo e($date_end); ?>">
    </div>
    <div class="flex items-center text-xs font-bold text-center">
      <span class="w-16 py-2 pl-1 text-left text-blue-500 capitalize">Delman</span>
      <span class="w-10 py-2 text-blue-600">GPS</span>
      <span class="w-11 py-2 text-blue-600">JOB</span>
      <span class="w-10 py-2 text-red-600">>1jam</span>
      <div
        class="flex items-center flex-1 overflow-x-auto border-l-2 border-gray-200 scrollbar-hide bg-yellow-50 _js-scroll">
        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <span class="flex-shrink-0 w-14 px-1 py-2 overflow-hidden text-gray-400 overflow-ellipsis whitespace-nowrap"><?php echo e($category->name); ?></span>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
    <div class="flex items-center font-bold text-xs text-center -mt-1.5">
      <div class="w-16 py-2 pl-1 text-left text-blue-500 capitalize">TOTAL</div>
      
      <div class="flex flex-col w-10 py-2 text-blue-600"></div>
      <div class="flex flex-col w-11 py-2 text-blue-600">
        <?php echo e(number_format($sum_all_delivered, 0, '', '.')); ?>

        <span class="text-blue-700"><?php echo e(number_format($sum_all_insentive, 0, '', '.').'k'); ?></span>
      </div>
      
      <div class="flex flex-col w-10 py-2 text-red-600">
        <?php echo e(number_format($sum_all_delivered_overtime, 0, '', '.')); ?>

        <span class="text-transparent"></span>
      </div>
      <div
        class="flex items-center flex-1 overflow-x-auto border-l-2 border-gray-200 scrollbar-hide bg-yellow-50 _js-scroll">
        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(array_key_exists($category->id, $sum_category_qty)): ?>
        <div class="flex flex-col flex-shrink-0 w-14 px-1 py-2 text-gray-400">
          <?php echo e('@'.number_format($sum_category_qty[$category->id], 0, '', '.')); ?>

          <span class="text-gray-500"><?php echo e(number_format($sum_category_insentive[$category->id], 0, '', '.').'k'); ?></span>
        </div>
        <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
   <?php $__env->endSlot(); ?>

  <div class="pt-56 pb-20">
    <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver_group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if($driver_group['sum_store_delivered'] > 0): ?>
    <div
      class="flex items-center gap-2 px-1 py-1 text-xs font-bold text-blue-800 bg-blue-100 border-b border-gray-200 border-solid">
      <span class="text-sm"><?php echo e($driver_group['store']['name']); ?></span>
      <span class="text-blue-500">
        <?php echo e(number_format($driver_group['sum_store_delivered'], 0, '', '.')); ?>

      </span>
      <span class="text-blue-700">
        <?php echo e(number_format($driver_group['sum_store_insentive'], 0, '', '.').'k'); ?>

      </span>
    </div>
    <?php $__currentLoopData = $driver_group['driver_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <a href="<?php echo e(url('cs/list/order?'.
      'filter_date='.date('Ymd', strtotime($date_start)).'..'.date('Ymd', strtotime($date_end)).'&'.
      'filter_status%5B0%5D=4&filter_status%5B1%5D=6&filter_status%5B2%5D=7&'.
      'filter_store%5B0%5D='.$driver_group['store']['id'].'&'.
      'filter_drivers%5B0%5D='.$driver['id']
    )); ?>" target="_blank"
      class="flex items-center text-xs font-bold text-center border-b border-gray-200 border-solid">
      <div class="flex items-center justify-start w-16 pl-1 leading-none text-left text-blue-500 break-normal h-20">
        <?php echo e($driver['name']); ?>

      </div>
      <div class="flex flex-col items-center justify-center w-10 py-2 text-green-600 bg-white bg-opacity-50 h-20">
        <?php echo e(number_format($driver['count_delivered'] - $driver['count_delivered_gpsoff'], 0, '', '.')); ?>

        <span class="text-green-700 mb-1"><?php echo e(number_format($driver['sum_insentive'] - $driver['sum_insentive_gpsoff'],
          0,
          '', '.').'k'); ?></span>
        <span class="text-red-700" style="font-size: 7px;">GPSOFF</span>
      </div>
      <div class="flex flex-col items-center justify-center w-11 py-2 text-blue-600 bg-white bg-opacity-50 h-20">
        <?php echo e(number_format($driver['count_delivered'], 0, '', '.')); ?>

        <span class="text-blue-700 mb-1"><?php echo e(number_format($driver['sum_insentive'], 0, '', '.').'k'); ?></span>
        <span class="text-red-700"><?php echo e(number_format($driver['count_delivered_gpsoff'], 0, '',
          '.')); ?></span>
        <span class="text-red-900"><?php echo e(number_format($driver['sum_insentive_gpsoff'], 0, '',
          '.').'k'); ?></span>
      </div>
      <div class="flex flex-col items-center justify-center w-10 py-2 text-red-600 h-20">
        
          <?php echo e(number_format($driver['count_delivered_overtime'], 0, '', '.')); ?>

          <span class="text-transparent mb-1"></span>
          <span class="text-red-900"><?php echo e(number_format($driver['count_delivered_overtime_gpsoff'], 0,
            '', '.')); ?></span>
          <span class="text-transparent"></span>
          
      </div>
      <div class="flex items-center flex-1 overflow-x-auto border-l-2 border-gray-200 scrollbar-hide _js-scroll">
        <?php $__currentLoopData = $driver['categories']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="flex flex-col items-center justify-center flex-shrink-0 w-14 px-1 py-2 text-gray-400 h-20">
          <?php echo e('@'.number_format($category['qty'], 0, '', '.')); ?>

          <span class="text-gray-500 mb-1"><?php echo e(number_format($category['insentive'], 0, '', '.').'k'); ?></span>
          <span class="text-red-700"><?php echo e('@'.number_format($category['qty_gpsoff'], 0, '', '.')); ?></span>
          <span class="text-red-900"><?php echo e(number_format($category['insentive_gpsoff'], 0, '',
            '.').'k'); ?></span>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </a>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  </div>

  <div id="loading"
    class="fixed inset-0 z-50 flex items-center justify-center text-4xl font-bold text-gray-500 bg-white bg-opacity-70"
    style="display: none;">
    Loading...
  </div>



   <?php $__env->slot('js', null, []); ?> 
    <script>
      $(document).ready(function() {
        // Change Date
        // --------------------------------------------------
        $('._js-btn-month').click(function() {
          $('#loading').show();
        });
        $('._js-input-month').change(function() {
          const url = "<?php echo e(url()->current()); ?>";
          const month = this.value;
          console.log(url + '?month=' + month);
          $('#loading').show();
          window.location.replace(url + '?month=' + month);
        });
        $('._js-input-date-start').change(function() {
          // const url = new URL('<?php echo e(url()->full()); ?>');
          const url = new URL(window.location.href);
          const datestart = this.value;
          url.searchParams.set('datestart', datestart);
          console.log("🚀 ~ url:", url)
          $('#loading').show();
          window.location.replace(url.href);
        });
        $('._js-input-date-end').change(function() {
          // const url = new URL('<?php echo e(url()->full()); ?>');
          const url = new URL(window.location.href);
          const dateend = this.value;
          url.searchParams.set('dateend', dateend);
          console.log("🚀 ~ url:", url)
          $('#loading').show();
          window.location.replace(url.href);
        });

        // Bind Multiple Scroll
        // --------------------------------------------------
        // $('._js-scroll').scrollsync()
        $('._js-scroll').scroll(function(e) {
          $('._js-scroll').scrollLeft(e.target.scrollLeft);
        });
        // var scrollers = document.getElementsByClassName('_js-scroll');
        // var scrollerDivs = Array.prototype.filter.call(scrollers, function(testElement) {
        //   return testElement.nodeName === 'DIV';
        // });
        // function scrollAll(scrollLeft) {
        //   scrollerDivs.forEach(function(element, index, array) {
        //     element.scrollLeft = scrollLeft;
        //   });
        // }
        // scrollerDivs.forEach(function(element, index, array) {
        //   element.addEventListener('scroll', function(e) {
        //     scrollAll(e.target.scrollLeft);
        //   });
        // });
      });

    </script>
   <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/report-driver-insentive.blade.php ENDPATH**/ ?>