<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>Accurate Sync - Step 3</title>

  <!-- Fonts -->
  

  <!-- Styles -->
  <link rel="stylesheet" href="<?php echo e(mix('css/app.css')); ?>">

  <!-- Matomo -->
  <script>
    var _paq = window._paq = window._paq || [];
  /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
  _paq.push(['trackPageView']);
  _paq.push(['enableLinkTracking']);
  (function() {
    var u="//stat.ordergasplus.online/";
    _paq.push(['setTrackerUrl', u+'matomo.php']);
    _paq.push(['setSiteId', '2']);
    var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
    g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
  })();
  </script>
  <!-- End Matomo Code -->

  <!-- Hotjar Tracking Code for Gasplus CS -->
  <script>
    (function(h,o,t,j,a,r){
      h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
      h._hjSettings={hjid:6373432,hjsv:6};
      a=o.getElementsByTagName('head')[0];
      r=o.createElement('script');r.async=1;
      r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
      a.appendChild(r);
  })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
  </script>

</head>

<body class="antialiased">
  <div class="px-5 py-4 flex flex-col gap-5 h-screen">
    <h1 class="text-2xl font-bold">Sync <span class="underline"><?php echo e($store_name); ?></span> (Step 3 - Finish)</h1>
    <p class="text-green-600 text-xl font-bold">TOTAL: <?php echo e(count($addresses_updated)); ?> data SUCCESS Synced</p>
    <div class="flex flex-1 overflow-auto border-b border-gray-200">
      <table class="w-full border text-xs">
        <thead>
          <tr class="divide-x">
            <th class="sticky top-0 px-1.5 py-2 text-black bg-green-300">ID</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-green-300">Code</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-green-300">Nama</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-green-300">Data GASPLUS</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-x">
          <?php $__currentLoopData = $addresses_updated; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> <tr class="divide-x">
            <input type="hidden" name="address_id[]" value="<?php echo e($address->id); ?>">
            <td class="px-1.5 py-2 text-left"><?php echo e($address->accurate_customer_id); ?></td>
            <td class="px-1.5 py-2 text-left"><?php echo e($address->accurate_customer_code); ?></td>
            <td class="px-1.5 py-2 text-left whitespace-nowrap"><?php echo e($address->customer->name); ?></td>
            <td class="px-1.5 py-2 text-left align-top">
              <strong>No. WA:</strong> <?php echo e($address->customer->phone); ?>

              <br />
              <strong>Alamat ID (Accurate):</strong> <?php echo e($address->accurate_customer_id); ?>

              <br />
              <strong>Alamat:</strong> <?php echo e($address->address); ?>

              <br />
              <strong>Toko:</strong> <?php echo e($address->store->name); ?>

            </td>
          </tr>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
      </table>
      <div
        class="fixed flex flex-col gap-3 bottom-0 left-0 right-0 p-5 shadow-2xl z-50 bg-white border-t border-gray-200">
        <a href="<?php echo e(route('accurate-sync-index')); ?>"
          class="flex justify-center items-center rounded-md w-full bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">Sync
          Again with
          Other Data</a>
        <a href="<?php echo e(route('accurate-sync-process-3', ['store_id' => $store_id])); ?>"
          class="flex justify-center items-center rounded-md w-full border-2 text-red-700 border-red-600 px-3 py-2 text-sm font-semibold shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">Create
          New Customer (<?php echo e($store_name); ?>) on Accurate</a>
      </div>
    </div>
    <div class="h-20">&nbsp;</div>
  </div>
</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/accurate-sync-step-3.blade.php ENDPATH**/ ?>