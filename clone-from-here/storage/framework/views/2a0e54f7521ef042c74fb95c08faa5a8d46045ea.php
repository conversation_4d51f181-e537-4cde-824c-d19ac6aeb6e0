<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3" href="<?php echo e(URL::previous()); ?>"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Data Armada
            </h2>
        </a>
        <a href="/cs/form/armada/<?php echo e($armada->id); ?>" target="_blank" class="ml-auto font-bold">✏️ EDIT ↗︎</a>
     <?php $__env->endSlot(); ?>

    <div class="px-3 pt-16 pb-20 prose">
        <table class="w-full table-auto">
            <tbody>
                <tr>
                    <th class="text-left">No.Polisi</th>
                    <td class="text-left"><?php echo e($armada->licence_number); ?>

                        <?php if($armada->stnk_valid_until): ?>
                        <span class="text-xs text-gray-500">Valid until: <?php echo e($armada->stnk_valid_until); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php if($armada->stnkphoto_url): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Foto STNK</th>
                    <td class="text-left"><img data-zoomable class="!my-0" src="<?php echo e($armada->stnkphoto_url); ?>" /></td>
                </tr>
                <?php endif; ?>
                <?php if($armada->chassis_number): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">No.Rangka</th>
                    <td class="text-left"><?php echo e($armada->chassis_number); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($armada->machine_number): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">No.Mesin</th>
                    <td class="text-left"><?php echo e($armada->machine_number); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($armada->brand): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Merk</th>
                    <td class="text-left"><?php echo e($armada->brand->name); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($armada->model): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Model</th>
                    <td class="text-left"><?php echo e($armada->model->name); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($armada->year): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Tahun</th>
                    <td class="text-left"><?php echo e($armada->year); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($armada->frontphotos->count() > 0): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Tampak Depan</th>
                    <td class="flex flex-col gap-1 text-left">
                        <?php $__currentLoopData = $armada->frontphotos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <img data-zoomable class="!my-0" src="<?php echo e($photo->url); ?>" />
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </td>
                </tr>
                <?php endif; ?>

                <?php if($armada->note): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">Catatan</th>
                    <td class="text-left"><?php echo e($armada->note); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($armada->employee): ?>
                <tr>
                    <th class="text-left whitespace-nowrap">SDM</th>
                    <td class="flex flex-col text-left"><?php echo e($armada->employee->full_name); ?>

                        
                    </td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
     <?php $__env->slot('js', null, []); ?> 
        <script>
            $(document).ready(function() {
                mediumZoom('[data-zoomable]');
            });
        </script>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/armada.blade.php ENDPATH**/ ?>