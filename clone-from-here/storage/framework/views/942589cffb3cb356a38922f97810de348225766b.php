<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3 whitespace-nowrap"
            href="<?php echo e(route('jobs', ['store_slug' => $store_slug])); ?>"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Add Pengeluaran Toko
            </h2>
        </a>
        <span class="ml-auto text-sm line-clamp-2 pl-2.5"><?php echo e($store->name); ?></span>
        
     <?php $__env->endSlot(); ?>

    <div class="px-5 pt-20 pb-20">
        <?php if((int)$totalkonsumsitoko > 0): ?>
        <div class="bg-green-400 rounded-lg py-2 px-3 mb-4 flex items-center">
            TOTAL Konsumsi Toko: <strong class="ml-auto">Rp<?php echo e(number_format($totalkonsumsitoko, 0, '', '.')); ?></strong>
        </div>
        <?php endif; ?>
        <form action="<?php echo e(route('operatingcost-post', ['store_slug' => $store_slug])); ?>" enctype="multipart/form-data"
            class="_js-form" method="POST">
            <?php echo csrf_field(); ?>
            <operatingcost-add :role_id="<?php echo e(auth()->user()->role_id); ?>"
                :current_user="<?php echo e(auth()->user()->with(['employee'])->first()); ?>" :armadas="<?php echo e($armadas); ?>"
                :stores="<?php echo e($stores); ?>" :current_store_id="<?php echo e($store->id); ?>" :cost_categories="<?php echo e($cost_categories); ?>"
                :sdm="<?php echo e($sdm); ?>" />
        </form>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/operatingcost-add.blade.php ENDPATH**/ ?>