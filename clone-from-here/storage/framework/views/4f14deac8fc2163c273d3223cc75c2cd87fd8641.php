<?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.layout-addjob','data' => []]); ?>
<?php $component->withName('layout-addjob'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3" href="<?php echo e(route('jobs', ['store_slug' => $store_slug])); ?>"><svg
                class="w-9 h-9" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Add Job
            </h2>
        </a>
        
        
     <?php $__env->endSlot(); ?>

    <div
        class="fixed z-0 top-0 left-0 right-0 flex items-center justify-center h-screen text-5xl font-bold text-gray-300 _js-loading">
        Loading...
    </div>
    <form action="<?php echo e(route('order-post', ['store_slug' => $store_slug])); ?>" class="_js-form" method="POST">
        <?php echo csrf_field(); ?>
        <order-add :current_role_id="<?php echo e(auth()->user()->role_id); ?>"
            :current_store_is_notif_wa="<?php echo e($store->is_notif_wa); ?>" current_store_open_hour="<?php echo e($store->open_hour); ?>"
            current_store_close_hour="<?php echo e($store->close_hour); ?>" current_store_id="<?php echo e($store->id); ?>" url_jobs_temp=<?php echo e(route('order-add-vue', ['store_slug'=> 'xxx'])); ?>

            :stores="<?php echo e($stores->toJson()); ?>"
            :drivers="<?php echo e($drivers->toJson()); ?>"
            />
    </form>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/order-add-vue.blade.php ENDPATH**/ ?>