<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Change Store</title>

    <link rel="shortcut icon" type="image/x-icon" href="docs/images/favicon.ico" />

    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp"></script>
    
    
    
    

    <style>
        html,
        body {
            height: 100%;
            overflow: hidden;
            width: 100%;
            margin: 0;
            padding: 0;
        }
    </style>

    <!-- Matomo -->
    <script>
        var _paq = window._paq = window._paq || [];
    /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
    _paq.push(['trackPageView']);
    _paq.push(['enableLinkTracking']);
    (function() {
      var u="//stat.ordergasplus.online/";
      _paq.push(['setTrackerUrl', u+'matomo.php']);
      _paq.push(['setSiteId', '2']);
      var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
      g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
    })();
    </script>
    <!-- End Matomo Code -->

    <!-- Hotjar Tracking Code for Gasplus CS -->
    <script>
        (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:6373432,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>



</head>

<body class="prose p-5 h-full flex flex-col max-w-none">
    <h3>Pindah Pelanggan 🛍 "<?php echo e($stores->map(function($store) {
        return $store->name;
        })->join(', ')); ?>" ke ➡️ "<?php echo e($store_destination->name); ?>"</h3>
    <?php if($distance_store): ?>
    <h4>Yang dekat dari toko "<?php echo e($distance_store->name); ?>" <?php if($max_distance_store > 0): ?>maksimal berjakar <?php echo e($max_distance_store/1000); ?>km <?php endif; ?></h4>
    <?php endif; ?>
    <p>(<?php echo e($customers->count()); ?> Pelanggan)</p>
    <form id="form_change_store" class="overflow-y-auto flex-1 flex flex-col gap-2.5">
        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div>
            <label
                class="flex items-start bg-gray-100 rounded-lg w-full py-1 px-2 <?php if($customer->addresses->count() > 1): ?> opacity-50 <?php endif; ?>">
                <input name="address_ids" value="<?php echo e($customer->addresses[0]->id); ?>" class="form-checkbox mt-1.5"
                    type="checkbox" <?php if($customer->addresses->count() === 1): ?>checked
                <?php else: ?> disabled <?php endif; ?>>
                <div class="flex flex-col ml-2 gap-0.5">
                    <div class="font-bold"><?php echo e($customer->name); ?> <a class="font-normal text-sm"
                            href="https://wa.me/<?php echo e($customer->phone); ?>" target="_blank">wa.me/<?php echo e($customer->phone); ?></a>
                    </div>
                    <div class="flex flex-col gap-0.5 text-xs">
                        <?php echo $customer->addresses->map(function($address) use ($distance_store) {
                        $distance = $address->storedistances->count() > 0 ? '<h5 style="font-weight: bold;">Jarak Toko:
                        </h5>'.$address->storedistances
                        // ->take(4)
                        ->map(function ($str) use ($distance_store) {
                        if ($distance_store && (int)$str->id !== (int)$distance_store->id) return;
                        if ($str->pivot->distance_meter_by_route > 20000 || $str->pivot->distance_meter > 20000) return;
                        return '<div>' .($str->pivot->distance_meter_by_route ? '📍 ' : '⚫️ ') . $str->name . ' (' .
                            number_format($str->pivot->distance_meter_by_route ? $str->pivot->distance_meter_by_route :
                            $str->pivot->distance_meter, 0, '', '.') . 'm)</div>';
                        })
                        ->join('') : null;
                        return '(🛍 '.$address->store->name.') 🏠 '.$address->address .$distance;
                        })->join('
                        <hr class="my-2">'); ?>

                    </div>
                </div>
            </label>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </form>
    <button id="btn_submit" onclick="onClickSubmit()"
        class="my-3 text-xl py-2 w-full bg-red-600 text-white rounded-lg font-bold">PINDAH PELANGGAN</button>

    <script src="<?php echo e(url('/js/axios.min.js')); ?>"></script>
    <script>
        // const stores = <?php echo json_encode($stores, 15, 512) ?>;
        // const distance_store = <?php echo json_encode($distance_store, 15, 512) ?>;
        // const max_distance_store = <?php echo json_encode($max_distance_store, 15, 512) ?>;
        const data = <?php echo json_encode($data, 15, 512) ?>;
        // const customers = <?php echo json_encode($customers, 15, 512) ?>;
        // console.log("🚀 ~ stores:", stores)
        // console.log("🚀 ~ distance_store:", distance_store)
        // console.log("🚀 ~ max_distance_store:", max_distance_store)
        // console.log("🚀 ~ data:", data)
        // console.log("🚀 ~ customers:", customers)

        function onClickSubmit() {
            if (confirm('Yakin ingin memindah pelanggan terpilih ke "<?php echo e($store_destination->name); ?>"?')) {
                const btn = document.getElementById("btn_submit");
                const form = document.getElementById('form_change_store');
                const inputs = form.elements;
                console.log("🚀 ~ inputs:", inputs)
                const formData = new FormData(form);
                console.log("🚀 ~ formData:", formData.getAll('address_ids'))
                btn.disabled = true;
                btn.style.opacity = 0.5;
                btn.innerHTML = "MEMINDAHKAN PELANGGAN...";
                for (var i = 0, len = inputs.length; i < len; ++i) {
                    inputs[i].disabled = true;
                }
                axios.post("<?php echo e(route('changestore')); ?>", {
                    address_ids: formData.getAll('address_ids'),
                    destination_store_id: data.store_id,
                })
                .then(function (res) {
                    // btn.disabled = false;
                    btn.style.opacity = 0.8;
                    btn.innerHTML = "SUCCESS ✅ Silahkan REFRESH (F5)";
                    alert("BERHASIL memindah pelanggan. Silahkan REFRESH (F5) halaman.")
                    console.log('res', res);
                })
                .catch(function (error) {
                    console.log('error', error);
                    btn.disabled = false;
                    btn.style.opacity = 1;
                    btn.innerHTML = "PINDAH PELANGGAN";
                    for (var i = 0, len = inputs.length; i < len; ++i) {
                        inputs[i].disabled = false;
                    }
                    alert("Pindah pelanggan GAGAL!");
                });
            }
        }
        // (function() {
            // console.log("🚀 ~ ready");
            // const btn = document.getElementById("btn_submit");
            // console.log("🚀 ~ btn:", btn)
            // btn.addEventListener("click", function () {
            //     if (confirm('Yakin?')) {
            //         onClickSubmit(this);
            //     }
            // });

        // })();
    </script>



</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/sharp/change-store.blade.php ENDPATH**/ ?>