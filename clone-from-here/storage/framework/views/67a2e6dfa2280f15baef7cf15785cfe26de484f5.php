<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3" href="<?php echo e(route('jobs', ['store_slug' => $store_slug])); ?>"><svg
                class="w-9 h-9" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="font-bold text-lg">
                Add Purchasing
            </h2>
        </a>
        
     <?php $__env->endSlot(); ?>

    <div class="pt-20 pb-20 px-5">
        <form action="<?php echo e(route('purchase-post', ['store_id' => $store->id])); ?>" class="_js-form" method="POST">
            <?php echo csrf_field(); ?>
            <purchase-add :stores="<?php echo e($stores); ?>" :products="<?php echo e($products); ?>" :current_store_id="<?php echo e($store->id); ?>"
                :vendors="<?php echo e($vendors); ?>" url_base_temp=<?php echo e(route('purchase-add', ['store_slug'=> 'xxx'])); ?> />
        </form>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/purchase-add.blade.php ENDPATH**/ ?>