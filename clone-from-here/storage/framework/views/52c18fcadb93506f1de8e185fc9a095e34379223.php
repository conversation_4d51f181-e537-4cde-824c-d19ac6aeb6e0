<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>Accurate Sync - Step 4</title>

  <!-- Fonts -->
  

  <!-- Styles -->
  <link rel="stylesheet" href="<?php echo e(mix('css/app.css')); ?>">

  <!-- Matomo -->
  <script>
    var _paq = window._paq = window._paq || [];
  /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
  _paq.push(['trackPageView']);
  _paq.push(['enableLinkTracking']);
  (function() {
    var u="//stat.ordergasplus.online/";
    _paq.push(['setTrackerUrl', u+'matomo.php']);
    _paq.push(['setSiteId', '2']);
    var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
    g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
  })();
  </script>
  <!-- End Matomo Code -->

  <!-- Hotjar Tracking Code for Gasplus CS -->
  <script>
    (function(h,o,t,j,a,r){
      h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
      h._hjSettings={hjid:6373432,hjsv:6};
      a=o.getElementsByTagName('head')[0];
      r=o.createElement('script');r.async=1;
      r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
      a.appendChild(r);
  })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
  </script>

</head>

<body class="antialiased">
  <div class="px-5 py-4 flex flex-col gap-5 h-screen">
    <h1 class="text-2xl font-bold">Sync <span class="underline"><?php echo e($store_name); ?></span> (Step 4 - Create New Customer)
    </h1>
    <p class="text-blue-600 text-xl font-bold">TOTAL: <?php echo e(count($unsync_customer_addresses)); ?> customers</p>
    <form action="<?php echo e(route('accurate-sync-process-4')); ?>" method="POST"
      class="flex flex-1 overflow-auto border-b border-gray-200">
      <?php echo csrf_field(); ?>
      <input type="hidden" name="store_id" value="<?php echo e($store_id); ?>" />
      <table class="w-full border text-xs">
        <thead>
          <tr class="divide-x">
            <th class="sticky top-0 px-1.5 py-2 text-black bg-blue-300 "><input type="checkbox" id="checkall" /></th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-blue-300 ">ID GASPLUS</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-blue-300 ">Nama</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-blue-300">Data GASPLUS</th>
          </tr>
        </thead>
        <?php
        $data_count = 0;
        ?>
        <tbody class="divide-y divide-x">
          <?php $__currentLoopData = $unsync_customer_addresses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer_address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <?php
          $data_count++;
          ?>
          <tr class="divide-x bg-blue-50 text-black hover:bg-blue-100">
            <td class="px-1.5 py-2 text-center">
              <input type="checkbox" name="address_ids[]" value="<?php echo e($customer_address->id); ?>" <?php echo e(date("Y",
                strtotime($customer_address->lastorder->created_at ?? '')) >= 2021 ? 'checked' : ''); ?> />
            </td>
            <td class="px-1.5 py-2 text-left"><?php echo e($customer_address->id); ?>

            </td>
            <td class="px-1.5 py-2 text-left whitespace-nowrap"><?php echo e($customer_address->customer->name); ?>

              <br />
              <a class="text-blue-500 underline font-semibold"
                href="/cs/show/customer/<?php echo e($customer_address->customer->id); ?>" target="_blank">Detail Pelanggan ↗️</a>
            </td>
            <td class="px-1.5 py-2 text-left align-top">
              <strong>No. WA:</strong> <?php echo e($customer_address->customer->phone); ?>

              <br />
              <strong>Alamat:</strong> <?php echo e($customer_address->address); ?>

              <br />
              <strong>Toko:</strong> <?php echo e($customer_address->store->name); ?>

              <br />
              <strong>Last Order:</strong> <?php echo e($customer_address->lastorder->created_at ?? ''); ?>

            </td>
          </tr>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
      </table>
      <div class="fixed bottom-0 left-0 right-0 p-5 shadow-2xl z-50 bg-white border-t border-gray-200">
        <button type="submit" <?php echo e($data_count===0 ? 'disabled' : ''); ?>

          class="rounded-md w-full <?php echo e($data_count===0 ? 'opacity-50' : ''); ?> bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">Create
          New Customer on Accurate</button>
      </div>
    </form>
    <div class="h-20">&nbsp;</div>
  </div>

  <script src="<?php echo e(url('/js/jquery.min.js')); ?>"></script>
  <script>
    $( document ).ready(function() {
      console.log('ready');
      $('#checkall').change(function() {
        console.log('change');
        let checkboxes = $(this).closest('form').find(':checkbox');
        checkboxes.prop('checked', $(this).is(':checked'));
      });
    });
  </script>
</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/accurate-sync-step-4.blade.php ENDPATH**/ ?>