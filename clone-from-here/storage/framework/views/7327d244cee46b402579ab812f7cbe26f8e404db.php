<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
   <?php $__env->slot('store_slug', null, []); ?> 
    <?php echo e($store_slug); ?>

   <?php $__env->endSlot(); ?>

   <?php $__env->slot('header', null, []); ?> 
    <div class="absolute inset-0 flex items-center px-4 bg-yellow-500">
      <h2 class="text-lg font-bold">
        <?php echo e(__('Report')); ?> <select class="inline p-0 ml-1 text-lg font-bold bg-transparent border-none _js-input-store">
          <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e(route('report', ['store_slug' => $store->slug])); ?>" <?php echo e($store->slug == $store_slug ?
            "selected" : null); ?>>
            <?php echo e($store->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </h2>
    </div>
   <?php $__env->endSlot(); ?>

  <div class="px-8 pb-20 pt-24 flex flex-col gap-8">
    <?php if(in_array(Auth::user()->role_id, [1,2])): ?>
    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['href' => ''.e(route('report.driver.list', ['store_slug' => $store_slug])).'','class' => 'w-full bg-blue-600']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['href' => ''.e(route('report.driver.list', ['store_slug' => $store_slug])).'','class' => 'w-full bg-blue-600']); ?>
      In-Job 🛵
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['href' => ''.e(route('report.lt2.list', ['store_slug' => $store_slug])).'','class' => 'w-full bg-blue-500']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['href' => ''.e(route('report.lt2.list', ['store_slug' => $store_slug])).'','class' => 'w-full bg-blue-500']); ?>
      Lantai 2 🎢
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['href' => ''.e(route('report.bbro', ['store_slug' => $store_slug])).'','class' => 'w-full bg-pink-600']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['href' => ''.e(route('report.bbro', ['store_slug' => $store_slug])).'','class' => 'w-full bg-pink-600']); ?>
      Performa B-Bro 🏍
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['href' => ''.e(route('report.product.select', ['store_slug' => $store_slug])).'','class' => 'w-full bg-green-600']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['href' => ''.e(route('report.product.select', ['store_slug' => $store_slug])).'','class' => 'w-full bg-green-600']); ?>
      Performa Produk 📦
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <?php endif; ?>
    <?php if(in_array(Auth::user()->role_id, [1,2]) || (is_array(Auth::user()->options) && array_key_exists('is_show_ltt',
    Auth::user()->options) && (int) Auth::user()->options['is_show_ltt'])): ?>
    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['href' => ''.e(route('report.stockopname.detail', ['store_slug' => $store_slug])).'','class' => 'w-full bg-yellow-600']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['href' => ''.e(route('report.stockopname.detail', ['store_slug' => $store_slug])).'','class' => 'w-full bg-yellow-600']); ?>
      (LTT) Lap. Tutup Toko 🏬
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <?php endif; ?>
    <?php if(in_array(Auth::user()->role_id, [1,2,3])): ?>
    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['href' => ''.e(route('report.deposit', ['store_slug' => $store_slug])).'','class' => 'w-full bg-indigo-600']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['href' => ''.e(route('report.deposit', ['store_slug' => $store_slug])).'','class' => 'w-full bg-indigo-600']); ?>
      Report Deposit 🌿
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <?php endif; ?>
  </div>

   <?php $__env->slot('js', null, []); ?> 
    <script>
      $( document ).ready(function() {
            // Change Store
            // --------------------------------------------------
            $('._js-input-store').change(function() {
                const url = this.value;
                window.location.replace(url);
            });
        });
    </script>
   <?php $__env->endSlot(); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/report.blade.php ENDPATH**/ ?>