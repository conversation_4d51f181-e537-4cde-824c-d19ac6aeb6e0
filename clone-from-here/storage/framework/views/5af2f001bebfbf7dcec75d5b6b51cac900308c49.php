<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
   <?php $__env->slot('store_slug', null, []); ?> 
    <?php echo e($store_slug); ?>

   <?php $__env->endSlot(); ?>

   <?php $__env->slot('header', null, []); ?> 
    <div class="absolute inset-0 flex px-4 items-center bg-yellow-500">
      <h2 class="font-bold text-base flex items-center w-full">
        <a href="<?php echo e(route('report', ['store_slug' => $store_slug])); ?>" class="flex -ml-3 items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          Produk
        </a>
        <select class="_js-input-store text-base inline font-bold ml-1 bg-transparent border-none p-0">
          <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option
            value="<?php echo e(route('report.product', ['ids' => $ids, 'store_slug' => $store->slug, 'month' => $_GET['month'] ?? ''])); ?>"
            <?php echo e($store->slug == $store_slug ?
            "selected" : null); ?>>
            <?php echo e($store->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <a class="w-13 ml-auto text-center -mr-3 flex flex-col justify-center items-center"
          href="<?php echo e(route('report.product.select', ['store_slug' => $store_slug])); ?>">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          
          <span class="text-xs block">Pilih Prod.</span></a>
      </h2>
    </div>
   <?php $__env->endSlot(); ?>

   <?php $__env->slot('subheader', null, []); ?> 
    <div class="mt-2 flex justify-center items-center h-10 text-yellow-500">
      <a class="w-6 h-6"
        href="<?php echo e(route('report.product', ['ids' => $ids, 'store_slug' => $store_slug, 'month' => $month_prev])); ?>"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
            clip-rule="evenodd" />
        </svg></a>
      <input type="month" class="mx-3 font-bold text-lg w-56 border-none p-0 text-center focus:ring-0 _js-input-month"
        max="<?php echo e(date('Y-m')); ?>" value="<?php echo e($month_now); ?>">
      <?php if($month_next): ?>
      <a class="w-6 h-6"
        href="<?php echo e(route('report.product', ['ids' => $ids, 'store_slug' => $store_slug, 'month' => $month_next])); ?>"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
            clip-rule="evenodd" />
        </svg></a>
      <?php endif; ?>
    </div>
    <div class="flex items-center font-bold text-xs text-center">
      <span class="capitalize text-gray-500 py-2 w-16">Tgl.</span>
      <span class="text-blue-700 py-2 w-16">Total</span>
      <div
        class="flex items-center border-l-2 scrollbar-hide border-gray-200 bg-yellow-50 flex-1 overflow-x-auto _js-scroll">
        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <span class="overflow-hidden overflow-ellipsis whitespace-nowrap text-blue-500 py-2 px-1 w-20 flex-shrink-0"><?php echo e($product->code); ?></span>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
    <div class="flex items-center font-bold text-xs text-center">
      <span class="capitalize text-gray-500 py-2 w-16">TOTAL</span>
      <?php
      $sum_total_all = 0;
      foreach ($products as $product) {
      $sum_total_all += $product->total;
      }
      ?>
      <span class="text-blue-700 py-2 w-16"><?php echo e($sum_total_all); ?></span>
      <div
        class="flex items-center border-l-2 scrollbar-hide border-gray-200 bg-yellow-50 flex-1 overflow-x-auto _js-scroll">
        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <span class="text-blue-500 py-2 w-20 px-1 flex-shrink-0"><?php echo e($product->total); ?></span>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
   <?php $__env->endSlot(); ?>

  <div class="pt-44 pb-20">
    <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div
      class="flex items-center font-bold text-sm text-center border-solid border-gray-200 border-b <?php echo e($month_now == date('Y-m') && $item['date'] == date('d') ? 'bg-yellow-100' : null); ?>">
      <div class="text-gray-500 h-11 w-16 flex flex-col justify-center items-center leading-none"><?php echo e($item['date']); ?><span class="mt-0 text-xs font-light"><?php echo e($item['day']); ?></span></div>
      <span class="text-blue-700 h-11 py-2 w-16"><?php echo e($item['day_total_delivery']); ?></span>
      <div class="flex items-center border-l-2 scrollbar-hide border-gray-200 flex-1 overflow-x-auto _js-scroll">
        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <span class="text-blue-500 h-11 py-2 px-1 w-20 flex-shrink-0"><?php echo e($item['product_total_delivery'][$product->id]); ?></span>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  </div>



   <?php $__env->slot('js', null, []); ?> 
    <script>
      $( document ).ready(function() {
            // Change Date
            // --------------------------------------------------
            $('._js-input-month').change(function() {
                const url = "<?php echo e(url()->current()); ?>";
                const month = this.value;
                console.log(url+'?month='+month);
                window.location.replace(url+'?month='+month);
            });
            $('._js-input-store').change(function() {
                const url = this.value;
                window.location.replace(url);
            });

            // Bind Multiple Scroll
            // --------------------------------------------------
            // $('._js-scroll').scrollsync()
            $('._js-scroll').scroll(function(e){
              $('._js-scroll').scrollLeft(e.target.scrollLeft);
            });
            // var scrollers = document.getElementsByClassName('_js-scroll');
            // var scrollerDivs = Array.prototype.filter.call(scrollers, function(testElement) {
            //   return testElement.nodeName === 'DIV';
            // });
            // function scrollAll(scrollLeft) {
            //   scrollerDivs.forEach(function(element, index, array) {
            //     element.scrollLeft = scrollLeft;
            //   });
            // }
            // scrollerDivs.forEach(function(element, index, array) {
            //   element.addEventListener('scroll', function(e) {
            //     scrollAll(e.target.scrollLeft);
            //   });
            // });
        });
    </script>
   <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/report-product.blade.php ENDPATH**/ ?>