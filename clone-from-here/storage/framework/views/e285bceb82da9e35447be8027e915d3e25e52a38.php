<?php $__env->startSection("content"); ?>

    <div id="sharp-app" class="login">
        <div class="container">
            <form method="POST" action="<?php echo e(route("code16.sharp.login.post")); ?>">
                <?php echo e(csrf_field()); ?>

                <div class="row justify-content-center">
                    <div class="col-sm-9 col-md-6 col-lg-5 col-xl-4">

                        <?php if(file_exists(public_path('/sharp-assets/login-icon.png'))): ?>
                            <div class="text-center">
                                <img src="<?php echo e(asset('/sharp-assets/login-icon.png')); ?>" alt="<?php echo e(config("sharp.name", "Sharp")); ?>" width="300" class="w-auto h-auto" style="max-height: 100px;max-width: 200px">
                            </div>
                        <?php else: ?>
                            <h1 class="text-center mb-3"><?php echo e(config("sharp.name", "Sharp")); ?></h1>
                        <?php endif; ?>

                        <?php if($errors->any()): ?>

                            <div role="alert" class="SharpNotification SharpNotification--error">
                                <div class="SharpNotification__details">
                                    <div class="SharpNotification__text-wrapper">
                                        <p class="SharpNotification__subtitle"><?php echo app('translator')->get('sharp::auth.validation_error'); ?></p>
                                    </div>
                                </div>
                            </div>

                        <?php elseif(session()->has('invalid')): ?>

                            <div role="alert" class="SharpNotification SharpNotification--error">
                                <div class="SharpNotification__details">
                                    <div class="SharpNotification__text-wrapper">
                                        <p class="SharpNotification__subtitle"><?php echo app('translator')->get('sharp::auth.invalid_credentials'); ?></p>
                                    </div>
                                </div>
                            </div>

                        <?php endif; ?>
                        <div class="SharpModule">
                            <div class="SharpModule__inner">
                                <div class="SharpModule__content">
                                    <div class="SharpForm__form-item SharpForm__form-item--row">
                                        <input type="text" name="login" id="login" class="SharpText" value="<?php echo e(old('login')); ?>" placeholder="<?php echo app('translator')->get('sharp::login.login_field'); ?>">
                                    </div>

                                    <div class="SharpForm__form-item SharpForm__form-item--row">
                                        <input type="password" name="password" id="password" class="SharpText" placeholder="<?php echo app('translator')->get('sharp::login.password_field'); ?>">
                                    </div>
                                    <button type="submit" id="submit" class="SharpButton SharpButton--primary">
                                        <?php echo app('translator')->get('sharp::login.button'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php if ($__env->exists(config("sharp.login_page_message_blade_path"))) echo $__env->make(config("sharp.login_page_message_blade_path"), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <p class="text-center mt-3 text-muted" style="font-size: .75em">powered by <a href="https://sharp.code16.fr/docs/">Sharp <?php echo e(sharp_version()); ?></a></p>
                    </div>
                </div>
            </form>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make("sharp::layout", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/vendor/code16/sharp/resources/views/login.blade.php ENDPATH**/ ?>