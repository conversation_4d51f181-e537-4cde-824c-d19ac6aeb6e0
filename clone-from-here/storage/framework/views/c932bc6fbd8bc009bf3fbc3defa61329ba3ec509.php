<?php $__env->startSection('content'); ?>
<div class="flex flex-col text-center gap-10 text-2xl h-screen w-screen justify-center items-center">
    <h1 class="font-bold text-gray-400">ORDER :: <?php echo e($order_code); ?></h1>
    <div class="font-bold flex flex-col gap-2 justify-center items-center">
        <span>TOTAL 💰</span>
        <div class="relative">
            <div
                class="_js-to-copy-nominal rounded-xl cursor-pointer py-1.5 flex items-center px-2.5 pr-20 bg-green-100 border border-gray-300 font-bold text-4xl text-truegreen-600">
                <?php echo e(number_format($total, 0, '',
                '.')); ?></div>
            <button type="button"
                class="absolute right-2.5 top-1/2 transform -translate-y-1/2 pointer-events-none text-sm px-1 flex justify-center items-center gap-1 rounded-lg font-bold border-2 border-yellow-500 text-yellow-600">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3">
                    <path fill-rule="evenodd"
                        d="M17.663 3.118c.**************.673.05C19.876 3.298 21 4.604 21 6.109v9.642a3 3 0 01-3 3V16.5c0-5.922-4.576-10.775-10.384-11.217.324-1.132 1.3-2.01 2.548-2.114.224-.019.448-.036.673-.051A3 3 0 0113.5 1.5H15a3 3 0 012.663 1.618zM12 4.5A1.5 1.5 0 0113.5 3H15a1.5 1.5 0 011.5 1.5H12z"
                        clip-rule="evenodd" />
                    <path
                        d="M3 8.625c0-1.036.84-1.875 1.875-1.875h.375A3.75 3.75 0 019 10.5v1.875c0 1.036.84 1.875 1.875 1.875h1.875A3.75 3.75 0 0116.5 18v2.625c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 013 20.625v-12z" />
                    <path
                        d="M10.5 10.5a5.23 5.23 0 00-1.279-3.434 9.768 9.768 0 016.963 6.963 5.23 5.23 0 00-3.434-1.279h-1.875a.375.375 0 01-.375-.375V10.5z" />
                </svg>
                Salin
            </button>
        </div>
    </div>
    <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="flex flex-col gap-2">
        <strong><?php echo e($payment->bank_name); ?> 🏧</strong>
        <div class="relative">
            <div
                class="_js-to-copy-norek rounded-xl cursor-pointer py-1.5 flex items-center px-2.5 pr-20 bg-gray-100 border border-gray-300 font-bold text-trueblue-600">
                <?php echo e($payment->account_number); ?></div>
            <button type="button"
                class="absolute right-2.5 top-1/2 transform -translate-y-1/2 pointer-events-none text-sm px-1 flex justify-center items-center gap-1 rounded-lg font-bold border-2 border-yellow-500 text-yellow-600">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3">
                    <path fill-rule="evenodd"
                        d="M17.663 3.118c.**************.673.05C19.876 3.298 21 4.604 21 6.109v9.642a3 3 0 01-3 3V16.5c0-5.922-4.576-10.775-10.384-11.217.324-1.132 1.3-2.01 2.548-2.114.224-.019.448-.036.673-.051A3 3 0 0113.5 1.5H15a3 3 0 012.663 1.618zM12 4.5A1.5 1.5 0 0113.5 3H15a1.5 1.5 0 011.5 1.5H12z"
                        clip-rule="evenodd" />
                    <path
                        d="M3 8.625c0-1.036.84-1.875 1.875-1.875h.375A3.75 3.75 0 019 10.5v1.875c0 1.036.84 1.875 1.875 1.875h1.875A3.75 3.75 0 0116.5 18v2.625c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 013 20.625v-12z" />
                    <path
                        d="M10.5 10.5a5.23 5.23 0 00-1.279-3.434 9.768 9.768 0 016.963 6.963 5.23 5.23 0 00-3.434-1.279h-1.875a.375.375 0 01-.375-.375V10.5z" />
                </svg>
                Salin
            </button>
        </div>
        <p>a.n. <?php echo e($payment->holder_name); ?></p>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script type="module">
    document.addEventListener("DOMContentLoaded", () => {

        const totalDue = document.getElementsByClassName('_js-to-copy-nominal');
        if (totalDue && totalDue.length > 0) {
            Object.keys(totalDue).forEach(key=>{
                const obj = totalDue[key];
                obj.onclick = function() {
                    document.execCommand("copy");
                    // console.log('click copy');
                }
                obj.addEventListener("copy", function(event) {
                    event.preventDefault();
                    if (event.clipboardData) {
                        event.clipboardData.setData("text/plain", obj.textContent.trim().replace('.',''));
                        mdtoast(
                        'Nominal berhasil disalin 👌', {
                            position: 'top center',
                            type: mdtoast.SUCCESS,
                            duration: 3000,
                            // interaction: true, 
                            // actionText: 'Tutup',
                            // action: function(){ this.hide(); }
                        });
                        // console.log(event.clipboardData.getData("text"))
                    }
                });
            })
        }

        const accountNumber = document.getElementsByClassName('_js-to-copy-norek');
        if (accountNumber && accountNumber.length > 0) {
            Object.keys(accountNumber).forEach(key=>{
                const obj = accountNumber[key];
                obj.onclick = function() {
                    document.execCommand("copy");
                    // console.log('click copy');
                }
                obj.addEventListener("copy", function(event) {
                    event.preventDefault();
                    if (event.clipboardData) {
                        event.clipboardData.setData("text/plain", obj.textContent.trim().replace('.',''));
                        mdtoast(
                        'Nomor rekening berhasil disalin 👍', {
                            position: 'top center',
                            type: mdtoast.INFO,
                            duration: 3000,
                            // interaction: true, 
                            // actionText: 'Tutup',
                            // action: function(){ this.hide(); }
                        });
                        // console.log(event.clipboardData.getData("text"))
                    }
                });
            })
        }

    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.info', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/info/payment.blade.php ENDPATH**/ ?>