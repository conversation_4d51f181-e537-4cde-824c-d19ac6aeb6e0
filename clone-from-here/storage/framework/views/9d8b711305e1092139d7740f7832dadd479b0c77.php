<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

    <div class="absolute text-green-900 bg-pink-500 border-green-900 pointer-events-none"></div>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3"
            href="<?php echo e(route('jobs', ['store_slug' => $store_slug, 'date' => $date_now])); ?>"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                
                <span class="capitalize"><?php echo e($store_slug); ?></span>
            </h2>
        </a>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('subheader', null, []); ?> 
        <div class="flex w-full h-14">
            <div class="flex flex-grow flex-col p-4 justify-center h-14 text-white bg-green-700">
                <p class="text-xs font-bold capitalize"><?php echo e(date('d F Y', strtotime($date_now))); ?>: Pengeluaran</p>
                <?php if($operatingcosts->count()): ?>
                <p class="text-xs font-light"><?php echo e($operatingcosts[0]->updated_at); ?></p>
                <?php endif; ?>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="pb-20 pt-28">
        <div
            class="fixed top-0 left-0 right-0 z-auto flex items-center justify-center h-screen text-5xl font-bold text-gray-300 _js-loading">
            Loading...
        </div>
        <operatingcost-list operatingcosts_original="<?php echo e($operatingcosts->toJson()); ?>"
            :role_id="<?php echo e(auth()->user()->role_id); ?>" :user_id="<?php echo e(auth()->user()->id); ?>"
            username="<?php echo e(auth()->user()->name); ?>" base_url="<?php echo e(URL::to('/')); ?>" date_now="<?php echo e($date_now); ?>">
        </operatingcost-list>
        
    </div>

     <?php $__env->slot('js', null, []); ?> 
        <script>
            $(document).ready(function() {
                setTimeout(() => {
                    $('._js-loading').fadeOut('fast');
                }, 500);
            })
        </script>
     <?php $__env->endSlot(); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/operatingcost-list.blade.php ENDPATH**/ ?>