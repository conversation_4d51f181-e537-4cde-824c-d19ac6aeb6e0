<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>WBCL Customers</title>
    
    
    
    
    <link rel="stylesheet" href="<?php echo e(mix('css/app.css')); ?>">

    <!-- Matomo -->
    <script>
        var _paq = window._paq = window._paq || [];
    /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
    _paq.push(['trackPageView']);
    _paq.push(['enableLinkTracking']);
    (function() {
      var u="//stat.ordergasplus.online/";
      _paq.push(['setTrackerUrl', u+'matomo.php']);
      _paq.push(['setSiteId', '2']);
      var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
      g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
    })();
    </script>
    <!-- End Matomo Code -->

    <!-- Hotjar Tracking Code for Gasplus CS -->
    <script>
        (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:6373432,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>

</head>

<body class="antialiased">
    <div class="px-5 py-4 flex flex-col gap-5 h-screen">
        <h1 class="text-2xl font-bold">WBCL "<?php echo e($notif_schedule->criteria_title); ?>"</h1>
        <p class="text-gray-500 text-lg font-bold"> <?php echo e(count($customers)); ?> Sample Pelanggan</p>
        <div class="flex flex-1 overflow-auto border-b border-gray-200">
            <table class="w-full border text-xs">
                <thead>
                    <tr class="divide-x">
                        <th class="sticky top-0 px-1.5 py-2 text-black bg-gray-300">ID</th>
                        <th class="sticky top-0 px-1.5 py-2 text-black bg-gray-300">Name</th>
                        <th class="sticky top-0 px-1.5 py-2 text-black bg-gray-300">Data GASPLUS</th>
                        <th class="sticky top-0 px-1.5 py-2 text-black bg-gray-300">Last Order</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-x">
                    <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="divide-x">
                        <td class="px-1.5 py-2 text-left"><?php echo e($customer->id); ?></td>
                        <td class="px-1.5 py-2 text-left"><?php echo e($customer->name); ?></td>
                        <td class="px-1.5 py-2 text-left align-top">
                            <strong>WA:</strong> <?php echo e($customer->phone); ?>

                            <?php $__currentLoopData = $customer->addresses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($address->store): ?>
                            <br />
                            <strong>Order <?php echo e($address->store->name); ?></strong> at <?php echo e($address->lastorder ? $address->lastorder->created_at : '-'); ?> : <?php echo e($address->address); ?>

                            <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </td>
                        <td class="px-1.5 py-2 text-left whitespace-nowrap">
                            <?php echo e(isset($customer->options['last_order_at']) ?
                            $customer->options['last_order_at'] : '-'); ?></td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        
    </div>
</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/sharp/wbcl-customers.blade.php ENDPATH**/ ?>