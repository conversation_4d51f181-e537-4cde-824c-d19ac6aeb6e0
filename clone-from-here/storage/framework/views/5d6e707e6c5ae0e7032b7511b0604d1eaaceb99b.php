<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
   <?php $__env->slot('store_slug', null, []); ?> 
    <?php echo e($store_slug); ?>

   <?php $__env->endSlot(); ?>

   <?php $__env->slot('header', null, []); ?> 
    <div class="absolute inset-0 flex px-4 items-center bg-yellow-500">
      <h2 class="font-bold text-base flex items-center">
        <a href="<?php echo e(route('report', ['store_slug' => $store_slug])); ?>" class="flex -ml-3 items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          Produk
        </a>
        <select class="_js-input-store text-base inline font-bold ml-1 bg-transparent border-none p-0">
          <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option
            value="<?php echo e(route('report.product.select', ['store_slug' => $store->slug, 'month' => $_GET['month'] ?? ''])); ?>"
            <?php echo e($store->slug == $store_slug ?
            "selected" : null); ?>>
            <?php echo e($store->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
      </h2>
    </div>
   <?php $__env->endSlot(); ?>

  <div class="pt-16 pb-52">
    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <label for="<?php echo e($product->code); ?>" class="flex items-center px-4 text-sm h-11 border-solid border-gray-200 border-b">
      <input type="checkbox" class="mr-2 _js-checkbox" id="<?php echo e($product->code); ?>" name="<?php echo e($product->code); ?>"
        value="<?php echo e($product->id); ?>">
      <strong class="mr-2"><?php echo e($product->code); ?></strong> <span
        class="overflow-hidden overflow-ellipsis whitespace-nowrap"><?php echo e($product->name); ?></span>
    </label>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  </div>

  <div class="fixed left-0 right-0 bottom-16 ">
    <div
      class="max-w-xl mx-auto pt-3 pb-6 px-4 bg-white z-20 flex flex-col justify-center items-center border-solid border-t-2 border-gray-300 shadow ">
      <h5 class="text-sm text-gray-400 font-bold">Pilih MAX 6 Produk</h5>
      <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.link','data' => ['href' => '#','class' => 'w-full bg-green-600 mt-3 opacity-50 pointer-events-none _js-btn-report']]); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['href' => '#','class' => 'w-full bg-green-600 mt-3 opacity-50 pointer-events-none _js-btn-report']); ?>
        Lihat Performa Produk 📦
       <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    </div>
  </div>

   <?php $__env->slot('js', null, []); ?> 
    <script>
      $( document ).ready(function() {
        const ids = [];

        $('._js-checkbox').change(function() {
          if (ids.length == 6) {
            $(this).prop('checked', false);
            alert('Pilih MAX 6 Produk');
            return;
          }
          if(this.checked) {
              // console.log('check');
              // console.log(this.value);
              ids.push(this.value);
            } else {
            // console.log('uncheck');
            // console.log(this.value);
            ids.splice (ids.indexOf(this.value), 1);
          }
          if (ids.length == 0) {
            $('._js-btn-report').addClass('opacity-50 pointer-events-none');
          } else {
            $('._js-btn-report').removeClass('opacity-50 pointer-events-none');
          }
          const url = window.location.href + '/' + ids.join('-');
          $('._js-btn-report').attr('href', url);
        });
      });
    </script>
   <?php $__env->endSlot(); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/report-product-select.blade.php ENDPATH**/ ?>