<sharp-left-nav v-cloak
    current="<?php echo e($sharpMenu->currentEntity); ?>"
    title="<?php echo e($sharpMenu->name); ?>"
    :items="<?php echo e(json_encode($sharpMenu->menuItems)); ?>"
    :has-global-filters="<?php echo e(json_encode($hasGlobalFilters)); ?>"
>
    <?php if(file_exists(public_path('/sharp-assets/menu-icon.png'))): ?>
        <template slot="title">
            <img src="<?php echo e(asset('/sharp-assets/menu-icon.png')); ?>" alt="<?php echo e($sharpMenu->name); ?>" width="150" class="w-auto h-auto" style="max-height: 50px;">
        </template>
    <?php endif; ?>
    <ul role="menubar" class="SharpLeftNav__list" aria-hidden="false">
        <sharp-nav-item disabled>
            <span title="<?php echo e($sharpMenu->user); ?>">
                <?php echo e($sharpMenu->user); ?>

            </span>
            <a href="<?php echo e(route('code16.sharp.logout')); ?>"> <sharp-item-visual :item="{ icon:'fas fa-sign-out-alt' }" icon-class="fa-fw"></sharp-item-visual></a>
        </sharp-nav-item>

        <?php $__currentLoopData = $sharpMenu->menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menuItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($menuItem->type == 'category'): ?>
                <sharp-collapsible-item label="<?php echo e($menuItem->label); ?>">
                    <?php $__currentLoopData = $menuItem->entities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo $__env->make('sharp::partials._menu-item', [
                            'item' => $entity,
                            'isCurrent' => $sharpMenu->currentEntity == $entity->key
                        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </sharp-collapsible-item>
            <?php else: ?>
                <?php echo $__env->make('sharp::partials._menu-item', [
                    'item' => $menuItem,
                    'isCurrent' => $sharpMenu->currentEntity == $menuItem->key
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
</sharp-left-nav><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/vendor/code16/sharp/resources/views/partials/_menu.blade.php ENDPATH**/ ?>