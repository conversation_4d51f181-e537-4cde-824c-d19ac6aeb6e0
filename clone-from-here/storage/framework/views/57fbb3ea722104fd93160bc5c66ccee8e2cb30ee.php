<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e('palagan'); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3" href="<?php echo e(route('drivers', ['store_slug' => 'palagan'])); ?>"><svg
                class="w-9 h-9" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="font-bold text-lg">
                <?php echo e(__('Drivers Palagan')); ?>

            </h2>
        </a>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('subheader', null, []); ?> 
        <div class="flex flex-col p-4 justify-center h-14 text-gray-600 bg-gray-100">
            <p class="font-bold text-sm">Aldo (6 Jobs)</p>
            <p class="font-light text-xs">Updated 2021-01-26 18:33:55</p>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="pt-28 pb-20">
        <?php for($i = 1; $i <= 3; $i++): ?> <?php echo $__env->make('components.order-item', ['order'=> null], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> <?php endfor; ?>
            <?php for($i = 1; $i <= 2; $i++): ?> <?php echo $__env->make('components.order-item', ['order'=> null], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> <?php endfor; ?>
                <?php for($i = 1; $i <= 1; $i++): ?> <?php echo $__env->make('components.order-item', ['order'=> null], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> <?php endfor; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/driver-jobs.blade.php ENDPATH**/ ?>