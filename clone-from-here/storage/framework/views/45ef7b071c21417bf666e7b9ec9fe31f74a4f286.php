<!DOCTYPE html>
<html>

<head>
  <title>Order Gasplus Telah Sampai ✅ <?php echo e($order['code']); ?><</title> </head> <body>
      <h1>Order Gasplus Telah Sampai ✅ <?php echo e($order['code']); ?></h1>
      <br>
      <h3>Pelanggan</h3>
      <hr>
      <p><strong>Nama:</strong> <?php echo e($order['customer']['name']); ?></p>
      <p><strong>HP:</strong> <a
          href="https://wa.me/<?php echo e($order['receiver_phone'] ? $order['receiver_phone'] : $order['customer']['phone']); ?>"
          target="_blank">WA 📲
          <?php echo e($order['receiver_phone'] ? $order['receiver_phone'] : $order['customer']['phone']); ?></a></p>
      <p><strong>Alamat:</strong> <?php echo e($order['address']['address']); ?></p>

      <h3>Produk</h3>
      <hr>
      <?php $__currentLoopData = $order['products']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <p><?php echo e($product['pivot']['qty']); ?>x <?php echo e($product['name']); ?>

        @Rp<?php echo e(number_format($product['pivot']['price'], 0, '', '.')); ?>

      </p>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      <?php if($order['note']): ?>
      <p><strong>Catatan: </strong> <mark><?php echo e($order['note']); ?></mark></p>
      <?php endif; ?>

      <h3>Pembayaran</h3>
      <hr>
      <?php if($order['payment'] == 'cash'): ?>
      <p><strong>Tunai</strong> (Rp<?php echo e($order['amount_will_pay']); ?>)</p>
      <p><em>*bisa juga transfer ke:</em></p>
      <?php else: ?>
      <p><strong>Transfer</strong></p>
      <?php endif; ?>
      <?php $__currentLoopData = $order['store']['banks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bank): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <p><strong><?php echo e($bank['bank_name']); ?> <?php echo e($bank['account_number']); ?> 🏧</strong><br>a.n. <?php echo e($bank['holder_name']); ?>

      </p>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      <?php if($order['payment'] == 'cash'): ?>
      <p><strong>Jika bayar transfer, harap mengirimkan foto <mark>BUKTI TRANSFER</mark></strong></p>
      <?php else: ?>
      <p><strong>Harap mengirimkan foto <mark>BUKTI TRANSFER</mark></strong></p>
      <?php endif; ?>
      <?php
      $total_qty = 0;
      foreach ($order['products'] as $product) {
      $total_qty = $total_qty + intval($product['pivot']['qty']);
      }
      ?>
      <p><strong>Total QTY:</strong> <?php echo e($total_qty); ?></p>
      <p><strong>Total Harga:</strong> Rp<?php echo e(number_format($order['total'], 0, '', '.')); ?></p>
      <p>Ongkos Kirim: <strong>FREE</strong></p>
      <p>Ekspedisi: <strong>Kurir Gasplus</strong></p>
      <p><strong>TOTAL BAYAR: Rp<?php echo e(number_format($order['total'], 0, '', '.')); ?></strong></p>

      <?php if($order['received_by'] || $order['driver_note'] || $order['receivephoto_url']): ?>
      <h3>Info Penerimaan Order</h3>
      <hr>
      <?php if($order['received_by']): ?>
      <p><strong>Nama Driver:</strong> <?php echo e($order['driver']['name']); ?></p>
      <p><strong>Diterima Oleh:</strong> <?php echo e($order['received_by']); ?></p>
      <?php endif; ?>
      <?php if($order['driver_note']): ?>
      <p><strong>Catatan Driver:</strong> <?php echo e($order['driver_note']); ?></p>
      <?php endif; ?>
      <?php if($order['receivephoto_url']): ?>
      <img style="width: 100%; max-width: 300px; height: auto;" src="<?php echo e($order['receivephoto_url']); ?>" alt="Foto ">
      <?php endif; ?>
      <?php endif; ?>
      </body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/emails/delivered.blade.php ENDPATH**/ ?>