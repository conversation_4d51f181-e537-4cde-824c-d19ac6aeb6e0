<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('head', null, []); ?> 
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.8.0/dist/leaflet.css"
            integrity="sha512-hoalWLoI8r4UszCkZ5kL8vayOGVae1oxXe/2A4AO6J9+580uKHDO3JdHb7NzwwzK5xr/Fs0W40kiNHxM9vyTtQ=="
            crossorigin="" />
        <script src="https://unpkg.com/leaflet@1.8.0/dist/leaflet.js"
            integrity="sha512-BB3hKbKWOc9Ez/TAwyWxNXeoV9c1v6FIeYiBieIWkpLjauysF18NzgR1MBNBXf8/KABdlkX68nAhlwcDFLGPCQ=="
            crossorigin=""></script>
     <?php $__env->endSlot(); ?>

    <div class="absolute text-green-900 bg-pink-500 border-green-900 pointer-events-none"></div>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3"
            href="<?php echo e(route('jobs', ['store_slug' => $store_slug, 'date' => $date_now])); ?>"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                
                <span class="capitalize"><?php echo e($store_slug); ?></span>
            </h2>
        </a>
        <div class="ml-auto flex items-center -mr-2 gap-x-4">
            <?php if(in_array(auth()->user()->role_id, [1])): ?>
            <button type="button" class="text-3xl focus:outline-none _js-btn-resync-job-acc">♻️</button>
            <?php endif; ?>
            <button class="text-3xl focus:outline-none" @click="onOpenSearch">🔍</button>
            <button class="focus:outline-none" data-store='<?php echo json_encode($store, 15, 512) ?>' data-marketings='<?php echo json_encode($marketings, 15, 512) ?>'
                data-orders='<?php echo json_encode($orders, 15, 512) ?>' @click="onOpenMap">
                <img src="<?php echo e(asset('public/images/map-768x768.webp')); ?>"
                    class="object-contain w-12 h-12 pointer-events-none" /></button>
        </div>
        <template v-if="searchIsShow">
            <div :class="[' absolute left-0 inset-0 bg-red-700 items-center z-50 flex']">
                <input v-model="search"
                    class="flex-1 min-w-0 px-4 mr-5 text-2xl placeholder-red-200 bg-red-700 focus:outline-none _js-input-search"
                    placeholder="Cari..." />
                <button class="ml-auto -mt-1.5 text-3xl mr-5 focus:outline-none" @click="onCloseSearch">╳</button>
            </div>
        </template>
        <input class="absolute opacity-0 pointer-events-none" id="search_query" value="<?php echo e($search_query); ?>" />
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('subheader', null, []); ?> 
        <div class="flex w-full h-14">
            <div class="flex flex-grow flex-col p-4 justify-center h-14 text-white <?php echo e($color); ?>">
                <?php if(app('request')->input('ltt') === 'std_cpr'): ?>
                <p class="text-xs font-bold capitalize"><?php echo e(date('d F Y', strtotime($date_now))); ?>: STD CPR</p>
                <?php elseif(app('request')->input('ltt') === 'spd_deposit'): ?>
                <p class="text-xs font-bold capitalize"><?php echo e(date('d F Y', strtotime($date_now))); ?>: SPD DEPOSIT</p>
                <?php else: ?>
                <p class="text-xs font-bold capitalize"><?php echo e(date('d F Y', strtotime($date_now))); ?>: Job
                    <?php echo e($job_type != 'invoice' ? __('order.'.$status) : 'Invoice'); ?></p>
                <?php endif; ?>
                <?php if($orders->count()): ?>
                <p class="text-xs font-light"><?php echo e($orders[0]->updated_at); ?></p>
                <?php endif; ?>
            </div>
            <?php if($job_type != 'marketing' && $job_type != 'invoice'): ?>
            <?php if($status == 'terkirim'): ?>
            <div class="w-1/6 <?php echo e($color); ?> flex justify-center items-center px-1 py-1.5"><button
                    @click="onClickFilterPaymentMethod('unpaid')" type="button"
                    :class="[filter_payment_method != '' && filter_payment_method != 'unpaid' ? 'opacity-40' : '', 'bg-white p-1 focus:outline-none shadow flex-col rounded flex justify-center items-center w-full h-full']">
                    <p class="text-xl leading-none">💔</p>
                    <p class="text-xs text-black leading-none mt-0.5 font-bold">UNPAID</p>
                </button></div>
            <?php endif; ?>
            <div class="w-1/6 <?php echo e($color); ?> flex justify-center items-center px-1 py-1.5"><button
                    @click="onClickFilterPaymentMethod('cash')" type="button"
                    :class="[filter_payment_method != '' && filter_payment_method != 'cash' ? 'opacity-40' : '', 'bg-white p-1 focus:outline-none shadow flex-col rounded flex justify-center items-center w-full h-full']">
                    
                    <img src="<?php echo e(asset('public/images/cash.png')); ?>" class="w-4/5" />
                    
                </button></div>
            <?php if($status == 'terkirim' || $status == 'total' || $status == '' ): ?>
            <div class="w-1/6 <?php echo e($color); ?> flex justify-center items-center px-1 py-1.5"><button
                    @click="onClickFilterPaymentMethod('non-tunai')" type="button"
                    :class="[filter_payment_method != '' && filter_payment_method != 'non-tunai' ? 'opacity-40' : '', 'bg-white p-1 focus:outline-none shadow flex-col rounded flex justify-center items-center w-full h-full']">
                    
                    <p class="text-xl leading-none">💳</p>
                    <p class="text-xs text-black leading-none mt-0.5 font-bold">Non-Tunai</p>
                </button></div>
            <?php else: ?>
            <div class="w-1/6 <?php echo e($color); ?> flex justify-center items-center px-1 py-1.5"><button
                    @click="onClickFilterPaymentMethod('transfer')" type="button"
                    :class="[filter_payment_method != '' && filter_payment_method != 'transfer' ? 'opacity-40' : '', 'bg-white p-1 focus:outline-none shadow flex-col rounded flex justify-center items-center w-full h-full']">
                    <img src="<?php echo e(asset('public/images/transfer.png')); ?>" class="w-4/5" />
                    
                    
                </button></div>
            <div class="w-1/6 <?php echo e($color); ?> flex justify-center items-center px-1 py-1.5"><button
                    @click="onClickFilterPaymentMethod('qris')" type="button"
                    :class="[filter_payment_method != '' && filter_payment_method != 'qris' ? 'opacity-40' : '', 'bg-white p-1 focus:outline-none shadow flex-col rounded flex justify-center items-center w-full h-full']">
                    <img src="<?php echo e(asset('public/images/qris.png')); ?>" class="w-4/5" />
                    
                    
                </button></div>
            <div class="w-1/6 <?php echo e($color); ?> flex justify-center items-center px-1 py-1.5"><button
                    @click="onClickFilterPaymentMethod('invoice')" type="button"
                    :class="[filter_payment_method != '' && filter_payment_method != 'invoice' ? 'opacity-40' : '', 'bg-white p-1 focus:outline-none shadow flex-col rounded flex justify-center items-center w-full h-full']">
                    
                    <p class="text-xl leading-none">🧾</p>
                    <p class="text-xs text-black leading-none mt-0.5 font-bold">INV</p>
                </button></div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="pb-20 pt-28">
        <div
            class="fixed top-0 left-0 right-0 z-auto flex items-center justify-center h-screen text-5xl font-bold text-gray-300 _js-loading">
            Loading...
        </div>
        <order-list invoices="<?php echo e($invoices->toJson()); ?>" marketings="<?php echo e($marketings->toJson()); ?>"
            orders="<?php echo e($orders->toJson()); ?>" :search="search" :job_type="'<?php echo e($job_type); ?>'" :status="'<?php echo e($status); ?>'"
            :filter_payment_method="filter_payment_method" :role_id="<?php echo e(auth()->user()->role_id); ?>"
            :user_id="<?php echo e(auth()->user()->id); ?>" username="<?php echo e(auth()->user()->name); ?>" base_url="<?php echo e(URL::to('/')); ?>"
            drivers="<?php echo e($drivers); ?>" date_now="<?php echo e($date_now); ?>">
        </order-list>
        
    </div>

    
    <div class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full px-3 py-4 bg-black bg-opacity-50 _js-modal-selesai"
        style="display: none;">
        <!-- modal -->
        <div class="flex flex-col w-full h-full max-w-md bg-white rounded shadow-lg">
            <!-- modal header -->
            <div class="flex items-center justify-between px-4 py-2 border-b">
                <h3 class="text-2xl font-bold">Selesai Job</h3>
                <button class="text-3xl font-bold text-red-600 _js-close-modal-selesai">&cross;</button>
            </div>
            <!-- modal body -->
            <form class="flex flex-col flex-1 p-3 overflow-y-auto _js-form-selesai-job">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="order_id" name="order_id" class="_js-input-order-id">
                <input type="hidden" id="received_latlng" name="received_latlng" class="_js-input-received-latlng">
                <input type="hidden" id="received_latlng_accuracy" name="received_latlng_accuracy"
                    class="_js-input-received-latlng-accuracy">
                <label class="block mt-1 mb-5" for="received_by">
                    <div class="font-bold text-gray-700">Nama Penerima <span class="text-red-600">*</span>
                    </div>
                    <input type="text" id="received_by" name="received_by"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-received-by focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                        value="" placeholder="">
                </label>
                <label class="block mb-5" for="driver_note">
                    <div class="font-bold text-gray-700">Catatan
                    </div>
                    <textarea id="driver_note" name="driver_note"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-driver-note focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                        rows="2"></textarea>
                </label>
                <div class="flex flex-col flex-1 pb-3 mb-1">
                    <div class="font-bold text-gray-700">Foto Penyelesaian</div>
                    <label
                        class="flex flex-col items-center justify-center flex-1 w-full px-4 py-6 mt-1 tracking-wide text-gray-400 uppercase bg-white border border-gray-300 rounded-lg shadow cursor-pointer _js-btn-photo">
                        <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z"
                                clip-rule="evenodd" />
                        </svg>
                        <span class="mt-2 text-base font-bold leading-normal">Foto</span>
                        <input type='file' accept="image/*" id="photo" name="photo" class="hidden _js-input-photo" />
                    </label>
                    <div class="relative flex flex-col flex-1 mt-1 _js-wrp-preview" style="display: none;">
                        <img class="object-cover h-auto border border-gray-300 rounded-lg shadow _js-image-preview"
                            src="/public/gasplus/img/stores/1/Toko Palagan.jpg" alt="">
                        <button type="button"
                            class="_js-btn-remove-photo flex justify-center items-center px-2 py-1.5 focus:outline-none rounded-md font-bold shadow-lg border-2 border-red-400 uppercase tracking-widest bg-red-600 text-white absolute top-2 right-2 text-xs">
                            <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                    clip-rule="evenodd" />
                            </svg> Hapus Foto</button>
                    </div>
                </div>
                <div class="flex flex-col flex-1 pb-3 mb-1">
                    <div class="font-bold text-gray-700">Bukti Pembayaran</div>
                    <label
                        class="flex flex-col items-center justify-center flex-1 w-full px-4 py-6 mt-1 tracking-wide text-gray-400 uppercase bg-white border border-gray-300 rounded-lg shadow cursor-pointer _js-btn-photo_paymentproof">
                        <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z"
                                clip-rule="evenodd" />
                        </svg>
                        <span class="mt-2 text-base font-bold leading-normal">Foto</span>
                        <input type='file' accept="image/*" id="photo_paymentproof" name="photo_paymentproof"
                            class="hidden _js-input-photo_paymentproof" />
                    </label>
                    <div class="relative flex flex-col flex-1 mt-1 _js-wrp-preview_paymentproof" style="display: none;">
                        <img class="object-cover h-auto border border-gray-300 rounded-lg shadow _js-image-preview_paymentproof"
                            src="/public/gasplus/img/stores/1/Toko Palagan.jpg" alt="">
                        <button type="button"
                            class="_js-btn-remove-photo_paymentproof flex justify-center items-center px-2 py-1.5 focus:outline-none rounded-md font-bold shadow-lg border-2 border-red-400 uppercase tracking-widest bg-red-600 text-white absolute top-2 right-2 text-xs">
                            <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                    clip-rule="evenodd" />
                            </svg> Hapus Foto</button>
                    </div>
                </div>
            </form>
            <div class="flex items-center justify-end p-3 border-t w-100">
                
                <button type="button"
                    class="w-full px-3 py-2 font-bold text-white bg-blue-700 rounded shadow-lg _js-btn-submit-selesai-job">SELESAIKAN
                    JOB</button>
            </div>
        </div>
    </div>

    
    <div class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full px-0 py-0 bg-black bg-opacity-50 _js-modal-checkin-marketing"
        style="display: none;">
        <!-- modal -->
        <div class="flex flex-col w-full h-full max-w-md bg-white rounded shadow-lg">
            <!-- modal header -->
            <div class="flex items-center justify-between px-4 py-2 border-b">
                <h3 class="text-2xl font-bold">Checkin Bagi Brosur</h3>
                <button class="text-3xl font-bold text-red-600 _js-close-modal-checkin-marketing">&cross;</button>
            </div>
            <!-- modal body -->
            <form class="flex flex-col flex-1 gap-3 p-3 _js-form-checkin-job-marketing">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="marketing_id" name="marketing_id" class="_js-input-marketing-id">
                <input type="hidden" id="received_latlng" name="received_latlng" class="_js-input-received-latlng">
                <input type="hidden" id="received_latlng_accuracy" name="received_latlng_accuracy"
                    class="_js-input-received-latlng-accuracy">
                <div class="relative flex items-center justify-center flex-1 overflow-hidden bg-black rounded-md">
                    <svg class="w-10 h-10 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    <div class="absolute inset-0 z-10 flex items-center">
                        <video id="webcam" autoplay playsinline class="w-full"></video>
                        <canvas id="canvas" class="hidden"></canvas>
                        <audio id="snapSound" src="/audio/demo_audio_snap.wav" preload="auto"></audio>
                    </div>
                    <div class="absolute left-0 right-0 z-20 bottom-20">
                        <div class="relative flex-shrink-0 _js-wrp-img-capture-default" style="display: none;">
                            <img src="" class="h-full _js-img-capture" />
                            <div
                                class="absolute inset-0 z-10 flex items-center justify-center bg-black bg-opacity-50 _js-wrp-loading">
                                <svg class="w-6 h-6 text-white _js-loading-process animate-spin"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4">
                                    </circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" style="display: none;"
                                    class="text-white _js-loading-done h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            
                        </div>
                        <div class="flex h-16 gap-4 pt-3 pr-3 overflow-x-auto _js-img-list scrollbar-hide">
                        </div>
                        
                    </div>
                    <button type="button"
                        class="absolute z-20 transform -translate-x-1/2 border-2 border-white rounded-full _js-btn-capture-camera group w-14 h-14 left-1/2 bottom-3">
                        
                    </button>
                    <button type="button"
                        class="absolute z-20 flex items-center justify-center bg-black bg-opacity-50 rounded-full _js-btn-flip-camera right-3 bottom-5 w-11 h-11">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-white" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                                clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <label class="block" for="driver_note">
                    <div class="font-bold text-gray-700">Catatan 
                    </div>
                    <textarea id="driver_note" name="driver_note"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-driver-note focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                        rows="2"></textarea>
                </label>
            </form>
            <div class="flex items-center justify-end p-3 border-t w-100">
                
                <button type="button"
                    class="w-full px-3 py-2 font-bold text-white bg-blue-700 rounded shadow-lg _js-btn-submit-checkin-job-marketing">SUBMIT
                    CHECKIN</button>
            </div>
        </div>
    </div>

    
    <div class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full px-3 py-4 bg-black bg-opacity-50 _js-modal-batal-marketing"
        style="display: none;">
        <!-- modal -->
        <div class="flex flex-col w-full h-full max-w-md bg-white rounded shadow-lg">
            <!-- modal header -->
            <div class="flex items-center justify-between px-4 py-2 border-b">
                <h3 class="text-2xl font-bold">Batal Bagi Brosur</h3>
                <button class="text-3xl font-bold text-red-600 _js-close-modal-batal-marketing">&cross;</button>
            </div>
            <!-- modal body -->
            <form class="flex flex-col flex-1 p-3 overflow-y-auto _js-form-batal-job-marketing">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="marketing_id" name="marketing_id" class="_js-input-marketing-id">
                <input type="hidden" id="received_latlng" name="received_latlng" class="_js-input-received-latlng">
                <input type="hidden" id="received_latlng_accuracy" name="received_latlng_accuracy"
                    class="_js-input-received-latlng-accuracy">
                <label class="block mb-5" for="driver_note">
                    <div class="font-bold text-gray-700">Alasan Pembatalan <span class="text-red-600">*</span>
                    </div>
                    <textarea id="driver_note" name="driver_note" required
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-driver-note focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                        rows="2"></textarea>
                </label>
            </form>
            <div class="flex items-center justify-end p-3 border-t w-100">
                
                <button type="button"
                    class="w-full px-3 py-2 font-bold text-white bg-red-500 rounded shadow-lg _js-btn-submit-batal-job-marketing">BATALKAN
                    JOB</button>
            </div>
        </div>
    </div>

    
    <div class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full px-3 py-4 bg-black bg-opacity-50 _js-modal-batal"
        style="display: none;">
        <!-- modal -->
        <div class="flex flex-col w-full h-full max-w-md bg-white rounded shadow-lg">
            <!-- modal header -->
            <div class="flex items-center justify-between px-4 py-2 border-b">
                <h3 class="text-2xl font-bold">Batal Job</h3>
                <button class="text-3xl font-bold text-red-600 _js-close-modal-batal">&cross;</button>
            </div>
            <!-- modal body -->
            <form class="flex flex-col flex-1 p-3 overflow-y-auto _js-form-batal-job">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="order_id" name="order_id" class="_js-input-order-id">
                <input type="hidden" id="received_latlng" name="received_latlng" class="_js-input-received-latlng">
                <input type="hidden" id="received_latlng_accuracy" name="received_latlng_accuracy"
                    class="_js-input-received-latlng-accuracy">
                <label class="block mb-5" for="driver_note">
                    <div class="font-bold text-gray-700">Alasan Pembatalan <span class="text-red-600">*</span>
                    </div>
                    <textarea id="driver_note" name="driver_note" required
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-driver-note focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                        rows="2"></textarea>
                </label>
            </form>
            <div class="flex items-center justify-end p-3 border-t w-100">
                
                <button type="button"
                    class="w-full px-3 py-2 font-bold text-white bg-red-500 rounded shadow-lg _js-btn-submit-batal-job">BATALKAN
                    JOB</button>
            </div>
        </div>
    </div>

    
    <div class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full px-3 py-4 bg-black bg-opacity-50 _js-modal-ganti-tanggal"
        style="display: none;">
        <!-- modal -->
        <div class="flex flex-col w-full h-full max-w-md bg-white rounded shadow-lg">
            <!-- modal header -->
            <div class="flex items-center justify-between px-4 py-2 border-b">
                <h3 class="text-2xl font-bold">Ganti Tanggal</h3>
                <button class="text-3xl font-bold text-red-600 _js-close-modal-ganti-tanggal">&cross;</button>
            </div>
            <!-- modal body -->
            <form class="flex flex-col flex-1 p-3 overflow-y-auto _js-form-ganti-tanggal">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="order_id" name="order_id" class="_js-input-order-id">
                <label class="block mb-5" for="driver_note">
                    <div class="font-bold text-gray-700">Tanggal <span class="text-red-600">*</span>
                    </div>
                    <?php
                    $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
                    $prev_date = date('Y-m-d\TH:i', strtotime($date .' -1 day'));
                    $next_date = date('Y-m-d\TH:i', strtotime($date .' +1 day'));
                    ?>
                    <input id="created_at" data-next_date="<?php echo e($next_date); ?>" name="created_at" type="datetime-local"
                        required="required" min="<?php echo e($next_date); ?>" value=""
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-created_at focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                </label>
            </form>
            <div class="flex items-center justify-end p-3 border-t w-100">
                <button type="button"
                    class="w-full px-3 py-2 font-bold text-white bg-indigo-500 rounded shadow-lg _js-btn-submit-ganti-tanggal">GANTI
                    TANGGAL</button>
            </div>
        </div>
    </div>

    
    <div class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full px-3 py-4 bg-black bg-opacity-50 _js-modal-upload-paymentproof"
        style="display: none;">
        <!-- modal -->
        <div class="flex flex-col w-full h-full max-w-md bg-white rounded shadow-lg">
            <!-- modal header -->
            <div class="flex items-center justify-between px-4 py-2 border-b">
                <h3 class="text-2xl font-bold">Upload Bukti Pembayaran</h3>
                <button class="text-3xl font-bold text-red-600 _js-close-modal-upload-paymentproof">&cross;</button>
            </div>
            <!-- modal body -->
            <form class="flex flex-col flex-1 p-3 overflow-y-auto _js-form-upload-paymentproof">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="order_type" name="order_type" class="_js-input-order-type">
                <input type="hidden" id="order_id" name="order_id" class="_js-input-order-id">
                <div class="flex flex-col flex-1 pb-3 mb-1">
                    
                    <label
                        class="flex flex-col items-center justify-center flex-1 w-full px-4 py-6 mt-1 tracking-wide text-gray-400 uppercase bg-white border border-gray-300 rounded-lg shadow cursor-pointer _js-btn-photo_paymentproof">
                        <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z"
                                clip-rule="evenodd" />
                        </svg>
                        <span class="mt-2 text-base font-bold leading-normal">Foto</span>
                        <input type='file' accept="image/*" id="photo_paymentproof" name="photo_paymentproof"
                            class="hidden _js-input-photo_paymentproof" />
                    </label>
                    <div class="relative flex flex-col flex-1 mt-1 _js-wrp-preview_paymentproof" style="display: none;">
                        <img class="object-cover h-auto border border-gray-300 rounded-lg shadow _js-image-preview_paymentproof"
                            src="/public/gasplus/img/stores/1/Toko Palagan.jpg" alt="">
                        <button type="button"
                            class="_js-btn-remove-photo_paymentproof flex justify-center items-center px-2 py-1.5 focus:outline-none rounded-md font-bold shadow-lg border-2 border-red-400 uppercase tracking-widest bg-red-600 text-white absolute top-2 right-2 text-xs">
                            <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                    clip-rule="evenodd" />
                            </svg> Hapus Foto</button>
                    </div>
                </div>
            </form>
            <div class="flex items-center justify-end p-3 border-t w-100">
                <button type="button"
                    class="w-full px-3 py-2 font-bold text-white bg-blue-700 rounded shadow-lg _js-btn-submit-upload-paymentproof">SUBMIT</button>
            </div>
        </div>
    </div>

    
    <div class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full px-3 py-4 bg-black bg-opacity-50 _js-modal-upload-address"
        style="display: none;">
        <!-- modal -->
        <div class="flex flex-col w-full h-full max-w-md bg-white rounded shadow-lg">
            <!-- modal header -->
            <div class="flex items-center justify-between px-4 py-2 border-b">
                <h3 class="text-2xl font-bold">Upload Foto Rumah</h3>
                <button class="text-3xl font-bold text-red-600 _js-close-modal-upload-address">&cross;</button>
            </div>
            <!-- modal body -->
            <form class="flex flex-col flex-1 gap-3 p-3 _js-form-upload-address">
                <?php echo csrf_field(); ?>
                
                <input type="hidden" id="order_id" name="order_id" class="_js-input-order-id-upload-address">
                <div class="relative flex items-center justify-center flex-1 overflow-hidden bg-black rounded-md">
                    <svg class="w-10 h-10 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    <div class="absolute inset-0 z-10 flex items-center">
                        <video id="webcam-rumah" autoplay playsinline class="w-full"></video>
                        <canvas id="canvas-rumah" class="hidden"></canvas>
                    </div>
                    <img src="https://www.manowargolf.com/images/galleries/gallery/2.jpg"
                        class="absolute inset-0 z-20 object-contain h-full _js-img-capture-rumah"
                        style="display: none;" />
                    <button type="button" style="display: none;"
                        class="_js-btn-remove-photo_address flex justify-center items-center px-2 py-1.5 focus:outline-none rounded-md font-bold shadow-lg border-2 border-red-400 uppercase tracking-widest bg-red-600 text-white absolute z-30 top-2 right-2 text-xs">
                        <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                clip-rule="evenodd" />
                        </svg> Hapus Foto</button>
                    <button type="button"
                        class="absolute z-20 transform -translate-x-1/2 border-2 border-white rounded-full _js-btn-capture-camera-rumah group w-14 h-14 left-1/2 bottom-3">
                        
                    </button>
                    <button type="button"
                        class="absolute z-20 flex items-center justify-center bg-black bg-opacity-50 rounded-full _js-btn-flip-camera-rumah right-3 bottom-5 w-11 h-11">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-white" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                                clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </form>
            
            <div class="flex items-center justify-end p-3 border-t w-100">
                <button type="button"
                    class="w-full px-3 py-2 font-bold text-white bg-blue-700 rounded shadow-lg _js-btn-submit-upload-address">SUBMIT</button>
            </div>
        </div>
    </div>

    <template v-if="mapIsShow">
        <div class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full bg-black bg-opacity-50">
            <!-- modal -->
            <div class="flex flex-col w-full h-full bg-white shadow-lg">
                <!-- modal header -->
                <div class="flex items-center justify-between px-4 py-2 border-b">
                    <h3 class="text-2xl font-bold">Map Job List</h3>
                    <button class="text-3xl font-bold text-red-600" @click="onCloseMap">&cross;</button>
                </div>
                <div id="map" style="width: 100%; height: 100%;"></div>
                
            </div>
        </div>
    </template>

     <?php $__env->slot('js', null, []); ?> 
        <script type="module">
            function removeURLParameter(url, parameter) {
                //prefer to use l.search if you have a location/link object
                var urlparts = url.split('?');   
                if (urlparts.length >= 2) {

                    var prefix = encodeURIComponent(parameter) + '=';
                    var pars = urlparts[1].split(/[&;]/g);

                    //reverse iteration as may be destructive
                    for (var i = pars.length; i-- > 0;) {    
                        //idiom for string.startsWith
                        if (pars[i].lastIndexOf(prefix, 0) !== -1) {  
                            pars.splice(i, 1);
                        }
                    }

                    return urlparts[0] + (pars.length > 0 ? '?' + pars.join('&') : '');
                }
                return url;
            }
            // import {OrderItem} from '../../js/components/OrderItem.vue';
            // const app = new Vue({
            //     el: '#app',
            //     data: {
            //         searchIsShow: false,
            //         search: '',
            //     },
            //     components: {
            //         'order-item': OrderItem,
            //     },
            //     methods: {
            //         onOpenSearch: function () {
            //             console.log(JSON.parse(this.orders));
            //             this.searchIsShow = true;
            //             setTimeout(() => {
            //                 $('._js-input-search').focus()
            //             }, 1);
            //         },
            //         onCloseSearch: function () {
            //             this.searchIsShow = false;
            //             this.search = '';
            //             setTimeout(() => {
            //                 $('._js-input-search').blur();
            //             }, 1);
            //         }
            //     }
            // });

            let latlng = null;
            let latlng_accuracy = null;
            let runGetLocation;
            function success(pos) {
                var crd = pos.coords;
                // console.log("Your current position is:");
                // console.log(`Latitude : ${crd.latitude}`);
                // console.log(`Longitude: ${crd.longitude}`);
                // console.log(`More or less ${crd.accuracy} meters.`);
                latlng = crd.latitude+','+crd.longitude;
                latlng_accuracy = crd.accuracy;
            }
            function error(err) {
                console.warn("ERROR(" + err.code + "): " + err.message);
                clearInterval(runGetLocation);
            }
            const options = {
                enableHighAccuracy: true,
                timeout: 5000,
                maximumAge: 0
            };
            function getLocation() {
                navigator.geolocation.getCurrentPosition(
                    success,
                    error,
                    options
                );
            }

            function base64ToBlob(base64, mime) 
            {
                mime = mime || '';
                var sliceSize = 1024;
                var byteChars = window.atob(base64);
                var byteArrays = [];

                for (var offset = 0, len = byteChars.length; offset < len; offset += sliceSize) {
                    var slice = byteChars.slice(offset, offset + sliceSize);

                    var byteNumbers = new Array(slice.length);
                    for (var i = 0; i < slice.length; i++) {
                        byteNumbers[i] = slice.charCodeAt(i);
                    }

                    var byteArray = new Uint8Array(byteNumbers);

                    byteArrays.push(byteArray);
                }

                return new Blob(byteArrays, {type: mime});
            }

            $( document ).ready(function() {

                $('._js-btn-resync-job-acc').on('click', function (){
                    if (confirm('Yakin ingin resync semua job tanggal ini di Accurate?')) {
                        console.log('<?php echo e(route("jobs.force-resync.acc", ["store_slug" => $store_slug, "date" => request()->query("date") ?? date("Y-m-d") ])); ?>');
                        axios.post('<?php echo e(route("jobs.force-resync.acc", ["store_slug" => $store_slug, "date" => request()->query("date") ?? date("Y-m-d") ])); ?>')
                        .then(function (res) {
                            console.log("🚀 ~ res:", res)
                        });
                    }
                });

                <?php
                function Get($index, $defaultValue = '') {
                    return isset($_GET[$index]) ? $_GET[$index] : $defaultValue;
                }
                ?>
                const msg = `<?php echo urldecode(Get("msg")) ?>`;
                const to = '<?php echo Get("to") ?>';
                const alert = '<?php echo Get("alert") ?>';
                const alertmsg = '<?php echo Get("alertmsg") ?>';
                // console.log("🚀 ~ to:", to)
                // console.log("🚀 ~ msg:", msg)
                if (alert === 'notif-sent') {
                    Swal.fire({
                        title: "Notif Job TERKIRIM",
                        text: alertmsg,
                        icon: "success",
                        confirmButtonColor: "#3085d6",
                        // confirmButtonText: "Tutup",
                    });
                }
                if (alert === 'notif-confirm' && msg && to) {
                    // console.log("🚀 ~ msg:", msg)
                    // console.log("🚀 ~ to:", to)
                    Swal.fire({
                        title: "Kirim Notif Job?",
                        text: "Silahkan pilih aksi...",
                        icon: "warning",
                        showDenyButton: true,
                        showCancelButton: true,
                        confirmButtonColor: "#3085d6",
                        denyButtonColor: "#25D366",
                        confirmButtonText: 'Copy Text Notif Job',
                        denyButtonText: 'Kirim via WhatsApp',
                        cancelButtonText: 'Tutup',
                    }).then((result) => {
                        if (result.isConfirmed) {
                            navigator.clipboard.writeText(msg.replaceAll('<br>', '\n'));
                            Swal.fire({
                                title: "Text notif job COPIED!",
                                text: 'Silahkan "PASTE" text di Socialchat/WhatsApp.',
                                icon: "success",
                                confirmButtonColor: "#3085d6",
                                // confirmButtonText: "Tutup",
                            });
                        } else if (result.isDenied) {
                            const url = `https://wa.me/${to}?text=${encodeURIComponent(msg.replaceAll('<br>', '\n'))}`;
                            window.open(url, '_blank').focus();
                        }
                    });
                }

                const webcamElement = document.getElementById('webcam');
                const canvasElement = document.getElementById('canvas');
                const snapSoundElement = document.getElementById('snapSound');
                const webcam = new Webcam(webcamElement, 'user', canvasElement, snapSoundElement);

                const webcamElementRumah = document.getElementById('webcam-rumah');
                const canvasElementRumah = document.getElementById('canvas-rumah');
                const webcamRumah = new Webcam(webcamElementRumah, 'user', canvasElementRumah, snapSoundElement);
                // webcamRumah.start()
                //     .then(result =>{
                //         console.log("webcamRumah started");
                //         webcamRumah.flip();
                //         webcamRumah.start();
                //     })
                //     .catch(err => {
                //         console.log(err);
                //     });

                setTimeout(() => {
                    $('._js-loading').fadeOut('fast');
                }, 500);

                <?php if(in_array(auth()->user()->role_id, [5, 6])): ?>

                // webcam.start()
                //     .then(result =>{
                //         console.log("webcam started");
                //         webcam.flip();
                //         webcam.start();
                //     })
                //     .catch(err => {
                //         console.log(err);
                //     });

                document.addEventListener('visibilitychange', (event) => {
                    if (document.hidden) {
                        console.log('not visible');
                        webcam.stop();

                    } else {
                        console.log('is visible');
                        webcam.start()
                            .then(result =>{
                                console.log("webcam started");
                                webcam.flip();
                                webcam.start();
                            })
                            .catch(err => {
                                console.log(err);
                            });
                    }
                });

                <?php endif; ?>

                // Job Assign MARKETER
                // ====================================================
                const defaultValDriverMarketing = {};
                $('._js-input-driver-marketing').each(function() {
                    const marketingId = $(this).data('marketing_id');
                    const driverId = this.value;
                    defaultValDriverMarketing[marketingId] = driverId;
                });
                $('body').on('change', '._js-input-driver-marketing', function() {
                    const $this = $(this);
                    const marketingId = $this.data('marketing_id');
                    const driverId = this.value;
                    mdtoast(
                    'LOADING...', {
                        // type: mdtoast.INFO,
                        interaction: true, 
                        actionText: 'CLOSE',
                        action: function(){ this.hide(); }
                    });
                    axios.post("<?php echo e(route('marketing-set-driver')); ?>", {
                        // params: {
                            marketing_id: marketingId,
                            driver_id: driverId,
                        // }
                    })
                    .then(function (res) {
                        console.log('res', res);
                        if (res && res.data) {
                            mdtoast('SET DRIVER BERHASIL', { duration: 1500, type: mdtoast.SUCCESS });
                            $('._js-marketing-item-'+marketingId).addClass('animate__bounceOut');
                            setTimeout(() => {
                                $('._js-marketing-item-'+marketingId).remove();
                            }, 1000);
                        } else {
                            mdtoast('SET DRIVER GAGAL', { duration: 1500, type: mdtoast.ERROR });
                            $this.val(defaultValDriverMarketing[marketingId]);
                        }
                    })
                    .catch(function (error) {
                        console.log('error', error);
                        mdtoast('SET DRIVER GAGAL', { duration: 1500, type: mdtoast.ERROR });
                        $this.val(defaultValDriverMarketing[marketingId]);
                    });
                });

                // Job Assign ORDER
                // ====================================================
                const defaultValDriver = {};
                $('._js-input-driver').each(function() {
                    const orderId = $(this).data('order_id');
                    const driverId = this.value;
                    defaultValDriver[orderId] = driverId;
                });
                $('body').on('change', '._js-input-driver', function() {
                    const $this = $(this);
                    const orderId = $this.data('order_id');
                    const driverId = this.value;
                    const amountReturn = $('._js-input-return-'+orderId).val() ? $('._js-input-return-'+orderId).val().replace('Rp','').replaceAll('.','') : null;
                    mdtoast(
                    'LOADING...', {
                        // type: mdtoast.INFO,
                        interaction: true, 
                        actionText: 'CLOSE',
                        action: function(){ this.hide(); }
                    });
                    axios.post("<?php echo e(route('order-set-driver')); ?>", {
                        order_id: orderId,
                        driver_id: driverId,
                        amount_return: amountReturn,
                    })
                    .then(function (res) {
                        console.log('res', res);
                        if (res && res.data) {
                            mdtoast('SET DRIVER BERHASIL', { duration: 1500, type: mdtoast.SUCCESS });
                            $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                            setTimeout(() => {
                                $('._js-order-item-'+orderId).remove();
                            }, 1000);
                        } else {
                            mdtoast('SET DRIVER GAGAL', { duration: 1500, type: mdtoast.ERROR });
                            $this.val(defaultValDriver[orderId]);
                        }
                    })
                    .catch(function (error) {
                        console.log('error', error);
                        mdtoast('SET DRIVER GAGAL', { duration: 1500, type: mdtoast.ERROR });
                        $this.val(defaultValDriver[orderId]);
                    });
                });

                // Job Selsai
                // ====================================================
                const $modalSelesai = $('._js-modal-selesai');

                const $btnCloseModalSelesai = $('._js-close-modal-selesai');

                $('body').on('click', '._js-btn-selesai', function() {
                    $modalSelesai.fadeIn('fast');
                    const $this = $(this);
                    const orderId = $this.data('order_id');
                    $('._js-input-order-id').val(orderId);
                    if (navigator.geolocation) {
                        runGetLocation = setInterval(getLocation, 1000);
                    } else {
                        // x.innerHTML = "Geolocation is not supported by this browser.";
                        console.log("Geolocation is not supported by this browser.");
                        clearInterval(runGetLocation);
                    }

                });
                $btnCloseModalSelesai.on('click', function (){
                    $modalSelesai.fadeOut('fast');
                    $('._js-input-order-id').val('');
                    $('._js-input-received-by').val('');
                    $('._js-input-driver-note').val('');
                    $('._js-btn-photo').show();
                    $('._js-btn-photo_paymentproof').show();
                    $("._js-image-preview").attr("src", '');
                    $("._js-image-preview_paymentproof").attr("src", '');
                    $('._js-input-photo').val('');
                    $('._js-input-photo_paymentproof').val('');
                    $('._js-wrp-preview').hide();
                    $('._js-wrp-preview_paymentproof').hide();
                    clearInterval(runGetLocation);
                });

                $('._js-input-photo').on('change', function() {
                    if (this.files && this.files[0]) {
                    // if (this.files[0].size > 3 * 1024 * 1024) {
                    //     // Check the constraint
                    //     this.setCustomValidity(
                    //     "The selected file must not be larger than 3 MB"
                    //     );
                    // } else {
                    //     this.setCustomValidity("");
                    // }
                        var reader = new FileReader();
                        reader.readAsDataURL(this.files[0]);
                        reader.onload = function (e) {
                            $("._js-image-preview").attr("src", e.target.result);
                            $('._js-btn-photo').hide();
                            $('._js-wrp-preview').show();
                        };
                    }
                });
                $('._js-input-photo_paymentproof').on('change', function() {
                    if (this.files && this.files[0]) {
                    // if (this.files[0].size > 3 * 1024 * 1024) {
                    //     // Check the constraint
                    //     this.setCustomValidity(
                    //     "The selected file must not be larger than 3 MB"
                    //     );
                    // } else {
                    //     this.setCustomValidity("");
                    // }
                        var reader = new FileReader();
                        reader.readAsDataURL(this.files[0]);
                        reader.onload = function (e) {
                            $("._js-image-preview_paymentproof").attr("src", e.target.result);
                            $('._js-btn-photo_paymentproof').hide();
                            $('._js-wrp-preview_paymentproof').show();
                        };
                    }
                });

                $('._js-btn-remove-photo').on('click', function() {
                    $('._js-btn-photo').show();
                    $("._js-image-preview").attr("src", '');
                    $('._js-input-photo').val('');
                    $('._js-wrp-preview').hide();
                });
                $('._js-btn-remove-photo_paymentproof').on('click', function() {
                    $('._js-btn-photo_paymentproof').show();
                    $("._js-image-preview_paymentproof").attr("src", '');
                    $('._js-input-photo_paymentproof').val('');
                    $('._js-wrp-preview_paymentproof').hide();
                });

                $('._js-btn-submit-selesai-job').on('click', function() {
                    $('._js-input-received-latlng').val(latlng);
                    $('._js-input-received-latlng-accuracy').val(latlng_accuracy);
                    const $form = $('._js-form-selesai-job');
                    const inputs = new FormData($form[0]);
                    const orderId = inputs.get('order_id');
                    const paymentNote = $('._js-input-payment-note-'+orderId).val();
                    const paymentMethodAsk = $('._js-input-payment-method-ask-'+orderId).val();
                    inputs.append('payment_method_ask', paymentMethodAsk);
                    inputs.append('payment_note', paymentNote ? paymentNote : "");
                    if (paymentMethodAsk == 'non-cash') {
                        inputs.append('amount_split_to_cash', $('._js-input-split-to-cash-'+orderId).val() ? $('._js-input-split-to-cash-'+orderId).val().replace('Rp','').replaceAll('.','') : null);
                    }

                    if (!inputs.get('received_by')) {
                        mdtoast('"Nama Penerima" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    // } else if (!inputs.get('photo').name) {
                    //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    } else {
                        if (confirm('Selesaikan job?')) {
                            mdtoast(
                                'LOADING...', {
                                    // type: mdtoast.INFO,
                                    interaction: true, 
                                    actionText: 'CLOSE',
                                    action: function(){ this.hide(); }
                                });
                            $.ajax({
                                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                                url: "<?php echo e(route('submit-finish-job')); ?>",
                                type: 'POST',
                                data: inputs,
                                processData: false,
                                contentType: false,
                                success: function(res) {
                                    console.log('res', res);
                                    console.log('orderId', orderId);
                                    if (res) {
                                        $modalSelesai.fadeOut('fast');
                                        $('._js-input-order-id').val('');
                                        $('._js-input-received-by').val('');
                                        $('._js-input-driver-note').val('');
                                        $('._js-btn-photo').show();
                                        $('._js-btn-photo_paymentproof').show();
                                        $("._js-image-preview").attr("src", '');
                                        $("._js-image-preview_paymentproof").attr("src", '');
                                        $('._js-input-photo').val('');
                                        $('._js-input-photo_paymentproof').val('');
                                        $('._js-wrp-preview').hide();
                                        $('._js-wrp-preview_paymentproof').hide();
                                        $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                                        setTimeout(() => {
                                            $('._js-order-item-'+orderId).remove();
                                            mdtoast('JOB BERHASIL DISELESAIKAN', { duration: 3000, type: mdtoast.SUCCESS });
                                        }, 1000);
                                    } else {
                                        mdtoast('SET JOB SELESAI GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                        $this.val(defaultValDriver[orderId]);
                                    }
                                },
                                error: function(error) {
                                    console.log('error', error);
                                    mdtoast('SET JOB SELESAI GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                },
                            });
                        }
                    }
                });

                // Job Checkin MARKETING
                // ====================================================
                const $modalCheckinMarketing = $('._js-modal-checkin-marketing');
                const $btnCloseModalCheckinMarketing = $('._js-close-modal-checkin-marketing');
                $('body').on('click', '._js-btn-checkin-marketing', function() {
                    $modalCheckinMarketing.fadeIn('fast');
                    const $this = $(this);
                    const marketingId = $this.data('marketing_id');
                    $('._js-input-marketing-id').val(marketingId);
                    if (navigator.geolocation) {
                        runGetLocation = setInterval(getLocation, 1000);
                    } else {
                        // x.innerHTML = "Geolocation is not supported by this browser.";
                        console.log("Geolocation is not supported by this browser.");
                        clearInterval(runGetLocation);
                    }
                    webcam.start()
                        .then(result =>{
                            console.log("webcam started");
                            webcam.flip();
                            webcam.start();
                        })
                        .catch(err => {
                            console.log(err);
                        });
                });
                $('._js-btn-flip-camera').on('click', function() {
                    webcam.flip();
                    webcam.start();
                });
                $('._js-btn-capture-camera').on('click', function() {
                    const localId = Math.floor(Math.random() * 100) + 1;
                    let photo = webcam.snap();
                    $('._js-wrp-img-capture-default')
                        .find('._js-img-capture')
                        .prop("src",photo)
                        .prop("id", 'capture_'+localId);
                    $('._js-wrp-img-capture-default')
                        .clone()
                        .removeClass('_js-wrp-img-capture-default')
                        .addClass('_js-wrp-img-capture')
                        .prop('id', 'img_capture_'+localId)
                        .show()
                        .appendTo('._js-img-list');
                    mediumZoom('#capture_'+localId);

                    const base64ImageContent = photo.replace(/^data:image\/(png|jpg);base64,/, "");
                    const blob = base64ToBlob(base64ImageContent, 'image/png');
                    const formData = new FormData();
                    formData.append('marketing_id', $('._js-input-marketing-id').val());
                    formData.append('photo_marketing', blob);
                    $.ajax({
                        headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                        url: "<?php echo e(route('upload-photo-marketing')); ?>",
                        type: 'POST',
                        cache: false,
                        contentType: false,
                        processData: false,
                        data: formData,
                        success: function(res) {
                            console.log('res', res);
                            $('#img_capture_'+localId).find('._js-loading-process').hide();
                            $('#img_capture_'+localId).find('._js-loading-done').show();
                            setTimeout(() => {
                                $('#img_capture_'+localId).find('._js-wrp-loading').hide();
                                // $('#img_capture_'+localId).find('._js-btn-delete-photo').prop('id', res.id).show();
                            }, 500);
                        },
                        error: function(error) {
                            console.log('error', error);
                        },
                    });
                });
                // $('._js-img-list').on('click', '._js-btn-delete-photo', function() {
                //     if (confirm('Hapus foto?')) {
                //         $(this).parents('._js-wrp-img-capture').remove();
                //     }
                // });
                $btnCloseModalCheckinMarketing.on('click', function (){
                    // if ($('._js-img-list').children().length > 0 && confirm('Hapus semua foto?')) {
                        $modalCheckinMarketing.fadeOut('fast');
                        $('._js-input-marketing-id').val('');
                        $('._js-input-driver-note').val('');
                        clearInterval(runGetLocation);
                        // webcam.stop();
                        $('._js-img-list').empty();
                    // } else {
                    //     $modalCheckinMarketing.fadeOut('fast');
                    //     $('._js-input-marketing-id').val('');
                    //     $('._js-input-driver-note').val('');
                    //     clearInterval(runGetLocation);
                    //     webcam.stop();
                    // }
                    webcam.stop();
                });
                $('._js-btn-submit-checkin-job-marketing').on('click', function() {
                    $('._js-input-received-latlng').val(latlng);
                    $('._js-input-received-latlng-accuracy').val(latlng_accuracy);
                    const $form = $('._js-form-checkin-job-marketing');
                    const inputs = new FormData($form[0]);
                    const marketingId = inputs.get('marketing_id');
                    console.log('inputs', inputs.values());

                    // if (!inputs.get('driver_note')) {
                    //     mdtoast('"Alasan Pembatalan" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    // // } else if (!inputs.get('photo').name) {
                    // //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    // } else {
                        if (confirm('Submit checkin?')) {
                            mdtoast(
                                'LOADING...', {
                                    // type: mdtoast.INFO,
                                    interaction: true, 
                                    actionText: 'CLOSE',
                                    action: function(){ this.hide(); }
                                });
                            $.ajax({
                                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                                url: "<?php echo e(route('submit-checkin-job-marketing')); ?>",
                                type: 'POST',
                                // type: 'GET',
                                data: inputs,
                                processData: false,
                                contentType: false,
                                success: function(res) {
                                    console.log('res', res);
                                    console.log('marketingId', marketingId);
                                    if (res) {
                                        $modalCheckinMarketing.fadeOut('fast');
                                        $('._js-input-marketing-id').val('');
                                        $('._js-input-driver-note').val('');
                                        $('._js-marketing-item-'+marketingId).addClass('animate__bounceOut');
                                        setTimeout(() => {
                                            $('._js-marketing-item-'+marketingId).remove();
                                            mdtoast('CHECKIN BERHASIL', { duration: 3000, type: mdtoast.SUCCESS });
                                        }, 1000);
                                    } else {
                                        mdtoast('CHECKIN GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                        $this.val(defaultValDriver[marketingId]);
                                    }
                                },
                                error: function(error) {
                                    console.log('error', error);
                                    mdtoast('CHECKIN GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                },
                            });
                        // }
                    }
                });

                // Job Batal MARKETING
                // ====================================================
                const $modalBatalMarketing = $('._js-modal-batal-marketing');
                const $btnCloseModalBatalMarketing = $('._js-close-modal-batal-marketing');
                $('body').on('click', '._js-btn-batal-marketing', function() {
                    $modalBatalMarketing.fadeIn('fast');
                    const $this = $(this);
                    const marketingId = $this.data('marketing_id');
                    $('._js-input-marketing-id').val(marketingId);
                    if (navigator.geolocation) {
                        runGetLocation = setInterval(getLocation, 1000);
                    } else {
                        // x.innerHTML = "Geolocation is not supported by this browser.";
                        console.log("Geolocation is not supported by this browser.");
                        clearInterval(runGetLocation);
                    }

                });
                $btnCloseModalBatalMarketing.on('click', function (){
                    $modalBatalMarketing.fadeOut('fast');
                    $('._js-input-marketing-id').val('');
                    $('._js-input-driver-note').val('');
                    clearInterval(runGetLocation);
                });
                $('._js-btn-submit-batal-job-marketing').on('click', function() {
                    $('._js-input-received-latlng').val(latlng);
                    $('._js-input-received-latlng-accuracy').val(latlng_accuracy);
                    const $form = $('._js-form-batal-job-marketing');
                    const inputs = new FormData($form[0]);
                    const marketingId = inputs.get('marketing_id');
                    console.log('inputs', inputs.values());

                    if (!inputs.get('driver_note')) {
                        mdtoast('"Alasan Pembatalan" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    // } else if (!inputs.get('photo').name) {
                    //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    } else {
                        if (confirm('Batalkan job?')) {
                            mdtoast(
                                'LOADING...', {
                                    // type: mdtoast.INFO,
                                    interaction: true, 
                                    actionText: 'CLOSE',
                                    action: function(){ this.hide(); }
                                });
                            $.ajax({
                                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                                url: "<?php echo e(route('submit-cancel-job-marketing')); ?>",
                                type: 'POST',
                                // type: 'GET',
                                data: inputs,
                                processData: false,
                                contentType: false,
                                success: function(res) {
                                    console.log('res', res);
                                    console.log('marketingId', marketingId);
                                    if (res) {
                                        $modalBatalMarketing.fadeOut('fast');
                                        $('._js-input-marketing-id').val('');
                                        $('._js-input-driver-note').val('');
                                        $('._js-marketing-item-'+marketingId).addClass('animate__bounceOut');
                                        setTimeout(() => {
                                            $('._js-marketing-item-'+marketingId).remove();
                                            mdtoast('JOB BERHASIL DIBATALKAN', { duration: 3000, type: mdtoast.SUCCESS });
                                        }, 1000);
                                    } else {
                                        mdtoast('SET JOB "BATAL" GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                        $this.val(defaultValDriver[marketingId]);
                                    }
                                },
                                error: function(error) {
                                    console.log('error', error);
                                    mdtoast('SET JOB "BATAL" GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                },
                            });
                        }
                    }
                });


                // Job Batal ORDER
                // ====================================================
                const $modalBatal = $('._js-modal-batal');
                const $btnCloseModalBatal = $('._js-close-modal-batal');
                $('body').on('click', '._js-btn-batal', function() {
                    $modalBatal.fadeIn('fast');
                    const $this = $(this);
                    const orderId = $this.data('order_id');
                    $('._js-input-order-id').val(orderId);
                    if (navigator.geolocation) {
                        runGetLocation = setInterval(getLocation, 1000);
                    } else {
                        // x.innerHTML = "Geolocation is not supported by this browser.";
                        console.log("Geolocation is not supported by this browser.");
                        clearInterval(runGetLocation);
                    }

                });
                $btnCloseModalBatal.on('click', function (){
                    $modalBatal.fadeOut('fast');
                    $('._js-input-order-id').val('');
                    $('._js-input-driver-note').val('');
                    clearInterval(runGetLocation);
                });
                $('._js-btn-submit-batal-job').on('click', function() {
                    $('._js-input-received-latlng').val(latlng);
                    $('._js-input-received-latlng-accuracy').val(latlng_accuracy);
                    const $form = $('._js-form-batal-job');
                    const inputs = new FormData($form[0]);
                    const orderId = inputs.get('order_id');

                    if (!inputs.get('driver_note')) {
                        mdtoast('"Alasan Pembatalan" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    // } else if (!inputs.get('photo').name) {
                    //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    } else {
                        if (confirm('Batalkan job?')) {
                            mdtoast(
                                'LOADING...', {
                                    // type: mdtoast.INFO,
                                    interaction: true, 
                                    actionText: 'CLOSE',
                                    action: function(){ this.hide(); }
                                });
                            $.ajax({
                                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                                url: "<?php echo e(route('submit-cancel-job')); ?>",
                                type: 'POST',
                                data: inputs,
                                processData: false,
                                contentType: false,
                                success: function(res) {
                                    console.log('res', res);
                                    console.log('orderId', orderId);
                                    if (res) {
                                        $modalBatal.fadeOut('fast');
                                        $('._js-input-order-id').val('');
                                        $('._js-input-driver-note').val('');
                                        $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                                        setTimeout(() => {
                                            $('._js-order-item-'+orderId).remove();
                                            mdtoast('JOB BERHASIL DIBATALKAN', { duration: 3000, type: mdtoast.SUCCESS });
                                        }, 1000);
                                    } else {
                                        mdtoast('SET JOB "BATAL" GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                        $this.val(defaultValDriver[orderId]);
                                    }
                                },
                                error: function(error) {
                                    console.log('error', error);
                                    mdtoast('SET JOB "BATAL" GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                },
                            });
                        }
                    }
                });

                // Ganti Tanggal ORDER
                // ====================================================
                const $modalGantiTanggal = $('._js-modal-ganti-tanggal');
                const $btnCloseModalGantiTanggal = $('._js-close-modal-ganti-tanggal');
                $('body').on('click', '._js-btn-ganti-tanggal', function() {
                    $modalGantiTanggal.fadeIn('fast');
                    const $this = $(this);
                    const orderId = $this.data('order_id');
                    const createdAtHours = $this.data('created_at_hours');
                    const createdAtMinutes = $this.data('created_at_minutes');
                    const nextDate = $('._js-input-created_at').data('next_date');
                    const newCreatedAt = moment(nextDate).hours(createdAtHours).minutes(createdAtMinutes).format('YYYY-MM-DDTHH:mm');
                    // console.log('orderId', orderId);
                    // console.log('nextDate', nextDate);
                    // console.log('newCreatedAt', newCreatedAt);
                    $('._js-input-order-id').val(orderId);
                    $('._js-input-created_at').val(newCreatedAt);
                });
                $btnCloseModalGantiTanggal.on('click', function (){
                    $modalGantiTanggal.fadeOut('fast');
                    $('._js-input-order-id').val('');
                    // $('._js-input-created_at').val('');
                });
                $('._js-btn-submit-ganti-tanggal').on('click', function() {
                    const $form = $('._js-form-ganti-tanggal');
                    const inputs = new FormData($form[0]);
                    const orderId = inputs.get('order_id');

                    if (!inputs.get('created_at')) {
                        mdtoast('"Tanggal" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    // } else if (!inputs.get('photo').name) {
                    //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    } else {
                        if (confirm('Ganti tanggal job?')) {
                            mdtoast(
                                'LOADING...', {
                                    // type: mdtoast.INFO,
                                    interaction: true, 
                                    actionText: 'CLOSE',
                                    action: function(){ this.hide(); }
                                });
                            $.ajax({
                                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                                url: "<?php echo e(route('submit-ganti-tanggal-job')); ?>",
                                type: 'POST',
                                data: inputs,
                                processData: false,
                                contentType: false,
                                success: function(res) {
                                    console.log('res', res);
                                    console.log('orderId', orderId);
                                    if (res) {
                                        $modalGantiTanggal.fadeOut('fast');
                                        $('._js-input-order-id').val('');
                                        // $('._js-input-created_at').val('');
                                        $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                                        setTimeout(() => {
                                            $('._js-order-item-'+orderId).remove();
                                            mdtoast('JOB BERHASIL DIGANTI TANGGAL', { duration: 3000, type: mdtoast.SUCCESS });
                                            window.location.href = '<?php echo e(route("job-list", ["store_slug" => $store_slug, "status" => "total"])); ?>&date='+inputs.get('created_at').split('T')[0];
                                        }, 1000);
                                    } else {
                                        mdtoast('GANTI TANGGAL JOB GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                        $this.val(defaultValDriver[orderId]);
                                    }
                                },
                                error: function(error) {
                                    console.log('error', error);
                                    mdtoast('GANTI TANGGAL JOB GAGAL', { duration: 3000, type: mdtoast.ERROR });
                                },
                            });
                        }
                    }
                });

                // Job Confirm
                // ====================================================
                const defaultValBankConfirmed = {};
                $('._js-input-bank-confirmed').each(function() {
                    const orderId = $(this).data('order_id');
                    const bankId = this.value;
                    defaultValBankConfirmed[orderId] = bankId;
                });
                $('body').on('click', '._js-btn-confirm', function() {
                    const $this = $(this);
                    const orderId = $this.data('order_id');
                    const bankId = $('._js-input-bank-confirmed-'+orderId).val();
                    const amountPay = $('._js-input-amount-pay-'+orderId).val() ? $('._js-input-amount-pay-'+orderId).val().replace('Rp','').replaceAll('.','') : null;
                    const amountSplitToCash = $('._js-input-split-to-cash-'+orderId).val() ? $('._js-input-split-to-cash-'+orderId).val().replace('Rp','').replaceAll('.','') : null;
                    const confirmNote = $('._js-input-confirm-note-'+orderId).val() ? $('._js-input-confirm-note-'+orderId).val() : null;
                    if (confirm('Confirm job?')) {
                        mdtoast(
                        'LOADING...', {
                            // type: mdtoast.INFO,
                            interaction: true, 
                            actionText: 'CLOSE',
                            action: function(){ this.hide(); }
                        });
                        axios.post("<?php echo e(route('submit-confirm-job')); ?>", {
                            // params: {
                                order_id: orderId,
                                bank_id: bankId ? bankId : null,
                                amount_pay: amountPay ? amountPay : null,
                                amount_split_to_cash: amountSplitToCash ? amountSplitToCash : null,
                                confirm_note: confirmNote ? confirmNote : null,
                            // }
                        })
                        .then(function (res) {
                            console.log(res);
                            if (res && res.data) {
                                mdtoast('JOB BERHASIL TERKONFIRMASI', { duration: 1500, type: mdtoast.SUCCESS });
                                $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                                setTimeout(() => {
                                    $('._js-order-item-'+orderId).remove();
                                }, 1000);
                            } else {
                                mdtoast('JOB GAGAL TERKONFIRMASI', { duration: 1500, type: mdtoast.ERROR });
                                $this.val(defaultValDriver[orderId]);
                            }
                        })
                        .catch(function (error) {
                            console.log('error', error);
                            mdtoast('JOB GAGAL TERKONFIRMASI', { duration: 1500, type: mdtoast.ERROR });
                            $this.val(defaultValDriver[orderId]);
                        });
                    }
                });


                // Upload Payment Proof
                // ====================================================
                const $modalUploadPaymentproof = $('._js-modal-upload-paymentproof');

                const $btnCloseModalUploadPaymentproof = $('._js-close-modal-upload-paymentproof');

                $('body').on('click', '._js-btn-upload-paymentproof', function() {
                    $modalUploadPaymentproof.fadeIn('fast');
                    const $this = $(this);
                    const orderType = $this.data('order_type') ? $this.data('order_type') : '';
                    const orderId = $this.data('order_id');
                    $('._js-input-order-type').val(orderType);
                    $('._js-input-order-id').val(orderId);
                });
                $btnCloseModalUploadPaymentproof.on('click', function (){
                    $modalUploadPaymentproof.fadeOut('fast');
                    $('._js-input-order-type').val('');
                    $('._js-input-order-id').val('');
                    $('._js-btn-photo_paymentproof').show();
                    $("._js-image-preview_paymentproof").attr("src", '');
                    $('._js-input-photo_paymentproof').val('');
                    $('._js-wrp-preview_paymentproof').hide();
                });

                $('._js-input-photo_paymentproof').on('change', function() {
                    if (this.files && this.files[0]) {
                    // if (this.files[0].size > 3 * 1024 * 1024) {
                    //     // Check the constraint
                    //     this.setCustomValidity(
                    //     "The selected file must not be larger than 3 MB"
                    //     );
                    // } else {
                    //     this.setCustomValidity("");
                    // }
                        var reader = new FileReader();
                        reader.readAsDataURL(this.files[0]);
                        reader.onload = function (e) {
                            $("._js-image-preview_paymentproof").attr("src", e.target.result);
                            $('._js-btn-photo_paymentproof').hide();
                            $('._js-wrp-preview_paymentproof').show();
                        };
                    }
                });

                $('._js-btn-remove-photo_paymentproof').on('click', function() {
                    $('._js-btn-photo_paymentproof').show();
                    $("._js-image-preview_paymentproof").attr("src", '');
                    $('._js-input-photo_paymentproof').val('');
                    $('._js-wrp-preview_paymentproof').hide();
                });

                $('._js-btn-submit-upload-paymentproof').on('click', function() {
                    $('._js-input-received-latlng').val(latlng);
                    $('._js-input-received-latlng-accuracy').val(latlng_accuracy);
                    const $form = $('._js-form-upload-paymentproof');
                    const inputs = new FormData($form[0]);
                    const orderId = inputs.get('order_id');

                    if (!inputs.get('photo_paymentproof')) {
                        mdtoast('"Gambar masih kosong!', { duration: 2000, type: mdtoast.ERROR });
                    // } else if (!inputs.get('photo').name) {
                    //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    } else {
                        if (confirm('Upload gambar?')) {
                            mdtoast(
                                'LOADING...', {
                                    // type: mdtoast.INFO,
                                    interaction: true, 
                                    actionText: 'CLOSE',
                                    action: function(){ this.hide(); }
                                });
                            $.ajax({
                                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                                url: "<?php echo e(route('submit-upload-paymentproof')); ?>",
                                type: 'POST',
                                data: inputs,
                                processData: false,
                                contentType: false,
                                success: function(res) {
                                    console.log('res', res);
                                    console.log('orderId', orderId);
                                    if (res) {
                                        $modalUploadPaymentproof.fadeOut('fast');
                                        $('._js-input-order-type').val('');
                                        $('._js-input-order-id').val('');
                                        $('._js-btn-photo_paymentproof').show();
                                        $("._js-image-preview_paymentproof").attr("src", '');
                                        $('._js-input-photo_paymentproof').val('');
                                        $('._js-wrp-preview_paymentproof').hide();
                                        // $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                                        mdtoast('GAMBAR BERHASIL TERUPLOAD', { duration: 3000, type: mdtoast.SUCCESS });
                                        setTimeout(() => {
                                            // $('._js-order-item-'+orderId).remove();
                                            window.location.reload();
                                        }, 1500);
                                    } else {
                                        mdtoast('GAMBAR GAGAL DIUPLOAD', { duration: 3000, type: mdtoast.ERROR });
                                        if ($('._js-input-order-type').val() != 'invoice') {
                                            $this.val(defaultValDriver[orderId]);
                                        }
                                    }
                                },
                                error: function(error) {
                                    console.log('error', error);
                                    mdtoast('GAMBAR GAGAL DIUPLOAD', { duration: 3000, type: mdtoast.ERROR });
                                },
                            });
                        }
                    }
                });


                // Upload Foto Rumah
                // ====================================================
                const $modalUploadAddress = $('._js-modal-upload-address');
                
                const $btnCloseModalUploadAddress = $('._js-close-modal-upload-address');

                let photoRumahBlob = '';
                
                $('body').on('click', '._js-btn-upload-address', function() {
                    webcamRumah.start()
                        .then(result =>{
                            console.log("webcamRumah started");
                            webcamRumah.flip();
                            webcamRumah.start();
                        })
                        .catch(err => {
                            console.log(err);
                        });
                    $modalUploadAddress.fadeIn('fast');
                    const $this = $(this);
                    // const orderType = $this.data('order_type') ? $this.data('order_type') : '';
                    const orderId = $this.data('order_id');
                    // $('._js-input-order-type-upload-address').val(orderType);
                    $('._js-input-order-id-upload-address').val(orderId);
                });
                $btnCloseModalUploadAddress.on('click', function (){
                    $modalUploadAddress.fadeOut('fast');
                    // $('._js-input-order-type-upload-address').val('');
                    $('._js-input-order-id-upload-address').val('');
                    $('._js-btn-capture-camera-rumah').show();
                    $('._js-btn-flip-camera-rumah').show();
                    $("._js-img-capture-rumah").hide().attr("src", '');
                    $('._js-btn-remove-photo_address').hide();
                    photoRumahBlob = '';
                    webcamRumah.stop();
                });

                $('._js-btn-flip-camera-rumah').on('click', function() {
                    webcamRumah.flip();
                    webcamRumah.start();
                });
                $('._js-btn-capture-camera-rumah').on('click', function() {
                    // const localId = Math.floor(Math.random() * 100) + 1;
                    let photo = webcamRumah.snap();
                    $('._js-img-capture-rumah')
                        .prop("src",photo)
                        .show();
                    
                    $('._js-btn-capture-camera-rumah').hide();
                    $('._js-btn-flip-camera-rumah').hide();
                    $('._js-btn-remove-photo_address').show();

                    const base64ImageContent = photo.replace(/^data:image\/(png|jpg);base64,/, "");
                    const blob = base64ToBlob(base64ImageContent, 'image/png');
                    photoRumahBlob = blob;

                    // const formData = new FormData();
                    // formData.append('marketing_id', $('._js-input-marketing-id').val());
                    // formData.append('photo_marketing', blob);
                    // $.ajax({
                    //     headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                    //     url: "<?php echo e(route('upload-photo-marketing')); ?>",
                    //     type: 'POST',
                    //     cache: false,
                    //     contentType: false,
                    //     processData: false,
                    //     data: formData,
                    //     success: function(res) {
                    //         console.log('res', res);
                    //         $('#img_capture_'+localId).find('._js-loading-process').hide();
                    //         $('#img_capture_'+localId).find('._js-loading-done').show();
                    //         setTimeout(() => {
                    //             $('#img_capture_'+localId).find('._js-wrp-loading').hide();
                    //             // $('#img_capture_'+localId).find('._js-btn-delete-photo').prop('id', res.id).show();
                    //         }, 500);
                    //     },
                    //     error: function(error) {
                    //         console.log('error', error);
                    //     },
                    // });
                });

                // $('._js-input-photo_address').on('change', function() {
                //     if (this.files && this.files[0]) {
                //     // if (this.files[0].size > 3 * 1024 * 1024) {
                //     //     // Check the constraint
                //     //     this.setCustomValidity(
                //     //     "The selected file must not be larger than 3 MB"
                //     //     );
                //     // } else {
                //     //     this.setCustomValidity("");
                //     // }
                //         var reader = new FileReader();
                //         reader.readAsDataURL(this.files[0]);
                //         reader.onload = function (e) {
                //             $("._js-image-preview_address").attr("src", e.target.result);
                //             $('._js-btn-photo_address').hide();
                //             $('._js-wrp-preview_address').show();
                //         };
                //     }
                // });

                $('._js-btn-remove-photo_address').on('click', function() {
                    $('._js-btn-capture-camera-rumah').show();
                    $('._js-btn-flip-camera-rumah').show();
                    $("._js-img-capture-rumah").hide().attr("src", '');
                    $('._js-btn-remove-photo_address').hide();
                    photoRumahBlob = '';
                });

                // NEXT PR
                $('._js-btn-submit-upload-address').on('click', function() {
                    const $form = $('._js-form-upload-address');
                    const inputs = new FormData($form[0]);
                    const orderId = inputs.get('order_id');
                    inputs.append('photo_address', photoRumahBlob);

                    console.log('inputs.get("photo_address")', inputs.get('photo_address'));

                    if (inputs.get('photo_address') == '') {
                        mdtoast('"Gambar masih kosong!', { duration: 2000, type: mdtoast.ERROR });
                    // } else if (!inputs.get('photo').name) {
                    //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
                    } else {
                        if (confirm('Upload gambar?')) {
                            mdtoast(
                                'LOADING...', {
                                    // type: mdtoast.INFO,
                                    interaction: true, 
                                    actionText: 'CLOSE',
                                    action: function(){ this.hide(); }
                                });
                            $.ajax({
                                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                                url: "<?php echo e(route('submit-upload-address')); ?>",
                                type: 'POST',
                                data: inputs,
                                processData: false,
                                contentType: false,
                                success: function(res) {
                                    console.log('res', res);
                                    console.log('orderId', orderId);
                                    if (res) {
                                        $modalUploadAddress.fadeOut('fast');
                                        $('._js-input-order-type').val('');
                                        $('._js-input-order-id').val('');
                                        $('._js-btn-photo_address').show();
                                        $("._js-image-preview_address").attr("src", '');
                                        $('._js-input-photo_address').val('');
                                        $('._js-wrp-preview_address').hide();
                                        // $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                                        mdtoast('GAMBAR BERHASIL TERUPLOAD', { duration: 3000, type: mdtoast.SUCCESS });
                                        setTimeout(() => {
                                            // $('._js-order-item-'+orderId).remove();
                                            window.location.reload();
                                        }, 1500);
                                    } else {
                                        mdtoast('GAMBAR GAGAL DIUPLOAD', { duration: 3000, type: mdtoast.ERROR });
                                        if ($('._js-input-order-type').val() != 'invoice') {
                                            $this.val(defaultValDriver[orderId]);
                                        }
                                    }
                                },
                                error: function(error) {
                                    console.log('error', error);
                                    mdtoast('GAMBAR GAGAL DIUPLOAD', { duration: 3000, type: mdtoast.ERROR });
                                },
                            });
                        }
                    }
                });

            });
        </script>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/job-list.blade.php ENDPATH**/ ?>