<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <h2 class="text-lg font-bold">
            <?php echo e(__('Calendar')); ?> <select
                class="inline p-0 ml-1 text-lg font-bold bg-transparent border-none _js-input-store">
                <?php if(in_array(auth()->user()->role_id, [1,2,3,4,6])): ?> <option
                    value="<?php echo e(route('calendar', ['store_slug' => 'all', 'month' => $_GET['month'] ?? ''])); ?>" <?php echo e($store_slug=='all' ? "selected" : null); ?>>
                    All</option>
                <?php endif; ?>
                <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e(route('calendar', ['store_slug' => $item->slug, 'month' => $_GET['month'] ?? ''])); ?>"
                    <?php echo e($item->slug == $store_slug ?
                    "selected" : null); ?>>
                    <?php echo e($item->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </h2>
        
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('subheader', null, []); ?> 
        <div class="flex items-center justify-center h-10 mt-2 text-red-500">
            <?php if($store_slug == 'all'): ?>
            <a class="w-6 h-6" href="<?php echo e(route('calendar', ['store_slug' => $store_slug, 'date' => $date_prev])); ?>"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
                        clip-rule="evenodd" />
                </svg></a>
            <input type="date"
                class="w-56 p-0 mx-3 text-lg font-bold text-center border-none focus:ring-0 _js-input-date"
                max="<?php echo e($date_next); ?>" value="<?php echo e($date_now); ?>">
            <?php if($date_next): ?>
            <a class="w-6 h-6" href="<?php echo e(route('calendar', ['store_slug' => $store_slug, 'date' => $date_next])); ?>"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                        clip-rule="evenodd" />
                </svg></a>
            <?php endif; ?>
            <?php else: ?>
            <a class="w-6 h-6"
                href="<?php echo e(route('calendar', ['store_slug' => $store_slug, 'month' => $month_prev])); ?>"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
                        clip-rule="evenodd" />
                </svg></a>
            <input type="month"
                class="w-56 p-0 mx-3 text-lg font-bold text-center border-none focus:ring-0 _js-input-month"
                max="<?php echo e(date('Y-m')); ?>" value="<?php echo e($month_now); ?>">
            <?php if($month_next): ?>
            <a class="w-6 h-6"
                href="<?php echo e(route('calendar', ['store_slug' => $store_slug, 'month' => $month_next])); ?>"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                        clip-rule="evenodd" />
                </svg></a>
            <?php endif; ?>
            <?php endif; ?>
        </div>
        <?php if(!in_array(auth()->user()->role_id, [5])): ?>
        <div class="grid grid-cols-8 gap-0 text-xs font-bold text-center">
            <?php else: ?>
            <div class="grid grid-cols-6 gap-0 text-xs font-bold text-center">
                <?php endif; ?>
                <span class="grid-cols-2 py-2 text-gray-500 capitalize"><?php echo e($store_slug == 'all' ? 'Toko' :
                    __('order.date')); ?></span>
                <span class="grid-cols-1 py-2 text-black capitalize"><?php echo e(__('order.total')); ?></span>
                <?php if(!in_array(auth()->user()->role_id, [5])): ?>
                <span class="grid-cols-1 py-2 text-yellow-500 capitalize"><?php echo e(__('order.bebas')); ?></span>
                <?php endif; ?>
                <span class="py-2 text-blue-500 capitalize grid-cols-"><?php echo e(__('order.diambil')); ?></span>
                <span class="py-2 text-blue-700 capitalize grid-cols-"><?php echo e(__('order.terkirim')); ?></span>
                <span class="py-2 text-green-500 capitalize grid-cols-"><?php echo e(__('order.selesai')); ?></span>
                <?php if(in_array(auth()->user()->role_id, [5])): ?>
                <span class="py-2 text-pink-500 capitalize grid-cols-">B-Bro</span>
                <?php else: ?>
                <span class="py-2 text-red-500 capitalize grid-cols-"><?php echo e(__('order.overtime')); ?></span>
                <span class="py-2 text-red-700 capitalize grid-cols-"><?php echo e(__('order.batal')); ?></span>
                <?php endif; ?>
            </div>
            <?php if(!in_array(auth()->user()->role_id, [5])): ?>
            <div class="grid grid-cols-8 gap-0 text-xs font-bold text-center bg-red-50">
                <?php else: ?>
                <div class="grid grid-cols-6 gap-0 text-xs font-bold text-center">
                    <?php endif; ?>
                    <span class="grid-cols-2 py-2 text-gray-500 capitalize">TOTAL</span>
                    <span class="grid-cols-1 py-2 text-black capitalize"><?php echo e($sum_total_job); ?></span>
                    <?php if(!in_array(auth()->user()->role_id, [5])): ?>
                    <span class="grid-cols-1 py-2 text-yellow-500 capitalize"><?php echo e($sum_total_bebas); ?></span>
                    <?php endif; ?>
                    <span class="py-2 text-blue-500 capitalize grid-cols-"><?php echo e($sum_total_diambil); ?></span>
                    <span class="py-2 text-blue-700 capitalize grid-cols-"><?php echo e($sum_total_terkirim); ?></span>
                    <span class="py-2 text-green-500 capitalize grid-cols-"><?php echo e($sum_total_selesai); ?></span>
                    <?php if(in_array(auth()->user()->role_id, [5])): ?>
                    <span class="py-2 text-pink-500 capitalize grid-cols-"><?php echo e($sum_total_bbro); ?></span>
                    <?php else: ?>
                    <span class="py-2 text-red-500 capitalize grid-cols-"><?php echo e($sum_total_overtime); ?><?php if($store_slug !==
                        'all'): ?>~<?php echo e($sum_total_job > 0 ? round($sum_total_overtime/$sum_total_job*100) : 0); ?>%<?php endif; ?></span>
                    <span class="py-2 text-red-700 capitalize grid-cols-"><?php echo e($sum_total_batal); ?></span>
                    <?php endif; ?>
                </div>
     <?php $__env->endSlot(); ?>

    <div class="pb-20 pt-44">
        <?php if($store_slug == 'all'): ?>
        <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

        <a href="<?php echo e($item['url']); ?>"
            class="grid grid-cols-8 gap-0 text-sm font-bold text-center border-b border-gray-200 border-solid">
            <div class="flex items-center justify-center w-16 pl-1 leading-none text-gray-500 h-11"
                style="font-size: 11px;">
                <div class="w-full text-left break-words">
                    <?php echo e($item['store']); ?>

                </div>
            </div>
            <span class="flex items-center justify-center grid-cols-1 text-black"><?php echo e($item['total']); ?></span>
            <span class="flex items-center justify-center grid-cols-1 text-yellow-500"><?php echo e($item['bebas']); ?></span>
            <span class="flex items-center justify-center grid-cols-1 text-blue-500"><?php echo e($item['diambil']); ?></span>
            <span class="flex items-center justify-center grid-cols-1 text-blue-700"><?php echo e($item['terkirim']); ?></span>
            <span class="flex items-center justify-center grid-cols-1 text-green-500"><?php echo e($item['selesai']); ?></span>
            <span class="flex items-center justify-center grid-cols-1 text-red-500"><?php echo e($item['overtime']); ?></span>
            <span class="flex items-center justify-center grid-cols-1 text-red-700"><?php echo e($item['batal']); ?></span>
        </a>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
        <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(!in_array(auth()->user()->role_id, [5])): ?>
        <a href="<?php echo e($item['url']); ?>"
            class="grid grid-cols-8 gap-0 font-bold text-sm text-center border-solid border-gray-200 border-b <?php echo e($month_now == date('Y-m') && $item['date'] == date('d') ? 'bg-yellow-100' : null); ?>">
            <?php else: ?>
            <a href="<?php echo e($item['url']); ?>"
                class="grid grid-cols-6 gap-0 font-bold text-sm text-center border-solid border-gray-200 border-b <?php echo e($i == 0 ? 'bg-yellow-100' : null); ?>">
                <?php endif; ?>
                <span class="flex flex-col items-center justify-center grid-cols-2 leading-none text-gray-500 h-11"><?php echo e($item['date']); ?><span class="mt-0 text-xs font-light"><?php echo e($item['day']); ?></span></span>
                <span class="flex items-center justify-center grid-cols-1 text-black"><?php echo e($item['total']); ?></span>
                <?php if(!in_array(auth()->user()->role_id, [5])): ?>
                <span class="flex items-center justify-center grid-cols-1 text-yellow-500"><?php echo e($item['bebas']); ?></span>
                <?php endif; ?>
                <span class="flex items-center justify-center grid-cols-1 text-blue-500"><?php echo e($item['diambil']); ?></span>
                <span class="flex items-center justify-center grid-cols-1 text-blue-700"><?php echo e($item['terkirim']); ?></span>
                <span class="flex items-center justify-center grid-cols-1 text-green-500"><?php echo e($item['selesai']); ?></span>
                <?php if(in_array(auth()->user()->role_id, [5])): ?>
                <span class="flex items-center justify-center grid-cols-1 text-pink-500"><?php echo e($item['bbro']); ?><small
                        class="opacity-50">/<?php echo e($store->marketing_each_day); ?></small></span>
                <?php else: ?>
                <span class="flex items-center justify-center grid-cols-1 text-red-500"><?php echo e($item['overtime']); ?></span>
                <span class="flex items-center justify-center grid-cols-1 text-red-700"><?php echo e($item['batal']); ?></span>
                <?php endif; ?>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
    </div>

     <?php $__env->slot('js', null, []); ?> 
        <script>
            $( document ).ready(function() {
                $('._js-input-date').change(function() {
                    const url = "<?php echo e(url()->current()); ?>";
                    const date = this.value;
                    // console.log(url+'?date='+date);
                    window.location.replace(url+'?date='+date);
                });
                $('._js-input-month').change(function() {
                    const url = "<?php echo e(url()->current()); ?>";
                    const month = this.value;
                    // console.log(url+'?month='+month);
                    window.location.replace(url+'?month='+month);
                });
                $('._js-input-store').change(function() {
                    const url = this.value;
                    window.location.replace(url);
                });
            });
        </script>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/calendar.blade.php ENDPATH**/ ?>