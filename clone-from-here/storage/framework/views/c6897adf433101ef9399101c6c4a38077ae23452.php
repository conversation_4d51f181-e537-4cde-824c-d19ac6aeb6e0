<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Laravel')); ?></title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap">

    <!-- Styles -->
    <link rel="stylesheet" href="<?php echo e(url('/css/mdtoast.min.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(url('/css/animate.min.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(url('/css/selectize.bootstrap3.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(mix('css/app.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(mix('css/manager.css')); ?>">

    <!-- Matomo -->
    <script>
        var _paq = window._paq = window._paq || [];
    /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
    _paq.push(['trackPageView']);
    _paq.push(['enableLinkTracking']);
    (function() {
      var u="//stat.ordergasplus.online/";
      _paq.push(['setTrackerUrl', u+'matomo.php']);
      _paq.push(['setSiteId', '2']);
      var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
      g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
    })();
    </script>
    <!-- End Matomo Code -->

    <!-- Hotjar Tracking Code for Gasplus CS -->
    <script>
        (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:6373432,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>



    <?php $config = (new \LaravelPWA\Services\ManifestService)->generate(); echo $__env->make( 'laravelpwa::meta' , ['config' => $config])->render(); ?>
</head>

<body class="font-sans antialiased bg-gray-300">
    <div id="app">
        <!-- Page Heading -->
        <header class="fixed z-30 top-0 left-0 right-0">
            <div class="max-w-xl mx-auto shadow bg-white">
                <div class="bg-red-700 h-14 text-white px-4 flex items-center relative">
                    <?php echo $__env->yieldContent('header'); ?>
                </div>
                <?php echo $__env->yieldContent('subheader'); ?>
            </div>
        </header>

        <!-- Page Content -->
        <main class="max-w-xl bg-white min-h-screen mx-auto relative">
            <?php echo $__env->yieldContent('content'); ?>
        </main>

        <!-- Navigation -->
        <nav class="h-16 fixed z-30 bottom-0 left-0 right-0">
            <div
                class="min-h-full max-w-xl mx-auto flex items-center justify-around shadow bg-white border-solid border-t-2 border-gray-300">
                <a href="<?php echo e(route('calendar', ['store_slug' => 'palagan'])); ?>"
                    class="<?php echo e(Route::currentRouteName() == 'calendar' ? 'text-red-600' : 'text-gray-600'); ?> flex flex-col items-center justify-center">
                    <?php if(Route::currentRouteName() == 'calendar'): ?>
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                            clip-rule="evenodd" />
                    </svg>
                    <?php else: ?>
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <?php endif; ?>
                    <span class="text-xs mt-1 font-bold">Calendar</span>
                </a>
                <a href="#" class="text-red-600 flex flex-col items-center justify-center">
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                            clip-rule="evenodd" />
                        <path
                            d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                    </svg>
                    <span class="text-xs mt-1 font-bold">Jobs</span>
                </a>
                
                <a href="<?php echo e(route('account', ['store_slug' => 'palagan'])); ?>"
                    class="<?php echo e(in_array(Route::currentRouteName(), ['account']) ? 'text-red-600' : 'text-gray-600'); ?> flex flex-col items-center justify-center">
                    <?php if(in_array(Route::currentRouteName(), ['account'])): ?>
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                            clip-rule="evenodd" />
                    </svg>
                    <?php else: ?>
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <?php endif; ?>
                    <span class="text-xs mt-1 font-bold"><?php echo e(Auth::user() ? Auth::user()->name : ''); ?></span>
                </a>
            </div>
        </nav>
    </div>

    <script src="<?php echo e(url('/js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(url('/js/moment.min.js')); ?>"></script>
    
    <script src="<?php echo e(url('/js/mdtoast.min.js')); ?>"></script>
    <script src="<?php echo e(url('/js/axios.min.js')); ?>"></script>
    <script src="<?php echo e(url('/js/selectize.min.js')); ?>"></script>
    
    <!-- Scripts -->
    <script src="<?php echo e(mix('js/app.js')); ?>" defer></script>
    <?php echo $__env->yieldContent('js'); ?>
</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/layouts/offline.blade.php ENDPATH**/ ?>