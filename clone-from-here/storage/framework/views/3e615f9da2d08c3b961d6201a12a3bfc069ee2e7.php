<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>IAK Pricelist</title>
    
    
    
    
    <link rel="stylesheet" href="<?php echo e(mix('css/app.css')); ?>">

    <!-- Matomo -->
    <script>
        var _paq = window._paq = window._paq || [];
    /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
    _paq.push(['trackPageView']);
    _paq.push(['enableLinkTracking']);
    (function() {
      var u="//stat.ordergasplus.online/";
      _paq.push(['setTrackerUrl', u+'matomo.php']);
      _paq.push(['setSiteId', '2']);
      var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
      g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
    })();
    </script>
    <!-- End Matomo Code -->

    <!-- Hotjar Tracking Code for Gasplus CS -->
    <script>
        (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:6373432,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>

</head>

<body class="antialiased">
    <div class="px-5 py-4 flex flex-col gap-5 h-screen">
        <h1 class="text-2xl font-bold">IAK Pricelist / <?php echo e($ppob_pricelist->updated_at); ?></h1>
        <p class="text-gray-500 text-lg font-bold"><?php echo e($product->name); ?></p>
        <div class="flex flex-1 overflow-auto border-b border-gray-200">
            <table class="w-full border text-xs">
                <thead>
                    <tr class="divide-x">
                        <?php $__currentLoopData = $pricelist[0]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $title => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <th class="sticky top-0 px-1.5 py-2 text-black bg-gray-300"><?php echo e(ucwords(str_replace('_', ' ',
                            $title))); ?></th>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                </thead>
                <tbody class="divide-y divide-x">
                    <?php $__currentLoopData = $pricelist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="divide-x">
                        <?php $__currentLoopData = $item; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td class="px-1.5 py-2 text-left"><?php echo e($value); ?></td>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        
    </div>
</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/sharp/product-ppob-iak-pricelist.blade.php ENDPATH**/ ?>