<?php
if ($order->received_at) {
$start_date = new DateTime($order->created_at);
$since_start = $start_date->diff(new DateTime($order->received_at));
// echo $since_start->days.' days total<br>';
// echo $since_start->y.' years<br>';
// echo $since_start->m.' months<br>';
// echo $since_start->d.' days<br>';
// echo $since_start->h.' hours<br>';
// echo $since_start->i.' minutes<br>';
// echo $since_start->s.' seconds<br>';
}
?>

<div
  class="px-4 py-4 border-solid border-gray-300 border-b-2 relative animate__animated _js-order-item-<?php echo e($order->id); ?>">
  <a target="_blank" href="<?php echo e(auth()->user()->role_id <= 3 ? URL::to(" /")."/cs/form/order/".$order->id : ''); ?>"
    class="absolute inset-0 <?php echo e(auth()->user()->role_id <= 3 ? '' : 'pointer-events-none'); ?>"></a>
  <div class="relative pointer-events-none index-10">
    <h4 class="text-lg font-bold"><?php echo e($order->code); ?><span class="ml-1 text-xs text-gray-400">🕐
        <?php echo e(date('H:i', strtotime($order->created_at))); ?></span>
      <?php if($order->received_at): ?>
      <?php if($since_start->h > 0 || $since_start->i > 60): ?>
      <span class="ml-1 text-xs text-red-500">🔴
        <?php echo e($since_start->h > 0 ? $since_start->h.':' : ''); ?><?php echo e($since_start->i); ?></span>
      <?php else: ?>
      <span class="ml-1 text-xs text-green-500">🟢 <?php echo e($since_start->i); ?></span>
      <?php endif; ?>
      <?php endif; ?>
      <?php if($order->distance_store_customer): ?>
      <span class="ml-1 text-xs text-gray-500"><?php echo e($order->distance_store_customer); ?></span>
      <?php endif; ?>
      <?php if($order->distance_customer_received): ?>
      <span class="text-xs text-gray-800">/ <?php echo e($order->distance_customer_received); ?></span>
      <?php endif; ?>
    </h4>
    <p
      class="_js-status-item-<?php echo e($order->id); ?> px-2 py-0.5 mb-1 text-xs font-bold rounded inline-block text-white uppercase <?php echo e(__('order.status.color.'.$order->status->code)); ?>">
      JOB <span class="_js-status-text-<?php echo e($order->id); ?>"><?php echo e(__('order.status.label.'.$order->status->code)); ?></span></p>
    <?php if($order->status->code == 'canceled'): ?>

    <?php endif; ?>
    <?php if($order->driver): ?>
    <p
      class="_js-driver-item-<?php echo e($order->id); ?> px-2 py-0.5 mb-1 ml-1 text-xs bg-blue-500 font-bold rounded inline-block text-white">
      🛵
      <?php echo e($order->driver->name); ?></p>
    <?php endif; ?>
    <?php if($order->status_id == 5 && $order->driver_note): ?>
    <p class="font-bold text-blue-500">⚠️ <?php echo e($order->driver_note); ?> <?php if($order->cancelby): ?>
      <span class="ml-1 text-sm text-gray-300">(by <?php echo e($order->cancelby->name); ?>)</span>
      <?php endif; ?>
    </p>
    <?php endif; ?>
    <p class="">👤 <?php echo e($order->customer->name); ?></p>
    <a href="https://wa.me/<?php echo e($order->customer->phone); ?>" target="_blank"
      class="relative text-red-600 underline pointer-events-auto">📲 <?php echo e($order->customer->phone); ?></a>
    <p class="">🏠 <?php echo e($order->address ? $order->address->address : ""); ?></p>
    <?php if($order->address && $order->address->latlng): ?>
    <a href="http://www.google.com/maps/place/<?php echo e($order->address ? $order->address->latlng : ""); ?>" target="_blank"
      class="relative text-red-600 underline pointer-events-auto">📍
      <?php echo e($order->address ? $order->address->latlng : ""); ?></a>
    <?php endif; ?>
    <?php $__currentLoopData = $order->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <p class="font-bold">🟢 <?php echo e($product->code.' '.$product->name); ?> (<?php echo e($product->pivot->qty); ?>x)</p>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php if($order->note): ?>
    <p class="font-bold text-blue-500">📝 <?php echo e($order->note); ?></p>
    <?php endif; ?>
    <hr class="my-2">
    <p class="">💰 <span class="font-bold">TOTAL:</span> Rp<?php echo e(number_format($order->total, 0, '', '.')); ?>

      <?php if($order->payment): ?>
      <span class="font-bold uppercase">(<?php echo e($order->payment); ?>)</span>
      <?php endif; ?>
    </p>
    <?php if($order->amount_will_pay): ?>
    <p class="">💵 <span class="font-bold">BAYAR:</span> Rp<?php echo e(number_format($order->amount_will_pay, 0, '', '.')); ?></p>
    <?php endif; ?>
    <p class="text-yellow-500">💸 <span class="font-bold">KEMBALI:</span>
      Rp<?php echo e(number_format(999999999, 0, '', '.')); ?></p>

    <?php if($date_now == date('Y-m-d')): ?>
    
    <?php if((auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) && $order->status_id <= 3): ?> <label
        class="block mt-3">
        
        <select data-order_id="<?php echo e($order->id); ?>"
          class="relative block w-full mt-1 border-gray-300 rounded-md shadow-sm pointer-events-auto _js-input-driver focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50">
          <option value="">Pilih driver..</option>
          <?php $__currentLoopData = $drivers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($driver->id); ?>" <?php echo e($order->driver_id == $driver->id ? 'selected="selected' : ''); ?>>
            <?php echo e($driver->name); ?></option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        </label>
        <?php endif; ?>
        
        <?php if((auth()->user()->role_id <= 3 || in_array(auth()->user()->role_id, [5, 6])) && $order->status_id <= 3): ?> <div
            class="flex justify-around mt-3 _js-wrp-action">
            <button type="button" data-order_id="<?php echo e($order->id); ?>"
              class="relative flex-1 px-2 py-2 mr-2 font-bold text-white bg-green-500 rounded shadow-lg pointer-events-auto _js-btn-selesai">SELESAI</button>
            <button type="button" data-order_id="<?php echo e($order->id); ?>"
              class="relative flex-1 px-2 py-2 ml-2 font-bold text-white bg-red-500 rounded shadow-lg pointer-events-auto _js-btn-batal">BATAL</button>
  </div>
  <?php endif; ?>
  <?php endif; ?>
</div>
<span class="absolute index-10 top-1 right-1.5 opacity-40 text-xs font-bold text-gray-400"><?php echo e($order->author ?
  $order->author->name : 'PWA'); ?></span>
</div><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/components/order-item.blade.php ENDPATH**/ ?>