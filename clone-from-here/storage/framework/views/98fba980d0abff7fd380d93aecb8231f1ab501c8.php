<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?php echo e($invoice->code); ?></title>
</head>

<body class="font-sans antialiase">

  
  <div style="">
    <img src="<?php echo e(asset('public/images/logotype.png')); ?>" height="50" />
    <div style="float: right; text-align: right;">
      <h1 style="margin: 0; font-size: 1.5rem;">INVOICE</h1>
      <p style="color: red; margin: 0;"><?php echo e($invoice->code); ?></p>
    </div>
  </div>
  <div style="clear: both;"></div>

  
  <div style="margin-top: 1.5rem; line-height: 1.375; font-size: .875rem;">
    <div style="width: 45%; float: left;">
      <table style="width: 100%;">
        <tr>
          <td>
            <h4 style="margin: 0;">TOKO</h4>
          </td>
        </tr>
        <tr>
          <td><strong><?php echo e($invoice->store->name); ?></strong> (<?php echo e($invoice->store->whatsapp_1); ?>)</td>
        </tr>
        <tr>
          <td><?php echo e($invoice->store->address); ?></td>
        </tr>
      </table>
    </div>
    <div style="width: 45%; float: right;">
      <table style="width: 100%;">
        <tr>
          <td colspan="2">
            <h4 style="margin: 0;">UNTUK</h4>
          </td>
        </tr>
        <tr style="vertical-align: top;">
          <td>Pembeli</td>
          <td>:</td>
          <td><strong><?php echo e($invoice->customer->name); ?></strong> (<?php echo e($invoice->customer->phone); ?>)</td>
        </tr>
        <tr style="vertical-align: top;">
          <td>Tanggal</td>
          <td>:</td>
          <td style="font-weight: bold;"><?php echo e($invoice->created_at->format('d-m-Y')); ?></td>
        </tr>
        <tr style="vertical-align: top;">
          <td>Alamat</td>
          <td>:</td>
          <td><?php echo e($invoice->customer->addresses[0]->address); ?></td>
        </tr>
      </table>
    </div>
  </div>
  <div style="clear: both;"></div>

  
  <?php
  $total_qty = 0;
  ?>
  <div style="margin-top: 1.5rem; line-height: 1.375; font-size: .875rem;">
    <table style="width: 100%;">
      <thead>
        <tr>
          <th style="text-align: left;">INFO PRODUK</th>
          <th style="text-align: right;">HARGA</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colspan="2" style="height: 0.5rem;"></td>
        </tr>
        <?php $__currentLoopData = $invoice->orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
          <td style="border-top: 1px solid gray; padding-top: 0.5rem;" colspan="2"><strong><?php echo e($order->code); ?></strong>
            <span style="color: gray; text-align: right;"> / <?php echo e($order->created_at->format('d-m-Y')); ?></span>
          </td>
        </tr>
        <?php $__currentLoopData = $order->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
          <td><?php echo e($product->code); ?> - <?php echo e($product->name); ?> @Rp<?php echo e(number_format($product->pivot->price, 0, '', '.')); ?>x<?php echo e($product->pivot->qty); ?></td>
          <td style="text-align: right;">Rp<?php echo e(number_format($product->pivot->price * $product->pivot->qty, 0, '', '.')); ?></td>
        </tr>
        <?php
        $total_qty += $product->pivot->qty;
        ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <tr>
          <td colspan="2" style="height: 0.5rem;"></td>
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <tr>
          <td colspan="2" style="height: 0.5rem; border-top: 1px solid gray;"></td>
        </tr>
      </tbody>
    </table>
    <div style="width: 50%; float: left; position: relative;">
      <table style="width: 100%; font-size: .875rem; line-height: 1.375;">
        <tr>
          <td>PEMBAYARAN TRANSFER KE:</td>
        </tr>
        <?php $__currentLoopData = $invoice->store->banks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bank): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(strtolower($bank->bank_name) != 'deposit' && strtolower($bank->bank_name) != 'qris'): ?>
        <tr>
          <td><?php echo e($bank->bank_name); ?> - <strong><?php echo e($bank->account_number); ?></strong> a.n. <?php echo e($bank->holder_name); ?></td>
        </tr>
        <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </table>
      <div style="margin-top: 1.5rem; position: relative">
        <img src="<?php echo e(asset('public/images/ttd.jpeg')); ?>" height="70" />
        <img style="position: absolute; top: 1px; left: 1px; opacity: 0.5;"
          src="<?php echo e(asset('public/images/logotype.png')); ?>" height="30" />
        <p style="margin-top: -1rem;">Dewi Kristanti (Finance Dept.)</p>
      </div>
    </div>
    <table style="width: 50%; float: right;">
      <tr>
        <td style="">TOTAL (<?php echo e($total_qty); ?> BARANG)</td>
        <td style="text-align: right;">Rp<?php echo e(number_format($invoice->total_bill, 0, '', '.')); ?></td>
      </tr>
      <tr>
        <td style="">POTONGAN</td>
        <td style="text-align: right;">- Rp<?php echo e(number_format($invoice->discount, 0, '', '.')); ?></td>
      </tr>
      <tr>
        <td colspan="2" style="height: 0.5rem;"></td>
      </tr>
      <tr>
        <td style="border-top: 1px solid gray; padding-top: 0.5rem;"><strong>TOTAL TAGIHAN</strong></td>
        <td style="text-align: right; border-top: 1px solid gray; padding-top: 0.5rem;"><strong>Rp<?php echo e(number_format($invoice->total_after_discount, 0, '', '.')); ?></strong>
        </td>
      </tr>
    </table>

  </div>

</body>

</html><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/pdf/invoice.blade.php ENDPATH**/ ?>