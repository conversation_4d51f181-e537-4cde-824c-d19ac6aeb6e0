<?php if (isset($component)) { $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\AppLayout::class, []); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('store_slug', null, []); ?> 
        <?php echo e($store_slug); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('head', null, []); ?> 
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.8.0/dist/leaflet.css"
            integrity="sha512-hoalWLoI8r4UszCkZ5kL8vayOGVae1oxXe/2A4AO6J9+580uKHDO3JdHb7NzwwzK5xr/Fs0W40kiNHxM9vyTtQ=="
            crossorigin="" />
        <script src="https://unpkg.com/leaflet@1.8.0/dist/leaflet.js"
            integrity="sha512-BB3hKbKWOc9Ez/TAwyWxNXeoV9c1v6FIeYiBieIWkpLjauysF18NzgR1MBNBXf8/KABdlkX68nAhlwcDFLGPCQ=="
            crossorigin=""></script>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('header', null, []); ?> 
        <a class="flex items-center -ml-3"
            href="<?php echo e(route('jobs', ['store_slug' => $store_slug, 'date' => $date_now])); ?>"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                
                <span class="capitalize"><?php echo e($store_slug); ?></span>
            </h2>
        </a>
     <?php $__env->endSlot(); ?>

    <div class="pb-20 pt-28">
        <div
            class="fixed top-0 left-0 right-0 z-auto flex items-center justify-center h-screen text-5xl font-bold text-gray-400">
            Notif Send ✅
        </div>
    </div>

     <?php $__env->slot('js', null, []); ?> 
        <script type="module">
            <?php
            function Get($index, $defaultValue = '') {
                return isset($_GET[$index]) ? $_GET[$index] : $defaultValue;
            }
            ?>
            const msg = `<?php echo urldecode(Get("msg")) ?>`;
            const to = '<?php echo Get("to") ?>';
            if (msg && to) {
                console.log("🚀 ~ msg:", msg)
                console.log("🚀 ~ to:", to)
                const url = `https://wa.me/${to}?text=${encodeURIComponent(msg.replaceAll('<br>', '\n'))}`;
                // window.open(url);
                console.log("🚀 ~ url:", url)
                // setTimeout(() => {
                    window.location.href = url;
                // }, 500);
                // history.pushState({}, null, url);
            }
        </script>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da)): ?>
<?php $component = $__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da; ?>
<?php unset($__componentOriginal8e2ce59650f81721f93fef32250174d77c3531da); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Sites/gp/gasplus-cs/resources/views/manager/wame.blade.php ENDPATH**/ ?>