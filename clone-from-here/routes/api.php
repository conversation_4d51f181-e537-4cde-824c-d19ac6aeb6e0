<?php

// use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\IakController;
// use App\Http\Controllers\SystemController;
use App\Http\Controllers\SystemController;
use App\Http\Controllers\WebhookController;
// use App\Http\Controllers\CustomerController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });

// API V1
// ========================================

Route::get('/citiesstores', [ApiController::class, 'getCitiesStores']);
Route::get('/city/{slugstore}', [ApiController::class, 'getCityStore']);
Route::get('/stores', [ApiController::class, 'getStores']);
Route::get('/storesproducts', [ApiController::class, 'getStoresProducts']);
Route::get('/store/{slugstore}', [ApiController::class, 'getStore']);
Route::get('/categories', [ApiController::class, 'getCategories']);
Route::get('/categories/{slugstore}', [ApiController::class, 'getStoreCategories']);
Route::get('/products', [ApiController::class, 'getProducts']);
Route::get('/products/{slugstore}', [ApiController::class, 'getStoreProducts']);
Route::get('/product/{slugstore}/{slugproduct}', [ApiController::class, 'getProduct']);
Route::get('/customer/byid/{id}', [ApiController::class, 'getCustomerById']);
Route::get('/customer/byphone/{phone}', [ApiController::class, 'getCustomerByPhone']);
Route::get('/customer/upsert', [ApiController::class, 'upsertCustomer']);
Route::get('/customer/update/notif', [ApiController::class, 'updateCustomerNotif']);
// Route::get('/order/new', [ApiController::class, 'createOrder']);
// Route::get('/sync/elora', [ApiController::class, 'syncElora']);
Route::get('/customerorder', [ApiController::class, 'upsertCustomerAddressOrder']);

Route::get('/deposit/{customer_id}', [ApiController::class, 'generateDepositHistory']);
Route::get('/lastorder/{customer_id}', [ApiController::class, 'getLastOrder']);

Route::get('/customerstore/{phone}', [ApiController::class, 'customerStore']);

Route::get('/forward-message', [ApiController::class, 'forwardMessage']);


// Route::get('/test/sendmail', [ApiController::class, 'testSendMail']);
// Route::get('/test/sendnotifwa', [ApiController::class, 'testSendNotifWa']);
// Route::get('/test', [ApiController::class, 'test']);
// Route::post('/test', [ApiController::class, 'testPost']);

// Route::any('/test-webhook', [SystemController::class, 'webhook']);
Route::any('/test-callback/{name}', [SystemController::class, 'testCallback']);
Route::any('/test-webhook-socialchat', [SystemController::class, 'testWebhookSocialchat']);
Route::post('/webhook/acc', [WebhookController::class, 'webhookAcc']);
Route::post('/iak/callback', [IakController::class, 'callback']);
