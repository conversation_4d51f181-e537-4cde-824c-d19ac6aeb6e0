<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CalendarController extends Controller
{
    /**
     * Display the calendar page with dummy data.
     */
    public function index(Request $request): Response
    {
        // Generate realistic dummy data for the calendar
        $currentDate = now();
        $currentMonth = $currentDate->format('Y-m');
        $prevMonth = $currentDate->copy()->subMonth()->format('Y-m');
        $nextMonth = $currentDate->copy()->addMonth()->format('Y-m');
        
        // Sample stores data
        $stores = [
            ['id' => 1, 'slug' => 'store-1', 'name' => 'Store Jakarta Pusat'],
            ['id' => 2, 'slug' => 'store-2', 'name' => 'Store Jakarta Selatan'],
            ['id' => 3, 'slug' => 'store-3', 'name' => 'Store Jakarta Utara'],
            ['id' => 4, 'slug' => 'store-4', 'name' => 'Store Bandung'],
            ['id' => 5, 'slug' => 'store-5', 'name' => 'Store Surabaya'],
        ];
        
        // Generate calendar data for current month
        $data = [];
        $daysInMonth = $currentDate->daysInMonth;
        $sumTotals = [
            'total_job' => 0,
            'total_bebas' => 0,
            'total_diambil' => 0,
            'total_terkirim' => 0,
            'total_selesai' => 0,
            'total_batal' => 0,
            'total_overtime' => 0,
            'total_bbro' => 0,
        ];
        
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $date = $currentDate->copy()->day($day);
            $dayName = $date->format('D');
            
            // Generate random but realistic data
            $total = rand(15, 45);
            $bebas = rand(2, 8);
            $diambil = rand(5, 15);
            $terkirim = rand(8, 20);
            $selesai = rand(10, 25);
            $batal = rand(0, 3);
            $overtime = rand(0, 5);
            $bbro = rand(3, 12);
            
            $data[] = [
                'date' => str_pad($day, 2, '0', STR_PAD_LEFT),
                'day' => $dayName,
                'url' => route('calendar') . '?date=' . $date->format('Y-m-d'),
                'total' => $total,
                'bebas' => $bebas,
                'diambil' => $diambil,
                'terkirim' => $terkirim,
                'selesai' => $selesai,
                'batal' => $batal,
                'overtime' => $overtime,
                'bbro' => $bbro,
            ];
            
            // Add to totals
            $sumTotals['total_job'] += $total;
            $sumTotals['total_bebas'] += $bebas;
            $sumTotals['total_diambil'] += $diambil;
            $sumTotals['total_terkirim'] += $terkirim;
            $sumTotals['total_selesai'] += $selesai;
            $sumTotals['total_batal'] += $batal;
            $sumTotals['total_overtime'] += $overtime;
            $sumTotals['total_bbro'] += $bbro;
        }
        
        // Sample user data
        $user = [
            'id' => 1,
            'role_id' => 1, // Admin role
            'email' => '<EMAIL>',
            'name' => 'Admin User'
        ];
        
        return Inertia::render('Calendar', [
            'store_slug' => 'all',
            'stores' => $stores,
            'month_now' => $currentMonth,
            'month_prev' => $prevMonth,
            'month_next' => $nextMonth,
            'date_now' => $currentDate->format('Y-m-d'),
            'date_prev' => $currentDate->copy()->subDay()->format('Y-m-d'),
            'date_next' => $currentDate->copy()->addDay()->format('Y-m-d'),
            'data' => $data,
            'sum_total_job' => $sumTotals['total_job'],
            'sum_total_bebas' => $sumTotals['total_bebas'],
            'sum_total_diambil' => $sumTotals['total_diambil'],
            'sum_total_terkirim' => $sumTotals['total_terkirim'],
            'sum_total_selesai' => $sumTotals['total_selesai'],
            'sum_total_batal' => $sumTotals['total_batal'],
            'sum_total_overtime' => $sumTotals['total_overtime'],
            'sum_total_bbro' => $sumTotals['total_bbro'],
            'user' => $user,
        ]);
    }
}
